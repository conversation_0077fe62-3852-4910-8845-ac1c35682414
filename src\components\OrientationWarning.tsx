import { useEffect, useState } from 'react';
import { RotateCcw } from 'lucide-react';

export default function OrientationWarning() {
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    // Function to check orientation
    const checkOrientation = () => {
      // Only show warning on mobile devices
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      if (isMobile) {
        const isPortrait = window.matchMedia("(orientation: portrait)").matches;
        setShowWarning(isPortrait);
      } else {
        setShowWarning(false);
      }
    };

    // Check orientation on mount
    checkOrientation();

    // Add event listener for orientation changes
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);

    // Clean up
    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);

  if (!showWarning) return null;

  return (
    <div className="fixed inset-0 bg-black/90 z-[9999] flex flex-col items-center justify-center p-6 text-center">
      <RotateCcw className="w-16 h-16 text-[#E1C760] mb-4 animate-spin" />
      <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Please Rotate Your Device</h2>
      <p className="text-white mb-6">
        Thunee is designed to be played in landscape orientation for the best experience.
        Please rotate your device to continue.
      </p>
      <div className="w-64 h-32 border-2 border-[#E1C760] rounded-lg relative mb-8">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-12 h-20 border-2 border-white/50 rounded-md rotate-90 relative">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-1 bg-white/50 rounded"></div>
          </div>
        </div>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-[#E1C760] text-sm">
          Rotate to Landscape
        </div>
      </div>
    </div>
  );
}
