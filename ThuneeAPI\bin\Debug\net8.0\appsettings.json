{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Information"}}, "AllowedHosts": "*", "Urls": "http://localhost:5000;https://localhost:5001", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ThuneeDB;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "ThuneeAPI", "Audience": "ThuneeClient", "ExpiryInDays": 7}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001", "http://localhost:5173", "http://127.0.0.1:5173"]}, "SignalR": {"KeepAliveInterval": "00:00:15", "ClientTimeoutInterval": "00:01:00", "HandshakeTimeout": "00:00:30", "MaximumReceiveMessageSize": 104857600}, "Game": {"DefaultTimeframeOptions": [3, 4, 5, 6, 60], "VotingTimeLimit": 15, "TrumpDisplayDuration": 10, "CardDealingSpeed": 300, "TimerUpdateInterval": 100, "ThuneeCallingDurations": {"Trumper": 5, "FirstRemaining": 3, "LastRemaining": 2}}}