import { useState } from 'react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { AlertCircle, CheckCircle, Settings } from 'lucide-react';
import gameService from '../services/gameService';

const EnvironmentIndicator = () => {
  const [showDetails, setShowDetails] = useState(false);
  const config = gameService.getConfig();

  const isDevelopment = config.environment === 'development';
  const isConnected = gameService.isConnected();

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="flex items-center gap-2">
        {/* Environment Badge */}
        <Badge 
          variant={isDevelopment ? "default" : "secondary"}
          className={`${isDevelopment ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white`}
        >
          {config.environment.toUpperCase()}
        </Badge>

        {/* Connection Status */}
        <Badge 
          variant={isConnected ? "default" : "destructive"}
          className="flex items-center gap-1"
        >
          {isConnected ? (
            <>
              <CheckCircle className="w-3 h-3" />
              Connected
            </>
          ) : (
            <>
              <AlertCircle className="w-3 h-3" />
              Disconnected
            </>
          )}
        </Badge>

        {/* Service Type Badge */}
        <Badge variant="outline" className="bg-white/90">
          {gameService.getServiceType()}
        </Badge>

        {/* Details Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="bg-white/90 hover:bg-white"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>

      {/* Details Panel */}
      {showDetails && (
        <div className="absolute top-12 right-0 bg-white border rounded-lg shadow-lg p-4 min-w-80 z-50">
          <h3 className="font-semibold mb-3 text-gray-900">Environment Configuration</h3>
          
          <div className="space-y-2 text-sm">
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">Environment:</span>
              <span className="font-mono">{config.environment}</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">Service:</span>
              <span className="font-mono">{gameService.getServiceType()}</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">Game Hub:</span>
              <span className="font-mono text-xs break-all">{config.gameHubUrl}</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">Video Hub:</span>
              <span className="font-mono text-xs break-all">{config.videoHubUrl}</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">API Base:</span>
              <span className="font-mono text-xs break-all">{config.apiBaseUrl}</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">Debug Mode:</span>
              <span className="font-mono">{config.debugMode ? 'ON' : 'OFF'}</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <span className="text-gray-600">Connection:</span>
              <span className={`font-mono ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>

          {isDevelopment && (
            <div className="mt-4 pt-3 border-t">
              <p className="text-xs text-gray-500 mb-2">Development Tools:</p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('Game Service Config:', config);
                    console.log('Underlying Service:', gameService.getUnderlyingService());
                  }}
                  className="text-xs"
                >
                  Log Config
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => gameService.forceSignalR()}
                  className="text-xs"
                >
                  Force SignalR
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnvironmentIndicator;
