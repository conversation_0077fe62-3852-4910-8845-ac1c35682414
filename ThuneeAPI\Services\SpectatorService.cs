using System.Collections.Concurrent;
using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class SpectatorService : ISpectatorService
    {
        private readonly ILobbyService _lobbyService;
        private readonly ConcurrentDictionary<string, string> _spectatorToGameLobby = new();

        public SpectatorService(ILobbyService lobbyService)
        {
            _lobbyService = lobbyService;
        }

        public Task<SpectatorJoinResponse> JoinAsSpectatorAsync(string connectionId, string gameCode, string spectatorName)
        {
            // Try to find the game lobby
            var gameLobby = _lobbyService.GetGameLobby(gameCode);
            if (gameLobby == null)
            {
                // Try to find in regular lobbies
                var lobby = _lobbyService.GetLobby(gameCode);
                if (lobby == null || !lobby.GameStarted)
                {
                    throw new InvalidOperationException("Game not found or not started");
                }

                // Convert lobby to game lobby format
                gameLobby = new GameLobby
                {
                    LobbyCode = lobby.LobbyCode,
                    Players = lobby.Players,
                    Teams = lobby.Teams,
                    TeamNames = lobby.TeamNames,
                    GameStarted = lobby.GameStarted,
                    GameState = lobby.GameState,
                    DealerId = lobby.DealerId,
                    TrumpSelectorId = lobby.TrumpSelectorId,
                    TrumpSuit = lobby.TrumpSuit,
                    PlayerCards = lobby.PlayerCards,
                    Spectators = new List<Spectator>(),
                    OriginalLobbies = new List<string> { lobby.LobbyCode }
                };
            }

            // Add spectator to the game
            var spectator = new Spectator
            {
                Id = connectionId,
                Name = spectatorName,
                JoinedAt = DateTime.UtcNow
            };

            gameLobby.Spectators.Add(spectator);
            _spectatorToGameLobby[connectionId] = gameCode;

            Console.WriteLine($"Spectator {spectatorName} joined game {gameCode}");

            return Task.FromResult(new SpectatorJoinResponse
            {
                GameLobby = gameLobby
            });
        }

        public Task<bool> RemoveSpectatorAsync(string connectionId)
        {
            if (!_spectatorToGameLobby.TryGetValue(connectionId, out var gameCode))
            {
                return Task.FromResult(false);
            }

            var gameLobby = _lobbyService.GetGameLobby(gameCode);
            if (gameLobby != null)
            {
                var spectator = gameLobby.Spectators.FirstOrDefault(s => s.Id == connectionId);
                if (spectator != null)
                {
                    gameLobby.Spectators.Remove(spectator);
                    Console.WriteLine($"Spectator {spectator.Name} left game {gameCode}");
                }
            }

            _spectatorToGameLobby.TryRemove(connectionId, out _);
            return Task.FromResult(true);
        }

        public Task<AvailableGamesResponse> GetAvailableGamesAsync()
        {
            var availableGames = new List<AvailableGame>();

            // Get all active game lobbies
            // Note: This would need to be implemented in the lobby service to iterate through game lobbies
            // For now, we'll return an empty list as a placeholder

            return Task.FromResult(new AvailableGamesResponse
            {
                Games = availableGames
            });
        }

        public Task SendGameStateToSpectatorAsync(string connectionId, string gameCode)
        {
            var gameLobby = _lobbyService.GetGameLobby(gameCode);
            if (gameLobby == null)
            {
                return Task.CompletedTask;
            }

            // Send current game state to the spectator
            // This would involve sending various game events to bring the spectator up to speed
            // Implementation would depend on the specific game state that needs to be synchronized

            return Task.CompletedTask; // Placeholder
        }
    }
}
