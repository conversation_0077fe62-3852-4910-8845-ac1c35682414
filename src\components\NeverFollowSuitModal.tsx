"use client";
import { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useGameStore } from "@/store/gameStore";
import gameService from "@/services/gameService";

type NeverFollowSuitModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function NeverFollowSuitModal({
  isOpen,
  onClose
}: NeverFollowSuitModalProps) {
  const { players, currentPlayerId, teamNames } = useGameStore();
  const [selectedPlayer, setSelectedPlayer] = useState<string | null>(null);
  const [selectedHand, setSelectedHand] = useState<number | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Get the current player
  const currentPlayer = players.find(p => p.id === currentPlayerId);

  // Log debugging information
  useEffect(() => {
    if (isOpen) {
      console.log("NeverFollowSuitModal opened");
      console.log("Current player ID:", currentPlayerId);
      console.log("Current player:", currentPlayer);
      console.log("All players:", players);

      // If currentPlayerId is not set, try to get it from gameService
      if (!currentPlayerId) {
        const socketId = gameService.getSocketId();
        if (socketId) {
          console.log("Getting socket ID as fallback:", socketId);
          // Update the game store with the current player ID
          useGameStore.getState().updateGameState({ currentPlayerId: socketId });
        }
      }
    }
  }, [isOpen, currentPlayerId, currentPlayer, players]);

  // Get the opposing team players (players not on the current player's team)
  // If currentPlayer is not available, try to determine teams another way
  const opposingTeamPlayers = useMemo(() => {
    // If we have a current player, filter by their team
    if (currentPlayer) {
      console.log(`Filtering opposing team players for team ${currentPlayer.team}`);
      return players.filter(p => p.team !== currentPlayer.team);
    }

    // If we don't have a current player but have players with teams
    if (players.length > 0 && players.some(p => p.team)) {
      // Try to determine the current player's team from the socket ID
      const socketId = gameService.getSocketId();
      const playerFromSocket = players.find(p => p.id === socketId);

      if (playerFromSocket) {
        console.log(`Found player from socket ID with team ${playerFromSocket.team}`);
        return players.filter(p => p.team !== playerFromSocket.team);
      }

      // If we still can't determine the team, just show all players from team 2
      // (assuming current player is on team 1)
      console.log("Fallback: showing all players from team 2");
      return players.filter(p => p.team === 2);
    }

    // If all else fails, return all players
    console.log("Fallback: showing all players");
    return players;
  }, [currentPlayer, players]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedPlayer(null);
      setSelectedHand(null);
      setErrorMessage(null);
      setIsProcessing(false);
    }
  }, [isOpen]);

  // Generate hand numbers for selection (1-6)
  const handNumbers = Array.from({ length: 6 }, (_, i) => i + 1);

  const handleSubmit = async () => {
    if (!selectedPlayer || !selectedHand) {
      setErrorMessage("Please select both a player and a hand number");
      return;
    }

    try {
      setIsProcessing(true);
      setErrorMessage(null);

      // Send the 4-ball action to the server
      await gameService.sendGameAction("four_eight_ball_selection", {
        ballType: "4 ball",
        option: "Never follow suit",
        accusedPlayerId: selectedPlayer,
        handNumber: selectedHand
      });

      // Close the modal after successful submission
      onClose();
    } catch (error) {
      console.error("Error submitting 4-ball claim:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to submit claim");
      setIsProcessing(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div
            className="absolute inset-0 bg-black/50"
            onClick={onClose}
          />
          <Card className="relative w-full bg-black border-2 border-[#E1C760] p-6 z-10">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-[#E1C760]">4-Ball: Never Follow Suit</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-[#E1C760] hover:text-white hover:bg-gray-800"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <p className="text-gray-400 text-center mb-4 text-sm italic">
              Select a player from the opposing team who didn't follow suit when they should have.
              Also select the hand number where this occurred.
            </p>

            <div className="space-y-4">
              <div>
                <h3 className="text-[#E1C760] font-semibold mb-2">Select Player</h3>
                <div className="grid grid-cols-2 gap-2">
                  {opposingTeamPlayers.map((player) => (
                    <Button
                      key={player.id}
                      onClick={() => setSelectedPlayer(player.id)}
                      className={`border ${
                        selectedPlayer === player.id
                          ? "bg-[#E1C760] text-black"
                          : "bg-black text-[#E1C760] border-[#E1C760]"
                      }`}
                    >
                      {player.name}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-[#E1C760] font-semibold mb-2">Select Hand Number</h3>
                <div className="grid grid-cols-3 gap-2">
                  {handNumbers.map((handNumber) => (
                    <Button
                      key={handNumber}
                      onClick={() => setSelectedHand(handNumber)}
                      className={`border ${
                        selectedHand === handNumber
                          ? "bg-[#E1C760] text-black"
                          : "bg-black text-[#E1C760] border-[#E1C760]"
                      }`}
                    >
                      Hand #{handNumber}
                    </Button>
                  ))}
                </div>
              </div>

              {errorMessage && (
                <div className="bg-red-900/30 border border-red-500 rounded-md p-2 text-red-300 text-sm">
                  {errorMessage}
                </div>
              )}

              <div className="flex justify-center mt-4">
                <Button
                  onClick={handleSubmit}
                  disabled={!selectedPlayer || !selectedHand || isProcessing}
                  className="bg-[#E1C760] text-black hover:bg-[#c9b052] disabled:bg-gray-700 disabled:text-gray-400"
                >
                  {isProcessing ? "Submitting..." : "Submit 4-Ball Claim"}
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
