using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Models;
using ThuneeAPI.Services;

namespace ThuneeAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PlayerController : ControllerBase
    {
        private readonly ICompetitionService _competitionService;
        private readonly IAuthService _authService;
        private readonly ILogger<PlayerController> _logger;

        public PlayerController(ICompetitionService competitionService, IAuthService authService, ILogger<PlayerController> logger)
        {
            _competitionService = competitionService;
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Get all registered players
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<UserDto>>> GetPlayers()
        {
            try
            {
                var players = await _competitionService.GetPlayersAsync();
                return Ok(players);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving players");
                return StatusCode(500, "An error occurred while retrieving players");
            }
        }

        /// <summary>
        /// Get detailed information about a specific player
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<PlayerDetailsDto>> GetPlayerDetails(int id)
        {
            try
            {
                var playerDetails = await _competitionService.GetPlayerDetailsAsync(id);
                if (playerDetails == null)
                {
                    return NotFound($"Player with ID {id} not found");
                }
                return Ok(playerDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving player details for {PlayerId}", id);
                return StatusCode(500, "An error occurred while retrieving player details");
            }
        }

        /// <summary>
        /// Get game history for a specific player
        /// </summary>
        [HttpGet("{id}/game-history")]
        public async Task<ActionResult<GameHistoryDto>> GetPlayerGameHistory(
            int id,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 10;

                var gameHistory = await _competitionService.GetPlayerGameHistoryAsync(id, page, pageSize);
                return Ok(gameHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving game history for player {PlayerId}", id);
                return StatusCode(500, "An error occurred while retrieving game history");
            }
        }

        /// <summary>
        /// Get current player's details (requires authentication)
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<PlayerDetailsDto>> GetMyDetails()
        {
            try
            {
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized();
                }

                var playerDetails = await _competitionService.GetPlayerDetailsAsync(userId);
                if (playerDetails == null)
                {
                    return NotFound("Player details not found");
                }
                
                return Ok(playerDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current player details");
                return StatusCode(500, "An error occurred while retrieving player details");
            }
        }

        /// <summary>
        /// Get current player's game history (requires authentication)
        /// </summary>
        [HttpGet("me/game-history")]
        [Authorize]
        public async Task<ActionResult<GameHistoryDto>> GetMyGameHistory(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized();
                }

                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 10;

                var gameHistory = await _competitionService.GetPlayerGameHistoryAsync(userId, page, pageSize);
                return Ok(gameHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current player's game history");
                return StatusCode(500, "An error occurred while retrieving game history");
            }
        }
    }
}
