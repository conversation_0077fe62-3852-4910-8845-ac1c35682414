"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Competition } from "@/types/leaderboard";
import { leaderboardService } from "@/services/leaderboardService";
import { ArrowLeft, Trophy, Calendar } from "lucide-react";
import BurgerMenu from "@/components/BurgerMenu";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";

export default function Competitions() {
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCompetitions = async () => {
      try {
        const data = await leaderboardService.getCompetitions();
        setCompetitions(data);
      } catch (error) {
        console.error("Error fetching competitions:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompetitions();
  }, []);

  const getStatusColor = (status: Competition["status"]) => {
    switch (status) {
      case "active":
        return "text-green-500";
      case "upcoming":
        return "text-blue-500";
      case "completed":
        return "text-gray-400";
      default:
        return "text-white";
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };

  return (
    <div className="min-h-screen bg-dark text-white flex flex-col relative">
      {/* BurgerMenu */}
      <BurgerMenu />

      {/* Main Content */}
      <div className="flex flex-col items-center px-6 pb-16 space-y-6 mt-16 overflow-y-auto h-[calc(100vh-4rem)] z-10 relative">
        {/* Header */}
        <div className="w-full flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
            onClick={() => navigate("/")}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-[#E1C760]">Competitions</h1>
          <div className="w-[60px]"></div> {/* Spacer for alignment */}
        </div>

        {/* Competitions List */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E1C760]"></div>
          </div>
        ) : (
          <div className="w-full space-y-4">
            {competitions.map((competition) => (
              <div
                key={competition.id}
                className="bg-[#1A1A1A] border border-[#333333] rounded-lg p-4 hover:border-[#E1C760] transition-colors cursor-pointer"
                onClick={() => navigate(`/competitions/${competition.id}`)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h2 className="text-xl font-semibold text-[#E1C760]">
                    {competition.name}
                  </h2>
                  <span
                    className={`text-sm px-2 py-1 rounded-full border ${getStatusColor(
                      competition.status
                    )}`}
                  >
                    {competition.status.charAt(0).toUpperCase() +
                      competition.status.slice(1)}
                  </span>
                </div>

                <div className="flex items-center text-sm text-gray-400 mb-3">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formatDate(competition.startDate)} - {formatDate(competition.endDate)}
                </div>

                <p className="text-sm text-gray-300 mb-4">
                  {competition.description || "No description available."}
                </p>

                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                    <Trophy className="h-4 w-4 mr-1 text-yellow-500" />
                    <span className="text-sm">1st: {competition.prizes.first}</span>
                  </div>
                  <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                    <Trophy className="h-4 w-4 mr-1 text-gray-400" />
                    <span className="text-sm">2nd: {competition.prizes.second}</span>
                  </div>
                  <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                    <Trophy className="h-4 w-4 mr-1 text-amber-700" />
                    <span className="text-sm">3rd: {competition.prizes.third}</span>
                  </div>
                </div>
              </div>
            ))}

            {competitions.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-400">No competitions available at the moment.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
