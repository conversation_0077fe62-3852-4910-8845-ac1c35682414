/* Responsive styles for Thunee game with focus on mobile landscape orientation */

/* Base styles for all screen sizes */
:root {
  --card-width-xs: 60px;
  --card-width-sm: 80px;
  --card-width-md: 100px;
  --card-width-lg: 120px;
  --header-height: 70px;
  --footer-height: 50px;
  --game-controls-height: 40px;
}

/* Small landscape phones */
@media (max-width: 640px) and (orientation: landscape) {
  :root {
    --card-width-xs: 50px;
    --card-width-sm: 60px;
    --card-width-md: 70px;
    --card-width-lg: 80px;
    --header-height: 60px;
    --footer-height: 40px;
    --game-controls-height: 36px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 60px !important;
  }
  
  /* Adjust player info section */
  .player-info-section {
    padding: 4px !important;
  }
  
  .player-avatar {
    width: 24px !important;
    height: 24px !important;
  }
  
  /* Adjust card sizing */
  .player-card {
    transform: scale(0.85);
  }
  
  /* Adjust played cards area */
  .played-cards-area {
    transform: scale(0.85);
  }
  
  /* Make bottom navigation more compact */
  .bottom-nav {
    height: 40px !important;
  }
  
  /* Adjust game controls */
  .game-controls {
    transform: scale(0.9);
    right: 5px !important;
  }
}

/* Medium landscape phones and small tablets */
@media (min-width: 641px) and (max-width: 768px) and (orientation: landscape) {
  :root {
    --card-width-xs: 55px;
    --card-width-sm: 70px;
    --card-width-md: 80px;
    --card-width-lg: 90px;
    --header-height: 65px;
    --footer-height: 45px;
    --game-controls-height: 38px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 65px !important;
  }
  
  /* Adjust player info section */
  .player-info-section {
    padding: 6px !important;
  }
  
  .player-avatar {
    width: 28px !important;
    height: 28px !important;
  }
  
  /* Adjust card sizing */
  .player-card {
    transform: scale(0.9);
  }
  
  /* Adjust played cards area */
  .played-cards-area {
    transform: scale(0.9);
  }
}

/* Large landscape phones and tablets */
@media (min-width: 769px) and (max-width: 1024px) and (orientation: landscape) {
  :root {
    --card-width-xs: 60px;
    --card-width-sm: 75px;
    --card-width-md: 90px;
    --card-width-lg: 100px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 70px !important;
  }
}

/* Specific height-based adjustments for very short screens */
@media (max-height: 450px) and (orientation: landscape) {
  :root {
    --card-width-xs: 45px;
    --card-width-sm: 55px;
    --card-width-md: 65px;
    --card-width-lg: 75px;
    --header-height: 50px;
    --footer-height: 36px;
    --game-controls-height: 32px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 50px !important;
    grid-template-rows: 40% 60% !important;
  }
  
  /* Adjust player info section */
  .player-info-section {
    padding: 2px !important;
    font-size: 0.75rem !important;
  }
  
  .player-avatar {
    width: 20px !important;
    height: 20px !important;
  }
  
  /* Adjust card sizing */
  .player-card {
    transform: scale(0.8);
  }
  
  /* Adjust played cards area */
  .played-cards-area {
    transform: scale(0.8);
  }
  
  /* Make bottom navigation more compact */
  .bottom-nav {
    height: 36px !important;
  }
  
  /* Adjust game controls */
  .game-controls {
    transform: scale(0.85);
    bottom: 40px !important;
  }
}

/* Utility classes for responsive design */
.landscape-hidden {
  display: none !important;
}

@media (orientation: landscape) {
  .landscape-hidden {
    display: none !important;
  }
  
  .landscape-visible {
    display: block !important;
  }
  
  .landscape-flex {
    display: flex !important;
  }
}

/* Fix for iOS Safari bottom bar */
@supports (-webkit-touch-callout: none) {
  .main-content {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }
  
  .bottom-nav {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }
}
