interface BetPanelProps {
  stake?: number;
  payout?: number;
  onClose?: () => void;
}

export function BetPanel({ stake = 100, payout = 90, onClose }: BetPanelProps) {
  return (
    <div className="bg-[#262626] rounded-xl p-3 border border-[#a07a4a] w-full">
      <div className="flex gap-2 h-full"> {/* Reduced gap from 4 to 2 */}
        {/* Left section (60%) */}
        <div className="w-[60%] flex flex-col justify-between"> {/* Changed from flex-[2] to w-[60%] */}
          <div className="space-y-3"> {/* Reduced space-y-4 to space-y-3 */}
            <div className="flex flex-col items-start w-full">
              <div className="flex w-full justify-between items-center">
                <h2 className="text-white text-lg font-semibold">
                  Winner of Next Hand
                </h2>
                <span className="text-white">9/10</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2"> {/* Reduced gap-4 to gap-2 */}
              <div className="space-y-1">
                <p className="text-[#646464] text-sm">Stake</p>
                <div className="bg-[#2d2d2d] rounded-md p-2">
                  <p className="text-white text-sm">R{stake.toFixed(2)}</p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-[#646464] text-sm">Payout</p>
                <div className="bg-[#2d2d2d] rounded-md p-2">
                  <p className="text-white text-sm">R{payout.toFixed(2)}</p>
                </div>
              </div>
            </div>

            <div className="flex gap-1 overflow-x-auto">
              {["R50", "R100", "R200", "R500", "X2"].map((value) => (
                <button
                  key={value}
                  className="bg-gradient-to-b from-[#a07a4a] via-[#edcf5d] to-[#7a5a00] px-2 py-1 rounded-md text-white text-sm font-medium hover:from-[#d4b972] hover:to-[#a08c57] transition-colors whitespace-nowrap"
                >
                  {value}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Right section (40%) */}
        <div className="w-[40%] mt-3"> {/* Changed from flex-1 to w-[40%] */}
          <div className="flex flex-col h-full gap-2">
            <button className="w-full bg-gradient-to-b from-[#a07a4a] via-[#edcf5d] to-[#7a5a00] text-white px-4 py-1.5 rounded-md text-sm font-semibold hover:from-[#d4b972] hover:to-[#a08c57] transition-colors border border-[#a07a4a]">
              Raise
            </button>
            <button className="w-full bg-[#208801] text-white px-4 py-1.5 rounded-md text-sm font-semibold hover:bg-[#2a9801] transition-colors border border-[#a07a4a]">
              Accept
            </button>
            <button
              className="w-full bg-[#880101] text-white px-4 py-1.5 rounded-md text-sm font-semibold hover:bg-[#980101] transition-colors border border-[#a07a4a]"
              onClick={onClose}
            >
              Decline
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
