// Custom event dispatcher to bridge socket events to DOM events
// This allows our components to listen for game events using standard DOM event listeners

export const dispatchCardDealtEvent = (cardData: any) => {
  // Create a custom event with the card data
  const event = new CustomEvent('card_dealt', { 
    detail: cardData 
  });
  
  // Dispatch the event on the window object
  window.dispatchEvent(event);
};

export const dispatchCardsDealingCompleteEvent = () => {
  // Create a custom event for when all cards have been dealt
  const event = new CustomEvent('cards_dealing_complete');
  
  // Dispatch the event on the window object
  window.dispatchEvent(event);
};

// Add more event dispatchers as needed
