/* StandardBallResultsDisplay.css */

.ball-results-container {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.9);
  overflow: auto;
}

.ball-results-card-wrapper {
  width: 100%;
  max-width: 48rem; /* 768px */
  padding: 0.5rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
}

.ball-results-card {
  background-color: black;
  border: 2px solid #E1C760;
  border-radius: 0.5rem;
  padding: 1.5rem;
  width: 100%;
  color: white;
  overflow: auto;
  max-height: 90vh;
}

.ball-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.ball-results-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #E1C760;
}

.ball-results-timer {
  background-color: #E1C760;
  color: black;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: bold;
}

.ball-results-section {
  margin-bottom: 1.5rem;
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 1rem;
}

.ball-results-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #E1C760;
  margin-bottom: 0.75rem;
}

.ball-results-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.ball-results-value {
  font-weight: bold;
}

.ball-results-value-highlight {
  color: #E1C760;
  font-size: 1.25rem;
}

.ball-results-divider {
  border-top: 1px solid #374151;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
}

.ball-results-hands-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.ball-results-hand-card {
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.ball-results-hand-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.ball-results-hand-winner {
  color: #E1C760;
}

.ball-results-cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.ball-results-card-image {
  width: 3rem;
  height: 4rem;
  position: relative;
}

.ball-results-card-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.ball-results-final {
  background-color: rgba(225, 199, 96, 0.2);
  border: 2px solid #E1C760;
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 1rem;
}

.ball-results-final-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #E1C760;
  margin-bottom: 0.75rem;
}

.ball-results-final-result {
  font-size: 1.875rem;
  font-weight: bold;
}

.ball-results-final-description {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  opacity: 0.8;
}

.ball-results-continue-button {
  background-color: #E1C760;
  color: black;
  font-weight: bold;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.25rem;
  transition: all 0.2s;
  cursor: pointer;
}

.ball-results-continue-button:hover {
  background-color: rgba(225, 199, 96, 0.8);
}

/* Media queries for responsive design */
@media (min-width: 768px) {
  .ball-results-hands-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .ball-results-card {
    padding: 2rem;
  }
  
  .ball-results-title {
    font-size: 2rem;
  }
}

/* Mobile landscape orientation */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .ball-results-card-wrapper {
    padding: 0.25rem;
    align-items: flex-start;
  }
  
  .ball-results-card {
    padding: 0.75rem;
    max-height: 85vh;
    font-size: 0.875rem;
  }
  
  .ball-results-title {
    font-size: 1.25rem;
  }
  
  .ball-results-section {
    padding: 0.5rem;
    margin-bottom: 0.75rem;
  }
  
  .ball-results-section-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .ball-results-final {
    padding: 0.75rem;
  }
  
  .ball-results-final-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .ball-results-final-result {
    font-size: 1.25rem;
  }
  
  .ball-results-continue-button {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
  
  .ball-results-hands-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
  
  .ball-results-card-image {
    width: 2rem;
    height: 3rem;
  }
}

/* Mobile portrait orientation */
@media (max-width: 480px) {
  .ball-results-card {
    padding: 1rem;
  }
  
  .ball-results-title {
    font-size: 1.25rem;
  }
  
  .ball-results-section {
    padding: 0.75rem;
  }
  
  .ball-results-final {
    padding: 1rem;
  }
  
  .ball-results-final-result {
    font-size: 1.5rem;
  }
  
  .ball-results-continue-button {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
  }
}
