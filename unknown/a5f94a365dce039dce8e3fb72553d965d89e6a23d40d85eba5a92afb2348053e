"use client";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { useGameStore } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";

interface BallsCardsProps {
  isVisible?: boolean;
}

export default function BallsCards({ isVisible = true }: BallsCardsProps) {
  const { isDealer } = useGameStore();

  // Only show for the dealer/host
  if (!isVisible || !isDealer) {
    return null;
  }

  return (
    <div className="fixed top-24 left-1/2 transform -translate-x-1/2 z-40">
      <h2 className="text-[#E1C760] text-xl font-bold mb-2 text-center">Balls Cards</h2>
      <div className="relative w-[120px] h-[220px]">
        {/* Stack of all 4 cards */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="relative w-[100px] h-[140px] mx-auto"
        >
          <div className="relative">
            {/* Bottom layer: Diamonds 6 card */}
            <Card className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-white border-2 border-[#E1C760] rounded-md overflow-hidden shadow-lg">
              <img
                src={getCardImagePath("6", "diamonds")}
                alt="6 of Diamonds"
                className="w-full h-full object-contain"
              />
            </Card>

            {/* Second layer: Spades 6 card */}
            <Card className="absolute top-2 left-0 w-full h-full flex items-center justify-center bg-white border-2 border-[#E1C760] rounded-md overflow-hidden shadow-lg">
              <img
                src={getCardImagePath("6", "spades")}
                alt="6 of Spades"
                className="w-full h-full object-contain"
              />
            </Card>

            {/* Third layer: Hearts 6 card - on top of diamonds/spades */}
            <Card className="absolute top-4 left-0 w-full h-full flex items-center justify-center bg-white border-2 border-[#E1C760] rounded-md overflow-hidden shadow-lg z-10">
              <img
                src={getCardImagePath("6", "hearts")}
                alt="6 of Hearts"
                className="w-full h-full object-contain"
              />
            </Card>

            {/* Top layer: Clubs 6 card - on top of hearts */}
            <Card className="absolute top-6 left-0 w-full h-full flex items-center justify-center bg-white border-2 border-[#E1C760] rounded-md overflow-hidden shadow-lg z-20">
              <img
                src={getCardImagePath("6", "clubs")}
                alt="6 of Clubs"
                className="w-full h-full object-contain"
              />
            </Card>
          </div>
        </motion.div>

        {/* Label */}
        <div className="absolute bottom-0 left-0 right-0 text-center">
          <span className="text-[#E1C760] font-bold text-lg">Balls Cards</span>
        </div>
      </div>
    </div>
  );
}
