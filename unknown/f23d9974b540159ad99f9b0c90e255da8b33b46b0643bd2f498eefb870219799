.card {
  position: absolute;
  width: 179px;
  height: 250px;
  perspective: 1000px;
  transform-style: preserve-3d;
  will-change: transform;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.card.flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.card-front img,
.card-back img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.card-back {
  transform: rotateY(180deg);
}

@media (max-width: 1000px)  {
  .card {
    position: absolute;
    width: 120px;
    height: 160px;
    perspective: 1000px;
    transform-style: preserve-3d;
    will-change: transform;
  }
}