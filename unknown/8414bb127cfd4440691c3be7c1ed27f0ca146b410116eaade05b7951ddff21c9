import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Define the user type
export interface User {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
}

// Define the authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  tempRegistrationData: {
    username: string;
    email: string;
    password: string;
    otpSent: boolean;
  } | null;
}

// Define the authentication actions
export interface AuthActions {
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  verifyOtp: (otp: string) => Promise<void>;
  resendOtp: () => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  tempRegistrationData: null,
};

// Create the store with persistence
export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Login action
      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // For now, just simulate a successful login
          // In a real app, this would validate credentials with a backend
          set({
            user: {
              id: '1',
              username,
              email: `${username}@example.com`,
              isVerified: true,
            },
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to login',
            isLoading: false,
          });
          throw error;
        }
      },

      // Register action
      register: async (username: string, email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Store registration data temporarily and set OTP sent flag
          set({
            tempRegistrationData: {
              username,
              email,
              password,
              otpSent: true,
            },
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to register',
            isLoading: false,
          });
          throw error;
        }
      },

      // Verify OTP action
      verifyOtp: async (otp: string) => {
        const { tempRegistrationData } = get();
        if (!tempRegistrationData) {
          throw new Error('No registration in progress');
        }

        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // For now, just accept any OTP (in a real app, this would validate with a backend)
          // Create the user after successful OTP verification
          set({
            user: {
              id: '1',
              username: tempRegistrationData.username,
              email: tempRegistrationData.email,
              isVerified: true,
            },
            isAuthenticated: true,
            tempRegistrationData: null,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to verify OTP',
            isLoading: false,
          });
          throw error;
        }
      },

      // Resend OTP action
      resendOtp: async () => {
        const { tempRegistrationData } = get();
        if (!tempRegistrationData) {
          throw new Error('No registration in progress');
        }

        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Just update the OTP sent flag
          set({
            tempRegistrationData: {
              ...tempRegistrationData,
              otpSent: true,
            },
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to resend OTP',
            isLoading: false,
          });
          throw error;
        }
      },

      // Logout action
      logout: () => {
        set(initialState);
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'thunee-auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
