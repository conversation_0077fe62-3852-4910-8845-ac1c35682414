@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap");
@import url("./styles/responsive.css");

@custom-variant dark (&:is(.dark *));

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes fadeInSlide {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Inter", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --tbl-bg-ocean: linear-gradient(180deg, #01162E 5%, #2D75B9 50.55%, #01162E 95%);
    --tbl-bg-dark:linear-gradient(0deg, #000 0%, #373737 100%);
    --tbl-bg-Green:linear-gradient(90deg, #052800 0%, #0F7501 49.08%, #052800 99.15%);
    --component-bg-grey: var(--Glass, linear-gradient(103deg, rgba(144, 144, 144, 0.50) 0.46%, rgba(0, 0, 0, 0.50) 31.78%, rgba(144, 144, 144, 0.50) 99.04%));
    --gold-border-gradient: linear-gradient(103.12deg, rgba(144, 144, 144, 0.5), rgba(0, 0, 0, 0.5) 31.77%, rgba(144, 144, 144, 0.5));
    --text-bg-gold1: linear-gradient(180deg, #A07A4A 0%, #F4DB7A 33%, #EDCF5D 61%, #945D25 100%);
    --btn-bg-gold2: linear-gradient(180deg, #A07A4A 0%, #EDCF5D 44.5%, #7A5A00 100%);
    --black-glass: linear-gradient(120deg, #535353 0.46%, #1C1B1B 16.96%, #000 66.67%, #535353 85.48%, #1C1B1B 99.04%);
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
