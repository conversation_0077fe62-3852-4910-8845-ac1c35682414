"use client";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLef<PERSON>, Clock, Timer, Settings, RotateCcw, Plus, Minus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useTimeSettingsStore } from "@/store/timeSettingsStore";
import { toast } from "sonner";

export default function TimeSettings() {
  const navigate = useNavigate();
  const { settings, updateSettings, resetToDefaults, updatePlayTimeframeOptions, updateThuneeCallingDuration } = useTimeSettingsStore();

  // Local state for editing
  const [localSettings, setLocalSettings] = useState(settings);
  const [newTimeframeOption, setNewTimeframeOption] = useState("");

  const handleSave = () => {
    updateSettings(localSettings);
    toast.success("Time settings saved successfully!");
  };

  const handleReset = () => {
    resetToDefaults();
    setLocalSettings(useTimeSettingsStore.getState().settings);
    toast.success("Settings reset to defaults!");
  };

  const handleAddTimeframeOption = () => {
    const value = parseInt(newTimeframeOption);
    if (value && value > 0 && value <= 3600 && !localSettings.playTimeframeOptions.includes(value)) {
      const newOptions = [...localSettings.playTimeframeOptions, value].sort((a, b) => a - b);
      setLocalSettings(prev => ({
        ...prev,
        playTimeframeOptions: newOptions
      }));
      setNewTimeframeOption("");
    } else if (value > 3600) {
      toast.error("Timeframe cannot exceed 3600 seconds (1 hour)");
    } else if (value <= 0) {
      toast.error("Timeframe must be a positive number");
    } else if (localSettings.playTimeframeOptions.includes(value)) {
      toast.error("This timeframe option already exists");
    }
  };

  const handleRemoveTimeframeOption = (option: number) => {
    if (localSettings.playTimeframeOptions.length > 1) {
      const newOptions = localSettings.playTimeframeOptions.filter(opt => opt !== option);
      setLocalSettings(prev => ({
        ...prev,
        playTimeframeOptions: newOptions,
        defaultPlayTimeframe: newOptions.includes(prev.defaultPlayTimeframe)
          ? prev.defaultPlayTimeframe
          : newOptions[0]
      }));
    }
  };

  const formatTime = (seconds: number) => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    }
    return `${seconds}s`;
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Clock className="h-6 w-6 text-[#E1C760]" />
            <h1 className="text-2xl font-bold text-[#E1C760]">Time Settings</h1>
          </div>
        </div>

        <div className="space-y-6">
          {/* Play Timeframe Settings */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760] flex items-center gap-2">
                <Timer className="h-5 w-5" />
                Play Timeframe Options
              </CardTitle>
              <CardDescription className="text-gray-400">
                Configure the time options available for players to vote on during gameplay
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-white mb-2 block">Available Options (seconds)</Label>
                <div className="flex flex-wrap gap-2 mb-4">
                  {localSettings.playTimeframeOptions.map((option) => (
                    <div key={option} className="flex items-center gap-1 bg-[#E1C760]/10 border border-[#E1C760]/30 rounded-lg px-3 py-1">
                      <span className="text-[#E1C760]">{formatTime(option)}</span>
                      {localSettings.playTimeframeOptions.length > 1 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 text-red-400 hover:text-red-300 hover:bg-red-400/10"
                          onClick={() => handleRemoveTimeframeOption(option)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Add new option (seconds)"
                    value={newTimeframeOption}
                    onChange={(e) => setNewTimeframeOption(e.target.value)}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="1"
                    max="3600"
                  />
                  <Button
                    onClick={handleAddTimeframeOption}
                    className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                    disabled={!newTimeframeOption || parseInt(newTimeframeOption) <= 0}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-white mb-2 block">Default Timeframe</Label>
                <select
                  value={localSettings.defaultPlayTimeframe}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, defaultPlayTimeframe: parseInt(e.target.value) }))}
                  className="w-full bg-black border border-[#E1C760]/30 rounded-lg px-3 py-2 text-white"
                >
                  {localSettings.playTimeframeOptions.map((option) => (
                    <option key={option} value={option}>
                      {formatTime(option)}
                    </option>
                  ))}
                </select>
              </div>
            </CardContent>
          </Card>
          {/* Thunee Calling Durations */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760] flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Thunee Calling Durations
              </CardTitle>
              <CardDescription className="text-gray-400">
                Set the time limits for different Thunee calling stages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-white mb-2 block">Trumper Stage</Label>
                  <Input
                    type="number"
                    value={localSettings.thuneeCallingDurations.trumper}
                    onChange={(e) => setLocalSettings(prev => ({
                      ...prev,
                      thuneeCallingDurations: {
                        ...prev.thuneeCallingDurations,
                        trumper: parseInt(e.target.value) || 0
                      }
                    }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="1"
                    max="60"
                  />
                  <span className="text-xs text-gray-400">seconds</span>
                </div>
                <div>
                  <Label className="text-white mb-2 block">First Remaining</Label>
                  <Input
                    type="number"
                    value={localSettings.thuneeCallingDurations.firstRemaining}
                    onChange={(e) => setLocalSettings(prev => ({
                      ...prev,
                      thuneeCallingDurations: {
                        ...prev.thuneeCallingDurations,
                        firstRemaining: parseInt(e.target.value) || 0
                      }
                    }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="1"
                    max="60"
                  />
                  <span className="text-xs text-gray-400">seconds</span>
                </div>
                <div>
                  <Label className="text-white mb-2 block">Last Remaining</Label>
                  <Input
                    type="number"
                    value={localSettings.thuneeCallingDurations.lastRemaining}
                    onChange={(e) => setLocalSettings(prev => ({
                      ...prev,
                      thuneeCallingDurations: {
                        ...prev.thuneeCallingDurations,
                        lastRemaining: parseInt(e.target.value) || 0
                      }
                    }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="1"
                    max="60"
                  />
                  <span className="text-xs text-gray-400">seconds</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Other Time Settings */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Other Time Settings</CardTitle>
              <CardDescription className="text-gray-400">
                Configure various timing aspects of the game
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-white mb-2 block">Voting Time Limit</Label>
                  <Input
                    type="number"
                    value={localSettings.votingTimeLimit}
                    onChange={(e) => setLocalSettings(prev => ({ ...prev, votingTimeLimit: parseInt(e.target.value) || 0 }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="5"
                    max="120"
                  />
                  <span className="text-xs text-gray-400">seconds for timeframe voting</span>
                </div>
                <div>
                  <Label className="text-white mb-2 block">Trump Display Duration</Label>
                  <Input
                    type="number"
                    value={localSettings.trumpDisplayDuration}
                    onChange={(e) => setLocalSettings(prev => ({ ...prev, trumpDisplayDuration: parseInt(e.target.value) || 0 }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="1"
                    max="60"
                  />
                  <span className="text-xs text-gray-400">seconds to show trump after first card</span>
                </div>
                <div>
                  <Label className="text-white mb-2 block">Card Dealing Speed</Label>
                  <Input
                    type="number"
                    value={localSettings.cardDealingSpeed}
                    onChange={(e) => setLocalSettings(prev => ({ ...prev, cardDealingSpeed: parseInt(e.target.value) || 0 }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="100"
                    max="2000"
                    step="50"
                  />
                  <span className="text-xs text-gray-400">milliseconds between cards</span>
                </div>
                <div>
                  <Label className="text-white mb-2 block">Timer Update Interval</Label>
                  <Input
                    type="number"
                    value={localSettings.timerUpdateInterval}
                    onChange={(e) => setLocalSettings(prev => ({ ...prev, timerUpdateInterval: parseInt(e.target.value) || 0 }))}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="50"
                    max="1000"
                    step="50"
                  />
                  <span className="text-xs text-gray-400">milliseconds for timer updates</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator className="bg-[#E1C760]/30" />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-end">
            <Button
              variant="outline"
              onClick={handleReset}
              className="border-red-500 text-red-500 hover:bg-red-500/10"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset to Defaults
            </Button>
            <Button
              onClick={handleSave}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
            >
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
