import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface TimeSettings {
  // Play timeframe options (in seconds)
  playTimeframeOptions: number[];
  defaultPlayTimeframe: number;
  
  // Thunee calling durations (in seconds)
  thuneeCallingDurations: {
    trumper: number;
    firstRemaining: number;
    lastRemaining: number;
  };
  
  // Voting and UI timeouts (in seconds)
  votingTimeLimit: number;
  trumpDisplayDuration: number;
  
  // Animation speeds (in milliseconds)
  cardDealingSpeed: number;
  
  // Turn timer update interval (in milliseconds)
  timerUpdateInterval: number;
}

interface TimeSettingsStore {
  settings: TimeSettings;
  updateSettings: (newSettings: Partial<TimeSettings>) => void;
  resetToDefaults: () => void;
  updatePlayTimeframeOptions: (options: number[]) => void;
  updateThuneeCallingDuration: (stage: keyof TimeSettings['thuneeCallingDurations'], duration: number) => void;
}

const defaultSettings: TimeSettings = {
  playTimeframeOptions: [3, 4, 5, 6, 60],
  defaultPlayTimeframe: 3,
  thuneeCallingDurations: {
    trumper: 5,
    firstRemaining: 3,
    lastRemaining: 2,
  },
  votingTimeLimit: 15,
  trumpDisplayDuration: 10,
  cardDealingSpeed: 300,
  timerUpdateInterval: 100,
};

export const useTimeSettingsStore = create<TimeSettingsStore>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),
      
      resetToDefaults: () =>
        set({ settings: { ...defaultSettings } }),
      
      updatePlayTimeframeOptions: (options) =>
        set((state) => ({
          settings: {
            ...state.settings,
            playTimeframeOptions: options,
            // If current default is not in new options, set to first option
            defaultPlayTimeframe: options.includes(state.settings.defaultPlayTimeframe)
              ? state.settings.defaultPlayTimeframe
              : options[0],
          },
        })),
      
      updateThuneeCallingDuration: (stage, duration) =>
        set((state) => ({
          settings: {
            ...state.settings,
            thuneeCallingDurations: {
              ...state.settings.thuneeCallingDurations,
              [stage]: duration,
            },
          },
        })),
    }),
    {
      name: 'thunee-time-settings',
      version: 1,
    }
  )
);
