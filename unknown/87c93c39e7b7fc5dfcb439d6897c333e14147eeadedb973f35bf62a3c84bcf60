"use client";
import { useState, useEffect } from "react";
import { AlertCircle, Eye } from "lucide-react";
import { Input } from "@/components/ui/input";
import LobbyButton from "@/components/LobbyButton";
import { useNavigate } from "react-router-dom";
import BurgerMenu from "@/components/BurgerMenu";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import gameService from "@/services/gameService";
import { useSpectatorStore } from "@/store/spectatorStore";
import { useLobbyStore } from "@/store/lobbyStore";

interface GameToSpectate {
  gameCode: string;
  partnerInviteCode?: string;
  opponentInviteCode?: string;
  teamNames: {
    1: string;
    2: string;
  };
  playerCount: number;
  spectatorCount: number;
}

export default function SpectateGames() {
  const [gameCode, setGameCode] = useState("");
  const [spectatorName, setSpectatorName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [availableGames, setAvailableGames] = useState<GameToSpectate[]>([]);
  const [isLoadingGames, setIsLoadingGames] = useState(false);

  const navigate = useNavigate();
  const { setSpectatorInfo } = useSpectatorStore();

  // Load available games on component mount
  useEffect(() => {
    loadAvailableGames();
  }, []);

  // Function to load available games
  const loadAvailableGames = async () => {
    setIsLoadingGames(true);
    setErrorMessage(null);

    try {
      // Connect to game service if not already connected
      if (!gameService.isConnected()) {
        await gameService.connect("Spectator");
      }

      // Get available games
      const response = await gameService.sendGameAction('get_available_games', {});

      if (response && response.games) {
        // Ensure each game has a unique key by adding an index if needed
        const games = response.games.map((game: any, index: number) => ({
          ...game,
          uniqueId: `${game.gameCode}_${index}`
        }));
        setAvailableGames(games);
      } else {
        setErrorMessage("Failed to load available games");
      }
    } catch (error) {
      console.error('Error loading games:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to load available games');
    } finally {
      setIsLoadingGames(false);
    }
  };

  // Function to handle joining a game as spectator
  const handleJoinGame = async (code: string) => {
    if (!spectatorName) {
      setErrorMessage("Please enter your name");
      return;
    }

    const codeToUse = code || gameCode;
    if (!codeToUse) {
      setErrorMessage("Please enter a game code or select a game from the list");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Connect to game service if not already connected
      if (!gameService.isConnected()) {
        await gameService.connect(spectatorName);
      }

      // Join as spectator
      const response = await gameService.sendGameAction('join_as_spectator', {
        gameCode: codeToUse,
        spectatorName
      });

      if (response.success) {
        // Store spectator info
        setSpectatorInfo({
          isSpectator: true,
          gameCode: codeToUse,
          spectatorName,
          gameLobby: response.gameLobby,
          playerCards: response.playerCards || {}
        });

        // Log the spectator state for debugging
        console.log('Spectator state set:', useSpectatorStore.getState());

        // Set the lobby code in the lobby store to prevent redirect
        useLobbyStore.getState().setLobbyCode(codeToUse);

        // Add a small delay to ensure state is updated before navigation
        setTimeout(() => {
          // Navigate to game view - the App component has been updated to not redirect spectators
          navigate('/game');
        }, 100);
      } else {
        setErrorMessage(response.error || "Failed to join game as spectator");
      }
    } catch (error) {
      console.error('Error joining game:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to join game as spectator');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      {/* Header with burger menu */}
      <div className="flex justify-between items-center p-4 border-b border-[#E1C760]/50">
        <BurgerMenu />
        <div className="flex-1 flex justify-center">
          <h1 className="text-xl font-bold text-[#E1C760]">Spectate Games</h1>
        </div>
        <div className="w-8"></div> {/* Empty div for balance */}
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col items-center p-4 overflow-y-auto">
        {/* Error message */}
        {errorMessage && (
          <Alert variant="destructive" className="mb-4 bg-red-900/50 border border-red-500">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        {/* Spectator name input */}
        <div className="w-full max-w-md mb-6">
          <label className="block text-[#E1C760] mb-2">Your Name</label>
          <Input
            value={spectatorName}
            onChange={(e) => setSpectatorName(e.target.value)}
            placeholder="Enter your name"
            className="bg-transparent border border-[#E1C760] text-white"
            disabled={isLoading}
          />
        </div>

        {/* Game code input */}
        <div className="w-full max-w-md mb-6">
          <label className="block text-[#E1C760] mb-2">Game Code</label>
          <div className="flex gap-2">
            <Input
              value={gameCode}
              onChange={(e) => setGameCode(e.target.value)}
              placeholder="Enter game code"
              className="bg-transparent border border-[#E1C760] text-white"
              disabled={isLoading}
            />
            <Button
              onClick={() => handleJoinGame(gameCode)}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
              disabled={isLoading || !spectatorName || !gameCode}
            >
              {isLoading ? "Joining..." : "Join"}
            </Button>
          </div>
        </div>

        {/* Available games list */}
        <div className="w-full max-w-md">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold text-[#E1C760]">Available Games</h2>
            <Button
              onClick={loadAvailableGames}
              variant="outline"
              size="sm"
              className="border-[#E1C760] text-[#E1C760]"
              disabled={isLoadingGames}
            >
              {isLoadingGames ? "Loading..." : "Refresh"}
            </Button>
          </div>

          {availableGames.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              {isLoadingGames ? "Loading games..." : "No active games found"}
            </div>
          ) : (
            <div className="space-y-3">
              {availableGames.map((game) => (
                <div
                  key={game.uniqueId || `${game.gameCode}_${Math.random()}`}
                  className="bg-gray-800 rounded-lg p-3 border border-gray-700 hover:border-[#E1C760] transition-colors"
                >
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
                    <div>
                      <div className="font-medium">{game.teamNames[1]} vs {game.teamNames[2]}</div>
                      <div className="text-sm text-gray-400">
                        {game.playerCount} players • {game.spectatorCount} spectators
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Game Code: {game.gameCode}
                      </div>
                      {game.partnerInviteCode && (
                        <div className="text-xs text-gray-500">
                          Partner Invite: {game.partnerInviteCode}
                        </div>
                      )}
                      {game.opponentInviteCode && (
                        <div className="text-xs text-gray-500">
                          Opponent Invite: {game.opponentInviteCode}
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <Button
                        onClick={() => handleJoinGame(game.gameCode)}
                        className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                        disabled={isLoading || !spectatorName}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Spectate Game
                      </Button>

                      {game.partnerInviteCode && (
                        <Button
                          onClick={() => handleJoinGame(game.partnerInviteCode!)}
                          className="bg-blue-600 text-white hover:bg-blue-700"
                          disabled={isLoading || !spectatorName}
                          size="sm"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Team 1
                        </Button>
                      )}

                      {game.opponentInviteCode && (
                        <Button
                          onClick={() => handleJoinGame(game.opponentInviteCode!)}
                          className="bg-red-600 text-white hover:bg-red-700"
                          disabled={isLoading || !spectatorName}
                          size="sm"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Team 2
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Back button */}
      <div className="p-4 border-t border-[#E1C760]/50">
        <Button
          onClick={() => navigate('/')}
          variant="outline"
          className="w-full border-[#E1C760] text-[#E1C760]"
        >
          Back to Home
        </Button>
      </div>
    </div>
  );
}
