// Import buffer
import { <PERSON><PERSON><PERSON> } from 'buffer';

// Polyfill for global object in browser environment
if (typeof window !== 'undefined') {
  // Add global to window
  if (typeof (window as any).global === 'undefined') {
    (window as any).global = window;
  }

  // Add process to window
  if (typeof (window as any).process === 'undefined') {
    (window as any).process = {
      env: { DEBUG: undefined },
      nextTick: (callback: Function, ...args: any[]) => {
        setTimeout(() => callback(...args), 0);
      },
      browser: true
    };
  }

  // Add Buffer to window
  (window as any).Buffer = Buffer;

  // Add util to window
  (window as any).util = {
    debuglog: () => () => {},
    deprecate: (fn: Function) => fn,
    format: (format: string, ...args: any[]) => {
      return format.replace(/%s/g, () => args.shift());
    },
    inherits: (ctor: any, superCtor: any) => {
      ctor.super_ = superCtor;
      ctor.prototype = Object.create(superCtor.prototype, {
        constructor: {
          value: ctor,
          enumerable: false,
          writable: true,
          configurable: true
        }
      });
    }
  };
}
