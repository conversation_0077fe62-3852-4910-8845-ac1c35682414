"use client";
import { useGameStore } from "@/store/gameStore";

export function DealActions() {
  const { dealCards, isDealer } = useGameStore();

  const handleDeal = async () => {
    try {
      await dealCards();
      console.log('Cards dealt successfully');
    } catch (error) {
      console.error('Error dealing cards:', error);
    }
  };

  console.log('DealActions component rendered, isDealer:', isDealer);

  return (
    <div className="absolute bottom-24 left-0 right-0 flex justify-center items-center z-50">
      <button
        onClick={handleDeal}
        className="bg-[#E1C760] text-black px-8 py-3 rounded-md text-xl font-bold shadow-lg border-2 border-[#E1C760]/80 hover:bg-[#E1C760]/90 transition-colors animate-pulse"
      >
        DEAL
      </button>
    </div>
  );
}

export default DealActions;