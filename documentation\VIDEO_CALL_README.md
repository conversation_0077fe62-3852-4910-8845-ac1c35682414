# Thunee Video Call Feature

This document explains how to set up and use the video call feature in the Thunee game.

## Overview

The video call feature allows all 4 players to see and hear each other during the game. It uses WebRTC technology for peer-to-peer communication, with a dedicated signaling server to establish connections.

## Setup

### Option 1: Start Both Servers Together (Recommended)

The easiest way to start both the game server and video server is to use the provided batch file:

```bash
# On Windows
start-servers.bat
```

This will start both servers in separate command windows.

### Option 2: Start Servers Individually

#### 1. Start the Video Server

Start the video signaling server:

```bash
# Using npm script
npm run video-server

# Or using the batch file (Windows)
start-video-server.bat
```

The video server runs on port 3002 by default.

#### 2. Start the Game Server

Start the main game server:

```bash
cd server
npm start
```

### 3. Start the Game Client

Start the game client:

```bash
npm run dev
```

## Using the Video Call Feature

1. Once in a game, click the camera icon in the bottom right corner to open the video call interface.
2. Grant permission to use your camera and microphone when prompted.
3. You should see your own video feed immediately.
4. As other players join the game and open the video call, their video feeds will appear.
5. Use the controls at the bottom of the video call interface to:
   - Mute/unmute your microphone
   - Turn your camera on/off
   - End the call

## Troubleshooting

### Camera Access Issues

If you're having trouble with camera access:

1. **Check browser permissions**: Make sure you've granted camera and microphone permissions to the site
2. **Device in use**: If your camera is being used by another application, close it first
3. **Multiple tabs**: If testing with multiple tabs on the same computer, only one tab can access the camera at a time
4. **Hardware issues**: Make sure your camera is properly connected and working
5. **Privacy settings**: Check your operating system's privacy settings to ensure browser access to camera

### Limited Video Call Mode

If you encounter "Limited Video Call Mode":

1. This mode activates when there are issues with camera/microphone access
2. You can still participate in the call with audio only
3. You can still see other players' videos if they have working cameras
4. Click "Try Again" to attempt to reconnect with video
5. Use the "Debug Info" button to get technical details about the connection

### Connection Issues

If players cannot see each other:

1. Make sure the video server is running
2. Check that all players have granted camera/microphone permissions
3. Ensure there are no firewall or network restrictions blocking WebRTC traffic
4. Try refreshing the page and reopening the video call
5. Check browser console for any error messages

### Server Issues

If you encounter issues with the video server:

1. Make sure both the game server and video server are running
2. Check that the ports (3001 for game server, 3002 for video server) are not in use by other applications
3. If you see "Connection timeout" errors, try restarting both servers
4. Check that your firewall is not blocking the connections

## Technical Details

- The video call feature uses a separate WebSocket server for signaling
- Peer connections are established directly between players using WebRTC
- Video and audio streams are sent directly between players, not through the server
- The server only helps establish the initial connection
- The implementation uses the browser's native RTCPeerConnection API
- STUN servers are used to help establish connections through NATs and firewalls

## Testing with Multiple Tabs

When testing with multiple tabs on the same computer:

1. Only one tab can access the camera at a time
2. You'll see your own video in one tab and "Device in use" errors in other tabs
3. This is expected behavior and won't happen in a real game with different devices
4. You can still test the connection logic even with camera errors
5. For full testing, use multiple computers or devices

## Implementation Notes

- The video call feature is implemented using the browser's native WebRTC API
- The VideoCall component handles the UI and peer connections
- The VideoStream component renders individual video streams
- The videoService handles communication with the video signaling server
- The video server (server/videoServer.js) handles signaling between peers
