"use client";
import { useEffect, useRef } from "react";
import { Mic, MicOff, Video, VideoOff } from "lucide-react";

interface VideoStreamProps {
  stream: MediaStream | null;
  name: string;
  isLocal?: boolean;
  isAudioEnabled?: boolean;
  isVideoEnabled?: boolean;
  isSpeaking?: boolean;
}

export default function VideoStream({
  stream,
  name,
  isLocal = false,
  isAudioEnabled = true,
  isVideoEnabled = true,
  isSpeaking = false,
}: VideoStreamProps) {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;

      // Add error handler for video element
      videoRef.current.onerror = (e) => {
        console.error("Video element error:", e);
      };

      // Log when video starts playing
      videoRef.current.onplaying = () => {
        console.log(`Video for ${name} is now playing`);
      };
    }
  }, [stream, name]);

  return (
    <div
      className={`relative rounded-lg overflow-hidden ${
        isSpeaking ? "ring-2 ring-[#edcf5d]" : ""
      }`}
    >
      {stream && stream.getVideoTracks().length > 0 ? (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={isLocal} // Mute local video to prevent feedback
          className={`w-full h-full object-cover bg-neutral-900 ${
            !isVideoEnabled ? "invisible" : "visible"
          }`}
        />
      ) : (
        <div className="w-full h-full bg-neutral-900"></div>
      )}

      {/* Placeholder when video is disabled or not available */}
      {(!isVideoEnabled || !stream || stream.getVideoTracks().length === 0) && (
        <div className="absolute inset-0 flex items-center justify-center bg-neutral-800">
          <div className="w-16 h-16 rounded-full bg-neutral-700 flex items-center justify-center">
            <span className="text-xl font-semibold text-[#edcf5d]">
              {name.charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
      )}

      {/* Name and status indicators */}
      <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent">
        <div className="flex items-center justify-between">
          <span className="text-white text-sm font-medium truncate">
            {name} {isLocal && "(You)"}
          </span>
          <div className="flex items-center space-x-1">
            {stream && stream.getAudioTracks().length > 0 ? (
              isAudioEnabled ? (
                <Mic size={16} className="text-white" />
              ) : (
                <MicOff size={16} className="text-red-500" />
              )
            ) : (
              <span title="No audio available"><MicOff size={16} className="text-gray-500" /></span>
            )}

            {stream && stream.getVideoTracks().length > 0 ? (
              isVideoEnabled ? (
                <Video size={16} className="text-white" />
              ) : (
                <VideoOff size={16} className="text-red-500" />
              )
            ) : (
              <span title="No video available"><VideoOff size={16} className="text-gray-500" /></span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
