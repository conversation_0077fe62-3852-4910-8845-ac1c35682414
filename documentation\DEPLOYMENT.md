# Thunee Game - IIS Windows Server Deployment Guide

This guide will help you deploy the Thunee card game to an IIS Windows Server.

## Prerequisites

1. **Windows Server with IIS installed**
2. **Node.js** (v18 or higher) - Download from [nodejs.org](https://nodejs.org/)
3. **IISNode** - Download from [GitHub](https://github.com/Azure/iisnode/releases)
4. **URL Rewrite Module** - Download from [IIS.net](https://www.iis.net/downloads/microsoft/url-rewrite)

## Deployment Steps

### Step 1: Prepare the Application

1. Clone or download the Thunee application to your development machine
2. Open a command prompt in the application directory
3. Run the deployment script:
   ```batch
   deploy-production.bat
   ```

This script will:
- Install dependencies
- Build the React frontend
- Copy files to `C:\inetpub\Thunee-Production`
- Set up the production environment

### Step 2: Configure IIS

Run the PowerShell script as Administrator:
```powershell
.\setup-iis.ps1
```

This script will:
- Enable required IIS features
- Create an Application Pool
- Create the website on port 96
- Configure permissions

### Step 3: Install Prerequisites (if not done automatically)

#### Install IISNode
1. Download the latest IISNode from [GitHub](https://github.com/Azure/iisnode/releases)
2. Install the appropriate version (x64 for 64-bit Windows)
3. Restart IIS after installation

#### Install URL Rewrite Module
1. Download from [IIS.net](https://www.iis.net/downloads/microsoft/url-rewrite)
2. Install and restart IIS

### Step 4: Start the Video Server (Optional)

The video calling feature requires a separate server:
```batch
start-video-server-production.bat
```

Or manually:
```batch
cd C:\inetpub\Thunee-Production
set NODE_ENV=production
node server/videoServer.js
```

## Configuration

### Environment Variables

The application uses these environment variables:
- `NODE_ENV=production`
- `PORT=80` (for main server)
- `VIDEO_PORT=3002` (for video server)

### URLs

- **Frontend**: `http://**************:96`
- **WebSocket Server**: Same as frontend (handled by IIS)
- **Video Server**: `http://**************:3002`

### Development vs Production

The application automatically detects the environment:

**Development (localhost)**:
- Uses `localhost:3001` for WebSocket server
- Uses `localhost:3002` for video server
- Configurable via browser settings

**Production**:
- Uses the same host as the frontend for WebSocket
- Uses port 3002 for video server
- CORS restricted to your domain

## File Structure

```
C:\inetpub\Thunee-Production\
├── dist/                 # Built React frontend
├── server/               # Node.js server files
│   ├── index.js         # Main WebSocket server
│   ├── videoServer.js   # Video calling server
│   ├── handlers/        # Game logic handlers
│   ├── utils/           # Utility functions
│   └── iisnode.yml      # IISNode configuration
├── web.config           # IIS configuration
├── package.json         # Production dependencies
└── .env                 # Environment variables
```

## Troubleshooting

### Check IISNode Logs
Visit: `http://**************:96/iisnode/`

### Common Issues

1. **500 Internal Server Error**
   - Check IISNode logs
   - Ensure Node.js is installed
   - Verify file permissions

2. **WebSocket Connection Failed**
   - Ensure WebSocket support is enabled in IIS
   - Check firewall settings
   - Verify CORS configuration

3. **Video Server Not Working**
   - Start the video server manually
   - Check if port 3002 is available
   - Verify firewall allows port 3002

### Manual IIS Configuration

If the PowerShell script doesn't work, configure manually:

1. **Create Application Pool**:
   - Name: `ThuneeAppPool`
   - .NET CLR Version: `No Managed Code`
   - Identity: `ApplicationPoolIdentity`

2. **Create Website**:
   - Name: `Thunee`
   - Physical Path: `C:\inetpub\Thunee-Production`
   - Port: `96`
   - Application Pool: `ThuneeAppPool`

3. **Enable Features**:
   - WebSocket Protocol
   - URL Rewrite
   - Static Content

## Security Considerations

1. **Firewall**: Ensure ports 96 and 3002 are open
2. **CORS**: Production CORS is restricted to your domain
3. **File Permissions**: IIS_IUSRS has full control over the application directory

## Monitoring

- **IIS Logs**: Check Windows Event Viewer
- **Application Logs**: Check `C:\inetpub\Thunee-Production\iisnode\` directory
- **Performance**: Monitor CPU and memory usage

## Updates

To update the application:
1. Run `deploy-production.bat` again
2. The script will rebuild and redeploy
3. IIS will automatically restart the application

## Support

For issues:
1. Check the deployment logs
2. Verify all prerequisites are installed
3. Check IISNode logs for detailed error messages
4. Ensure Node.js version compatibility
