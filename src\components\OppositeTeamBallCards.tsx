import { useState, useEffect } from 'react';
import { useGameStore } from '../store/gameStore';
import '../styles/BallCards.css';
import Card from './Card';
import type { Card as CardType } from '../types';

// Reuse the existing card position map from BallCards.tsx
const cardPositionMap: Record<number, Record<string, { x: number; y: number; rotation: number; zIndex: number }>> = {
  0: {
    // Position for ball 0: All cards stacked in center
    '6H': { x: 150, y: 150, rotation: 27, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 150, y: 150, rotation: 25, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  1: {
    // Position for ball 1: translate(180px, 210px) rotate(36deg)
    '6H': { x: 180, y: 185, rotation: 36, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 180, y: 185, rotation: 36, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  2: {
    // Position for ball 2: translate(150px, 250px) rotate(90deg)
    '6H': { x: 150, y: 220, rotation: 90, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 150, y: 220, rotation: 90, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  3: {
    // Position for ball 3: translate(250px, 200px) rotate(0deg)
    '6H': { x: 250, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 250, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  4: {
    // Position for ball 4: translate(150px, 310px) rotate(90deg)
    '6H': { x: 150, y: 290, rotation: 90, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 150, y: 290, rotation: 90, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  5: {
    // Position for ball 5: translate(250px, 380px) rotate(0deg)
    '6H': { x: 250, y: 330, rotation: 0, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 250, y: 330, rotation: 0, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  6: {
    // Position for ball 6: translate(350px, 200px) rotate(0deg) with the card flipped
    '6H': { x: 350, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 2
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 350, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 1
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  // Balls 7-12 use the same positions as 1-6, but with different card backs
  7: {
    // Same position as ball 1: translate(180px, 210px) rotate(36deg)
    '6H': { x: 180, y: 185, rotation: 36, zIndex: 2 }, // Card back for Team 1
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 180, y: 185, rotation: 36, zIndex: 2 }, // Card back for Team 2
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  8: {
    // Same position as ball 2: translate(150px, 250px) rotate(90deg)
    '6H': { x: 150, y: 220, rotation: 90, zIndex: 2 }, // Card back for Team 1
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 150, y: 220, rotation: 90, zIndex: 2 }, // Card back for Team 2
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  9: {
    // Same position as ball 3: translate(250px, 200px) rotate(0deg)
    '6H': { x: 250, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 1
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 250, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 2
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  10: {
    // Same position as ball 4: translate(150px, 310px) rotate(90deg)
    '6H': { x: 150, y: 290, rotation: 90, zIndex: 2 }, // Card back for Team 1
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 150, y: 290, rotation: 90, zIndex: 2 }, // Card back for Team 2
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  11: {
    // Same position as ball 5: translate(250px, 380px) rotate(0deg)
    '6H': { x: 250, y: 330, rotation: 0, zIndex: 2 }, // Card back for Team 1
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 250, y: 330, rotation: 0, zIndex: 2 }, // Card back for Team 2
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
  12: {
    // Same position as ball 6: translate(350px, 200px) rotate(0deg) with the card flipped
    '6H': { x: 350, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 1
    '6D': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 2
    '6S': { x: 350, y: 150, rotation: 0, zIndex: 2 }, // Card back for Team 2
    '6C': { x: 150, y: 150, rotation: 0, zIndex: 1 }, // Fixed position for Team 1
  },
};

// Component for opposite team ball cards (positioned left)
const OppositeTeamBallCards = () => {
  const { ballScores, teamNames, players } = useGameStore();
  const [cards, setCards] = useState<CardType[]>([]);

  // Determine the player's team (1 or 2)
  const currentPlayer = players.find(player => player.isCurrentPlayer);
  const playerTeam = currentPlayer?.team as 1 | 2 || 1; // Default to team 1 if not found

  // Get the opposite team
  const oppositeTeam = playerTeam === 1 ? 2 : 1;

  // Get the ball number for the opposite team
  const ballNumber = Math.min(ballScores[`team${oppositeTeam}`] || 0, 12);

  // Initialize cards
  useEffect(() => {
    const cardFaces = ['6H', '6D', '6C', '6S'] as const;

    // Use the actual ball number (including 0) for both positions and display logic
    const ballNumberToUse = ballNumber;

    const initialCards = cardFaces.map((face, index) => {
      // For Team 1:
      // - For balls 1-5: 6C is visible, 6S is visible
      // - For ball 6: 6C is visible, 6S is flipped
      // - For balls 7-11: 6C is visible, 6H is visible
      // - For ball 12: 6C is visible, 6H is flipped
      // For Team 2:
      // - For balls 1-5: 6D is visible, 6H is visible
      // - For ball 6: 6D is visible, 6H is flipped
      // - For balls 7-11: 6D is visible, 6S is visible
      // - For ball 12: 6D is visible, 6S is flipped
      const shouldBeFlipped =
        (ballNumberToUse === 6 && ((oppositeTeam === 1 && face === '6S') || (oppositeTeam === 2 && face === '6H'))) ||
        (ballNumberToUse === 12 && ((oppositeTeam === 1 && face === '6H') || (oppositeTeam === 2 && face === '6S')));

      return {
        id: `card-${oppositeTeam}-${index}`,
        face,
        position: cardPositionMap[ballNumberToUse][face],
        isFlipped: shouldBeFlipped,
      };
    });
    setCards(initialCards);
  }, [oppositeTeam, ballNumber]);

  // Update card positions and flipped state when ball number changes
  useEffect(() => {
    if (cards.length > 0) {
      // Use the actual ball number (including 0) for both positions and display logic
      const ballNumberToUse = ballNumber;

      setCards(prevCards =>
        prevCards.map(card => {
          const shouldBeFlipped =
            (ballNumberToUse === 6 && ((oppositeTeam === 1 && card.face === '6S') || (oppositeTeam === 2 && card.face === '6H'))) ||
            (ballNumberToUse === 12 && ((oppositeTeam === 1 && card.face === '6H') || (oppositeTeam === 2 && card.face === '6S')));

          return {
            ...card,
            position: cardPositionMap[ballNumberToUse][card.face],
            isFlipped: shouldBeFlipped,
          };
        })
      );
    }
  }, [ballNumber, cards.length, oppositeTeam]);

  return (
    <div className="opposite-team-ball-cards">
      <div className="team-name">{teamNames[oppositeTeam]}</div>
      <div className="table-surface">
        {cards.length > 0 ? (
          cards.map(card => (
            <Card key={card.id} card={card} team={oppositeTeam} ballNumber={ballNumber} />
          ))
        ) : (
          <p>No cards to display</p>
        )}
      </div>
    </div>
  );
};

export default OppositeTeamBallCards;
