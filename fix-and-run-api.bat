@echo off
echo Fixing and starting Thunee ASP.NET Core API...
echo.

REM Navigate to the correct directory
cd /d "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
if errorlevel 1 (
    echo Error: Could not navigate to ThuneeAPI directory
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

echo Checking for processes using ports 3001, 5000, and 5001...
echo.

REM Kill any processes using port 3001 (your Node.js server)
echo Stopping processes on port 3001...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001') do (
    echo Killing process %%a on port 3001
    taskkill /f /pid %%a >nul 2>&1
)

REM Kill any processes using port 5000
echo Stopping processes on port 5000...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do (
    echo Killing process %%a on port 5000
    taskkill /f /pid %%a >nul 2>&1
)

REM Kill any processes using port 5001
echo Stopping processes on port 5001...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5001') do (
    echo Killing process %%a on port 5001
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo Ports cleared. Starting ASP.NET Core API...
echo.

REM Set environment to Development explicitly
set ASPNETCORE_ENVIRONMENT=Development

echo Environment: %ASPNETCORE_ENVIRONMENT%
echo.

echo Building project...
dotnet build
if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Starting the application on development ports...
echo.
echo The API will be available at:
echo   http://localhost:5000
echo   https://localhost:5001
echo.
echo Test client available at:
echo   http://localhost:5000/test-client.html
echo.
echo Press Ctrl+C to stop the server
echo.

REM Run with explicit URLs to override any configuration issues
dotnet run --urls "http://localhost:5000;https://localhost:5001"
