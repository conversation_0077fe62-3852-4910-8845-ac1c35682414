"use client";
import { useState } from "react";
import { useGameStore, Player } from "@/store/gameStore";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { getCardImagePath } from "@/utils/cardUtils";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";

// Extended Card type for the HandHistory component
interface ExtendedCard {
  id: string;
  value: string;
  suit: string;
  image: string;
  points: number;
  playedBy?: string;
}

export default function HandHistory() {
  const { hands, teamNames, players } = useGameStore();
  const [isOpen, setIsOpen] = useState(false);

  const toggleHistory = () => {
    setIsOpen(!isOpen);
  };

  // Get suit name with first letter capitalized
  const formatSuit = (suit: string): string => {
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  // Get team name based on team number
  const getTeamName = (teamNumber: 1 | 2): string => {
    return teamNames[teamNumber] || `Team ${teamNumber}`;
  };

  return (
    <>
      {/* But<PERSON> to open the history */}
      <Button
        onClick={toggleHistory}
        className="fixed bottom-4 right-4 z-50 bg-[#E1C760] text-black hover:bg-[#D1B750] hover:text-black"
      >
        {isOpen ? "Close History" : "Hand History"}
      </Button>

      {/* Modal for hand history */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div
              className="absolute inset-0 bg-black/50"
              onClick={() => setIsOpen(false)}
            />
            <Card className="relative w-full max-w-3xl max-h-[80vh] overflow-auto bg-black border-2 border-[#E1C760] p-4 z-10">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-[#E1C760]">Hand History</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                  className="text-[#E1C760] hover:text-white hover:bg-gray-800"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {hands.length === 0 ? (
                <p className="text-gray-400 text-center py-8">No hands played yet.</p>
              ) : (
                <div className="space-y-4">
                  {hands.map((hand) => (
                    <div
                      key={hand.id}
                      className="border border-gray-700 rounded-md p-3 bg-gray-900"
                    >
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-[#E1C760] font-semibold">
                          Hand #{hand.id} - Lead Suit: {formatSuit(hand.leadSuit || 'Unknown')}
                        </h3>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          hand.winningTeam === 1 ? 'bg-blue-900 text-blue-200' : 'bg-red-900 text-red-200'
                        }`}>
                          {getTeamName(hand.winningTeam || 1)} Won
                        </span>
                      </div>

                      {/* Cards played in this hand */}
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mb-3">
                        {hand.cards.map((card: ExtendedCard, cardIndex) => {
                          // Find the player who played this card
                          const playerId = card.playedBy as string;
                          // Try to find the player in the game state or use the winner if it matches
                          const player: Player = 
                            // First check if this is the winner
                            (playerId === hand.winner?.id ? hand.winner : 
                            // Then check if we can find the player in the current game state
                            players.find(p => p.id === playerId)) || 
                            // Fallback to a default player
                            { id: playerId, name: 'Player', avatar: '', team: (hand.winningTeam === 1 ? 2 : 1) as 1 | 2 };

                          return (
                            <div key={`${card.id}-${cardIndex}`} className="flex flex-col items-center">
                              <div className="w-16 h-24 relative mb-1">
                                <img
                                  src={getCardImagePath(card.value, card.suit)}
                                  alt={`${card.value} of ${card.suit}`}
                                  className={`w-full h-full object-contain border rounded ${
                                    playerId === hand.winner?.id ? 'border-2 border-[#E1C760]' : 'border-gray-700'
                                  }`}
                                />
                                {playerId === hand.winner?.id && (
                                  <div className="absolute -top-2 -right-2 bg-[#E1C760] text-black text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                                    ✓
                                  </div>
                                )}
                              </div>
                              <span className="text-xs text-gray-300 truncate max-w-full">
                                {player?.name || 'Unknown'}
                              </span>
                              <span className={`text-xs ${player?.team === 1 ? 'text-blue-400' : 'text-red-400'}`}>
                                {getTeamName(player?.team || 1)}
                              </span>
                            </div>
                          );
                        })}
                      </div>

                      {/* Hand result */}
                      <div className="bg-gray-800 p-2 rounded text-sm">
                        <p className="text-white">
                          <span className="font-semibold">{hand.winner?.name || 'Unknown'}</span> won with{' '}
                          <span className="font-semibold">
                            {(() => {
                              const winningCard = hand.cards.find((c: ExtendedCard) => c.playedBy === hand.winner?.id) as ExtendedCard;
                              return winningCard ? `${winningCard.value} of ${formatSuit(winningCard.suit)}` : 'Unknown card';
                            })()}
                          </span>
                        </p>
                        <p className="text-gray-400 text-xs mt-1">
                          Reason: {hand.winReason || 'Unknown'} • Points: {hand.points}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
