@echo off
echo Testing ThuneeAPI build...

cd ThuneeAPI
if errorlevel 1 (
    echo ThuneeAPI directory not found
    pause
    exit /b 1
)

echo Restoring packages...
dotnet restore
if errorlevel 1 (
    echo Package restore failed
    pause
    exit /b 1
)

echo Building project...
dotnet build
if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo Build successful!
echo.
echo To run the application:
echo   cd ThuneeAPI
echo   dotnet run
echo.
echo Then access:
echo   http://localhost:5000
echo   http://localhost:5000/test-client.html
echo.
pause
