/**
 * Utility functions for dealing cards
 */
const cardUtils = require('./cardUtils');

/**
 * Deal a specific number of cards to all players in a lobby
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object
 * @param {number} cardsPerPlayer - Number of cards to deal to each player
 */
function dealCardsToAllPlayers(io, lobby, matchedLobby, cardsPerPlayer) {
  console.log(`Dealing ${cardsPerPlayer} cards to each player in lobby ${lobby.lobbyCode}`);

  // Get all players from both lobbies
  const allPlayers = [];

  // Check if this is a single lobby with all players (invite code case)
  const team1Count = lobby.teams[1]?.length || 0;
  const team2Count = lobby.teams[2]?.length || 0;
  const totalPlayers = team1Count + team2Count;
  const isSingleLobby = totalPlayers === 4;

  // Add players from the first lobby
  if (lobby.teams[1]) {
    lobby.teams[1].forEach(player => {
      allPlayers.push(player);
    });
  }

  // Add players from the matched lobby if it's different from the main lobby
  if (!isSingleLobby && matchedLobby && matchedLobby.teams && matchedLobby.teams[1]) {
    matchedLobby.teams[1].forEach(player => {
      allPlayers.push(player);
    });
  }

  // Add players from the second team in the first lobby
  if (lobby.teams[2]) {
    lobby.teams[2].forEach(player => {
      allPlayers.push(player);
    });
  }

  // Add players from the second team in the matched lobby if it's different from the main lobby
  if (!isSingleLobby && matchedLobby && matchedLobby.teams && matchedLobby.teams[2]) {
    matchedLobby.teams[2].forEach(player => {
      allPlayers.push(player);
    });
  }

  // Sort players by ID to ensure consistent order across all clients
  allPlayers.sort((a, b) => a.id.localeCompare(b.id));

  // Find the dealer index
  const dealerIndex = allPlayers.findIndex(player => player.id === lobby.dealerId);
  if (dealerIndex === -1) {
    console.error(`Dealer ${lobby.dealerId} not found in players list`);
    return;
  }

  // Determine the dealing order based on the dealer's position
  // Order: player on right → partner → opponent's partner → dealer
  const dealingOrder = [];

  // Player on right (clockwise from dealer)
  const rightPlayerIndex = (dealerIndex + 1) % allPlayers.length;
  dealingOrder.push(allPlayers[rightPlayerIndex]);

  // Partner (opposite the dealer)
  const partnerIndex = (dealerIndex + 2) % allPlayers.length;
  dealingOrder.push(allPlayers[partnerIndex]);

  // Opponent's partner (opposite the player on right)
  const opponentPartnerIndex = (dealerIndex + 3) % allPlayers.length;
  dealingOrder.push(allPlayers[opponentPartnerIndex]);

  // Dealer
  dealingOrder.push(allPlayers[dealerIndex]);

  console.log('Dealing order:', dealingOrder.map(p => `${p.name} (${p.id})`));

  // Get existing player cards from both lobbies if they exist
  const existingPlayerCards = {};

  // Always create a new game deck to ensure no duplicates
  lobby.gameDeck = cardUtils.createGameDeck();
  console.log(`Created game deck with ${lobby.gameDeck.length} cards`);

  // Share the deck with the matched lobby
  matchedLobby.gameDeck = lobby.gameDeck;

  // Initialize or reset the allDealtCards set
  lobby.allDealtCards = new Set();
  matchedLobby.allDealtCards = new Set();

  // Log the current state of player cards
  console.log(`Starting with ${Object.values(existingPlayerCards).flat().length} cards already dealt across all players`);

  // Ensure the deck has no duplicates
  if (cardUtils.hasDuplicateCards(lobby.gameDeck)) {
    console.warn('Duplicates found in game deck, removing duplicates');
    lobby.gameDeck = cardUtils.removeDuplicateCards(lobby.gameDeck);
    matchedLobby.gameDeck = lobby.gameDeck;
    console.log(`Unique deck has ${lobby.gameDeck.length} cards`);
  }

  // Collect existing cards from all players in both lobbies
  // First check if we have stored player cards in the lobby
  if (lobby.playerCards) {
    console.log('Using stored player cards from lobby');
    Object.entries(lobby.playerCards).forEach(([playerId, cards]) => {
      // Make sure we have a valid array of cards
      if (Array.isArray(cards) && cards.length > 0) {
        existingPlayerCards[playerId] = [...cards];
        console.log(`Player ${playerId} already has ${cards.length} cards from lobby storage`);
      } else {
        console.log(`Player ${playerId} has no valid cards in lobby storage, initializing empty array`);
        existingPlayerCards[playerId] = [];
      }
    });
  } else {
    // If no stored cards, check player objects
    allPlayers.forEach(player => {
      if (player.cards && Array.isArray(player.cards) && player.cards.length > 0) {
        existingPlayerCards[player.id] = [...player.cards];
        console.log(`Player ${player.name} already has ${player.cards.length} cards from player object`);
      } else {
        console.log(`Player ${player.name} has no valid cards in player object, initializing empty array`);
        existingPlayerCards[player.id] = [];
      }
    });
  }

  // Verify all players have entries in existingPlayerCards
  dealingOrder.forEach(player => {
    if (!existingPlayerCards[player.id]) {
      console.log(`Player ${player.name} (${player.id}) missing from existingPlayerCards, initializing`);
      existingPlayerCards[player.id] = [];
    }
  });

  // Use our card utilities to deal cards to players, considering existing cards
  const playerCards = cardUtils.dealCardsToPlayers(dealingOrder, lobby.gameDeck, cardsPerPlayer, existingPlayerCards);

  // Emit card dealt events for each player's cards
  dealingOrder.forEach(player => {
    const cards = playerCards[player.id];
    if (cards && cards.length > 0) {
      // Emit card dealt events for each card
      cards.forEach(card => {
        console.log(`Emitting card_dealt event for ${card.value} of ${card.suit} to ${player.name} (${player.id})`);

        // Emit to the main lobby
        io.to(lobby.lobbyCode).emit('card_dealt', {
          playerId: player.id,
          card: card,
          isDealer: false
        });

        // Only emit to matched lobby if it's different from the main lobby
        if (!isSingleLobby && matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
          io.to(matchedLobby.lobbyCode).emit('card_dealt', {
            playerId: player.id,
            card: card,
            isDealer: false
          });
        }
      });

      // Send all cards to the player
      io.to(player.id).emit('receive_cards', { cards });
    }
  });

  // Validate that all players have exactly the expected number of cards
  let allPlayersHaveCorrectCards = true;
  dealingOrder.forEach(player => {
    const cards = playerCards[player.id];
    if (!cards || !Array.isArray(cards) || cards.length !== cardsPerPlayer) {
      console.error(`ERROR: Player ${player.name} (${player.id}) has ${cards?.length || 0} cards instead of ${cardsPerPlayer}!`);
      allPlayersHaveCorrectCards = false;
    }
  });

  if (!allPlayersHaveCorrectCards) {
    console.error(`CRITICAL ERROR: Not all players have ${cardsPerPlayer} cards after dealing!`);

    // Force fix: ensure all players have exactly the expected number of cards
    dealingOrder.forEach(player => {
      if (!playerCards[player.id] || !Array.isArray(playerCards[player.id]) || playerCards[player.id].length !== cardsPerPlayer) {
        console.log(`Fixing card count for player ${player.name} (${player.id})`);

        // Create a new set of cards for this player
        const newPlayerCards = [];
        const playerCardKeys = new Set();

        // Try to keep any valid existing cards
        if (playerCards[player.id] && Array.isArray(playerCards[player.id])) {
          playerCards[player.id].forEach(card => {
            if (card && card.value && card.suit && newPlayerCards.length < cardsPerPlayer) {
              newPlayerCards.push(card);
              playerCardKeys.add(`${card.value}_${card.suit}`);
            }
          });
        }

        // Create a new deck if needed
        if (!lobby.gameDeck || lobby.gameDeck.length < (cardsPerPlayer - newPlayerCards.length)) {
          console.log("Creating new deck for emergency dealing");
          lobby.gameDeck = cardUtils.createGameDeck();
        }

        // Add new cards until we have the correct number
        while (newPlayerCards.length < cardsPerPlayer && lobby.gameDeck.length > 0) {
          const randomIndex = Math.floor(Math.random() * lobby.gameDeck.length);
          const card = lobby.gameDeck[randomIndex];
          const cardKey = `${card.value}_${card.suit}`;

          if (!playerCardKeys.has(cardKey) && !lobby.allDealtCards.has(cardKey)) {
            newPlayerCards.push(card);
            playerCardKeys.add(cardKey);
            lobby.allDealtCards.add(cardKey);
            lobby.gameDeck.splice(randomIndex, 1);

            // Emit this card to all players
            console.log(`Emergency dealing: ${card.value} of ${card.suit} to ${player.name} (${player.id})`);
            io.to(lobby.lobbyCode).emit('card_dealt', {
              playerId: player.id,
              card: card,
              isDealer: false
            });

            if (!isSingleLobby && matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
              io.to(matchedLobby.lobbyCode).emit('card_dealt', {
                playerId: player.id,
                card: card,
                isDealer: false
              });
            }
          }
        }

        // Update the player's cards
        playerCards[player.id] = newPlayerCards;

        // Send all cards to the player
        io.to(player.id).emit('receive_cards', { cards: newPlayerCards });
      }
    });
  }

  // Store the player cards in the lobby for future reference
  lobby.playerCards = playerCards;
  matchedLobby.playerCards = playerCards;

  // Log the number of cards each player has
  Object.entries(playerCards).forEach(([playerId, cards]) => {
    console.log(`Player ${playerId} now has ${cards.length} cards total`);
  });

  // Notify all clients that all cards have been dealt
  io.to(lobby.lobbyCode).emit('cards_dealt', { dealerId: allPlayers[dealerIndex].id });

  // Only emit to matched lobby if it's different from the main lobby
  if (!isSingleLobby && matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
    io.to(matchedLobby.lobbyCode).emit('cards_dealt', { dealerId: allPlayers[dealerIndex].id });
  }
}

module.exports = {
  dealCardsToAllPlayers
};
