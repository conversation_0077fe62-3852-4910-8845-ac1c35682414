"use client";
import { useState } from "react";
import { useGameStore } from "@/store/gameStore";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";
import { X, CheckCircle, XCircle, Info } from "lucide-react";
import gameService from "@/services/gameService";

export default function JordhiHistory() {
  const { jordhiCalls, teamNames, jordhiHistoryOpen, updateGameState } = useGameStore();
  const currentPlayerId = gameService.getSocketId();

  const toggleHistory = () => {
    updateGameState({ jordhiHistoryOpen: !jordhiHistoryOpen });
  };

  // Get team name based on team number
  const getTeamName = (teamNumber: 1 | 2): string => {
    return teamNames[teamNumber] || `Team ${teamNumber}`;
  };

  // Get card value name for display
  const getCardValueName = (value: string): string => {
    if (!value) return 'Unknown';

    switch (value) {
      case 'J': return 'Jack';
      case '9': return 'Nine';
      case 'A': return 'Ace';
      case '10': return 'Ten';
      case 'K': return 'King';
      case 'Q': return 'Queen';
      default: return value;
    }
  };

  // Format suit name with first letter capitalized
  const formatSuit = (suit: string | null | undefined): string => {
    if (!suit) return 'Unknown';
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  return (
    <>
      {/* Button to open the history */}
      <Button
        onClick={toggleHistory}
        className="fixed bottom-4 left-4 z-50 bg-[#E1C760] text-black hover:bg-[#D1B750] hover:text-black"
      >
        {jordhiHistoryOpen ? "Close Jordhi History" : "Jordhi History"}
      </Button>

      {/* Modal for Jordhi history */}
      <AnimatePresence>
        {jordhiHistoryOpen && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div
              className="absolute inset-0 bg-black/50"
              onClick={() => updateGameState({ jordhiHistoryOpen: false })}
            />
            <Card className="relative w-full  max-h-[80vh] overflow-auto bg-black border-2 border-[#E1C760] p-4 z-10">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-[#E1C760]">Jordhi Call History</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => updateGameState({ jordhiHistoryOpen: false })}
                  className="text-[#E1C760] hover:text-white hover:bg-gray-800"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              <p className="text-gray-400 text-center mb-4 text-sm italic">
                Remember: Jordhi calls can only be made after your team has won a hand.
              </p>
              {jordhiCalls.length === 0 ? (
                <p className="text-gray-400 text-center py-8">No Jordhi calls made yet.</p>
              ) : (
                <div className="space-y-4">
                  {jordhiCalls.map((call, index) => {
                    const isCurrentPlayerCall = call.playerId === currentPlayerId;
                    // Determine validity status - prefer new fields, fall back to legacy isValid
                    const isFullyValid = call.isFullyValid !== undefined ? call.isFullyValid : call.isValid === true;
                    const isInvalid = call.isValidCards === false || (call.isValidCards === undefined && call.isValid === false);
                    const isPartiallyValid = call.isValidCards === true && call.isValidHandCount === false;

                    const hasDetailedInfo = isCurrentPlayerCall && (call.isValid !== undefined || call.isFullyValid !== undefined);

                    return (
                      <div
                        key={index}
                        className={`border rounded-md p-3 ${
                          isInvalid
                            ? 'border-red-700 bg-red-950/30'
                            : isPartiallyValid
                              ? 'border-yellow-700 bg-yellow-950/30'
                              : isFullyValid
                                ? 'border-green-700 bg-green-950/30'
                                : 'border-gray-700 bg-gray-900/30'
                        }`}
                      >
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="text-[#E1C760] font-semibold">
                            {call.playerName} ({getTeamName(call.playerTeam)})
                            {isCurrentPlayerCall && <span className="ml-2 text-xs text-white">(You)</span>}
                          </h3>
                          <div className="flex items-center">
                            <span className="text-lg font-bold mr-2">{call.value}</span>
                            {isInvalid ? (
                              <XCircle className="h-5 w-5 text-red-500" />
                            ) : isPartiallyValid ? (
                              <Info className="h-5 w-5 text-yellow-500" />
                            ) : isFullyValid ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : (
                              <Info className="h-5 w-5 text-gray-500" />
                            )}
                          </div>
                        </div>

                        <div className="text-sm">
                          {hasDetailedInfo ? (
                            isInvalid ? (
                              <p className="text-red-400">
                                Invalid call: You don't have the required card combination.
                                <span className="block mt-1 text-yellow-400">
                                  {call.playerTeam === 1
                                    ? `Team 1's target score reduced by ${call.value} points.`
                                    : `Team 2's target score reduced by ${call.value} points.`}
                                </span>
                              </p>
                            ) : isPartiallyValid ? (
                              <>
                                <p className="text-yellow-400">
                                  Partially valid call: Valid cards but called at an invalid time.
                                  <span className="block mt-1 text-yellow-400">
                                    {call.playerTeam === useGameStore.getState().biddingTeam
                                      ? `Opponent team's target score increased by ${call.value} points.`
                                      : `${getTeamName(call.playerTeam)}'s target score reduced by ${call.value} points.`}
                                  </span>
                                </p>
                                {call.jordhiCards && call.jordhiCards.length > 0 && (
                                  <p className="text-gray-300 mt-1">
                                    Cards: {call.jordhiCards.map(c => `${getCardValueName(c.value)} of ${formatSuit(c.suit)}`).join(', ')}
                                    {call.cardsRevealed === true && (
                                      <span className="ml-2 text-green-400">(Revealed to all players)</span>
                                    )}
                                    {call.cardsRevealed === false && (
                                      <span className="ml-2 text-gray-400">(Not revealed)</span>
                                    )}
                                  </p>
                                )}
                              </>
                            ) : isFullyValid ? (
                              <>
                                <p className="text-green-400">
                                  Valid call with {call.jordhiCards?.length || 0} cards in {formatSuit(call.jordhiSuit)}
                                  <span className="block mt-1 text-yellow-400">
                                    {call.playerTeam === useGameStore.getState().biddingTeam
                                      ? `Opponent team's target score increased by ${call.value} points.`
                                      : `${getTeamName(call.playerTeam)}'s target score reduced by ${call.value} points.`}
                                  </span>
                                </p>
                                {call.jordhiCards && call.jordhiCards.length > 0 && (
                                  <p className="text-gray-300 mt-1">
                                    Cards: {call.jordhiCards.map(c => `${getCardValueName(c.value)} of ${formatSuit(c.suit)}`).join(', ')}
                                    {call.cardsRevealed === true && (
                                      <span className="ml-2 text-green-400">(Revealed to all players)</span>
                                    )}
                                    {call.cardsRevealed === false && (
                                      <span className="ml-2 text-gray-400">(Not revealed)</span>
                                    )}
                                  </p>
                                )}
                              </>
                            ) : (
                              <p className="text-gray-400">
                                Call details not available
                              </p>
                            )
                          ) : (
                            <>
                              {isFullyValid ? (
                                <p className="text-green-400">
                                  Valid call in {formatSuit(call.jordhiSuit)}
                                  <span className="block mt-1 text-yellow-400">
                                    {call.playerTeam === useGameStore.getState().biddingTeam
                                      ? `Opponent team's target score increased by ${call.value} points.`
                                      : `${getTeamName(call.playerTeam)}'s target score reduced by ${call.value} points.`}
                                  </span>
                                </p>
                              ) : isPartiallyValid ? (
                                <p className="text-yellow-400">
                                  Partially valid call: Valid cards but called at an invalid time.
                                  <span className="block mt-1 text-yellow-400">
                                    {call.playerTeam === useGameStore.getState().biddingTeam
                                      ? `Opponent team's target score increased by ${call.value} points.`
                                      : `${getTeamName(call.playerTeam)}'s target score reduced by ${call.value} points.`}
                                  </span>
                                </p>
                              ) : isInvalid ? (
                                <p className="text-red-400">
                                  Invalid call: Player doesn't have the required card combination.
                                  <span className="block mt-1 text-yellow-400">
                                    {call.playerTeam === useGameStore.getState().biddingTeam
                                      ? `Opponent team's target score increased by ${call.value} points.`
                                      : `${getTeamName(call.playerTeam)}'s target score reduced by ${call.value} points.`}
                                  </span>
                                </p>
                              ) : (
                                <p className="text-gray-400">
                                  Validity information not available
                                </p>
                              )}
                            </>
                          )}

                          {/* Show hand number information */}
                          <p className="text-gray-400 mt-1">
                            {call.handNumber > 0
                              ? `Called after trick #${call.handNumber}`
                              : call.handNumber === 0
                                ? "Called before any tricks were won"
                                : ""}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
