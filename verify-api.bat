@echo off
echo Verifying Thunee ASP.NET Core API...
echo.

echo Testing API endpoint...
curl -s -o nul -w "Status: %%{http_code}" http://localhost:5000
echo.

echo.
echo API Verification Complete!
echo.
echo If you see "Status: 200" above, the API is working correctly.
echo.
echo Available URLs:
echo   Main API: http://localhost:5000
echo   Test Client: http://localhost:5000/test-client.html
echo   Config: http://localhost:5000/config.js
echo   SignalR Game Hub: http://localhost:5000/gameHub
echo   SignalR Video Hub: http://localhost:5000/videoHub
echo.
echo Next Steps:
echo 1. Update your React frontend to use the SignalR service
echo 2. Test the SignalR connection using the test client
echo 3. Deploy to production using deploy-to-iis.ps1
echo.
pause
