import { create } from 'zustand';
import { ChatMessage, ChatState } from '../types/chat';
import { v4 as uuidv4 } from 'uuid';
import gameService from '../services/gameService';

// Fallback function to generate a simple ID if uuid is not available
const generateId = () => {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

interface ChatActions {
  addMessage: (message: ChatMessage) => void;
  sendMessage: (text: string) => void;
  toggleChat: () => void;
  openChat: () => void;
  closeChat: () => void;
  clearMessages: () => void;
}

const initialState: ChatState = {
  messages: [],
  isOpen: false,
};

export const useChatStore = create<ChatState & ChatActions>((set) => ({
  ...initialState,

  addMessage: (message) => {
    set((state) => ({
      messages: [...state.messages, message],
    }));
  },

  sendMessage: (text) => {
    if (!text.trim()) return;

    // Generate a unique ID using uuid or fallback to our simple generator
    let messageId;
    try {
      messageId = uuidv4();
    } catch (error) {
      console.warn('UUID generation failed, using fallback ID generator');
      messageId = generateId();
    }

    const message: Omit<ChatMessage, 'senderName'> = {
      id: messageId,
      senderId: gameService.getSocketId() || 'unknown',
      text: text.trim(),
      timestamp: Date.now(),
    };

    // Send message to server
    gameService.sendGameAction('send_chat_message', { message });
  },

  toggleChat: () => {
    set((state) => ({
      isOpen: !state.isOpen,
    }));
  },

  openChat: () => {
    set({ isOpen: true });
  },

  closeChat: () => {
    set({ isOpen: false });
  },

  clearMessages: () => {
    set({ messages: [] });
  },
}));

// Set up socket event listeners for chat
export const setupChatListeners = () => {
  console.log('Setting up chat listeners');
  const { addMessage } = useChatStore.getState();

  // Clean up any existing listeners to prevent duplicates
  gameService.off('chat_message');

  // Listen for incoming chat messages
  gameService.on('chat_message', (data: ChatMessage) => {
    console.log('Received chat message:', data);

    // Make sure we have a valid message
    if (data && data.text && data.senderId) {
      // Add the message to the store
      addMessage(data);
    } else {
      console.warn('Received invalid chat message:', data);
    }
  });

  console.log('Chat listeners set up successfully');
};

