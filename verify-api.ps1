# Verification script for Thunee ASP.NET Core API
Write-Host "Verifying Thunee ASP.NET Core API..." -ForegroundColor Green

try {
    # Test the main endpoint
    Write-Host "Testing main endpoint..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Main endpoint is working (Status: $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "✗ Main endpoint returned status: $($response.StatusCode)" -ForegroundColor Red
    }
    
    # Test the test client
    Write-Host "Testing test client..." -ForegroundColor Yellow
    $testClientResponse = Invoke-WebRequest -Uri "http://localhost:5000/test-client.html" -TimeoutSec 10
    
    if ($testClientResponse.StatusCode -eq 200) {
        Write-Host "✓ Test client is accessible" -ForegroundColor Green
    } else {
        Write-Host "✗ Test client returned status: $($testClientResponse.StatusCode)" -ForegroundColor Red
    }
    
    # Test the config file
    Write-Host "Testing config file..." -ForegroundColor Yellow
    $configResponse = Invoke-WebRequest -Uri "http://localhost:5000/config.js" -TimeoutSec 10
    
    if ($configResponse.StatusCode -eq 200) {
        Write-Host "✓ Config file is accessible" -ForegroundColor Green
    } else {
        Write-Host "✗ Config file returned status: $($configResponse.StatusCode)" -ForegroundColor Red
    }
    
    Write-Host "`n=== API Verification Summary ===" -ForegroundColor Cyan
    Write-Host "✓ ASP.NET Core API is running successfully!" -ForegroundColor Green
    Write-Host "✓ All endpoints are accessible" -ForegroundColor Green
    Write-Host "`nAvailable URLs:" -ForegroundColor White
    Write-Host "  Main API: http://localhost:5000" -ForegroundColor Gray
    Write-Host "  Test Client: http://localhost:5000/test-client.html" -ForegroundColor Gray
    Write-Host "  Config: http://localhost:5000/config.js" -ForegroundColor Gray
    Write-Host "  SignalR Game Hub: http://localhost:5000/gameHub" -ForegroundColor Gray
    Write-Host "  SignalR Video Hub: http://localhost:5000/videoHub" -ForegroundColor Gray
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Update your React frontend to use the SignalR service" -ForegroundColor Gray
    Write-Host "2. Test the SignalR connection using the test client" -ForegroundColor Gray
    Write-Host "3. Deploy to production using deploy-to-iis.ps1" -ForegroundColor Gray
    
} catch {
    Write-Host "✗ API verification failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`nPossible issues:" -ForegroundColor Yellow
    Write-Host "- API is not running (run: dotnet run in ThuneeAPI directory)" -ForegroundColor Gray
    Write-Host "- Port 5000 is blocked by firewall" -ForegroundColor Gray
    Write-Host "- Another application is using port 5000" -ForegroundColor Gray
}
