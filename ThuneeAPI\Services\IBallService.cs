using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface IBallService
    {
        string DetermineNextDealer(Lobby lobby, List<Player> allPlayers);
        BallCompletedResponse CalculateBallWinner(Lobby lobby, List<Player> allPlayers);
        bool CheckGameEnd(Lobby lobby);
        void InitializeGameHistory(Lobby lobby);
        void AddBallToHistory(Lobby lobby, BallCompletedResponse ballData);
        List<BallHistory> GetGameHistory(Lobby lobby);
        void MarkWinningKhanak(Lobby lobby, object khanakResult);
        (bool ApplySpecialRules, int BallsAwarded, int WinningTeam, string Outcome, string Reason) CheckSpecialBallLimitRules(Lobby lobby, int callerTeam, string callType);
    }
}
