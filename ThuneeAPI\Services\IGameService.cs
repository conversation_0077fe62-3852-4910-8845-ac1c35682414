using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface IGameService
    {
        // Timeframe voting
        Task<bool> VoteTimeframeAsync(string connectionId, int timeframe);
        Task<bool> RequestTimeframeOptionsAsync(string connectionId);

        // Card actions
        Task<bool> PlayCardAsync(string connectionId, Card card);
        Task<bool> ShuffleCardsAsync(string connectionId, string shuffleType);
        Task<bool> RequestCutAsync(string connectionId, string playerId);
        Task<bool> CutCardsAsync(string connectionId, int cutPosition);
        Task<bool> SkipCutAsync(string connectionId);
        Task<bool> DealCardsAsync(string connectionId, int cardsPerPlayer);

        // Trump and bidding
        Task<bool> SelectTrumpAsync(string connectionId, string trumpSuit);
        Task<bool> BidAsync(string connectionId, int bidAmount);
        Task<bool> PassBidAsync(string connectionId);

        // Special calls
        Task<bool> CallJordhiAsync(string connectionId, int value, string jordhiSuit, List<Card> jordhiCards);
        Task<bool> RevealJordhiAsync(string connectionId, int jordhiValue, string jordhiSuit, List<Card> jordhiCards, bool revealCards);
        Task<bool> CallDoubleAsync(string connectionId);
        Task<bool> CallKhanakAsync(string connectionId);
        Task<bool> CallThuneeAsync(string connectionId, Card firstCard);
        Task<bool> FourBallAsync(string connectionId, string ballType, string option, string accusedPlayerId, int handNumber);

        // Dealer actions
        Task<bool> SetDealerAsync(string connectionId, string dealerId);
        Task<bool> StartDealerDeterminationAsync(string connectionId);

        // Events
        event Func<string, Task>? DealerDeterminationReset;
        event Func<string, DealerDeterminationStartedResponse, Task>? DealerDeterminationStarted;
        event Func<string, DealingCardToResponse, Task>? DealingCardTo;
        event Func<string, CardDealtResponse, Task>? CardDealt;
        event Func<string, DealerFoundResponse, Task>? DealerFound;
        event Func<string, DealerDeterminationAbortedResponse, Task>? DealerDeterminationAborted;
        event Func<string, TimeframeVoteResponse, Task>? TimeframeVoteUpdated;
        event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        event Func<string, ShuffleAnimationResponse, Task>? ShuffleAnimation;
        event Func<string, ShuffleCompleteResponse, Task>? ShuffleComplete;
        event Func<string, CutRequestedResponse, Task>? CutRequested;
        event Func<string, CutCompleteResponse, Task>? CutComplete;
        event Func<string, FirstFourDealtResponse, Task>? FirstFourDealt;

        // Game state
        Task<bool> PassThuneeAsync(string connectionId);
        Task<bool> HoldGameAsync(string connectionId);

        // Additional Events
        event Func<string, TimeframeOptionsResponse, Task>? TimeframeOptionsUpdated;
        event Func<string, CardsDealtResponse, Task>? CardsDealt;
        event Func<string, TrumpSelectedResponse, Task>? TrumpSelected;
        event Func<string, PlayerTurnResponse, Task>? PlayerTurn;
        event Func<string, CardPlayedResponse, Task>? CardPlayed;
        event Func<string, HandCompletedResponse, Task>? HandCompleted;
        event Func<string, BallCompletedResponse, Task>? BallCompleted;
        event Func<string, GameEndedResponse, Task>? GameEnded;
        event Func<string, JordhiCalledResponse, Task>? JordhiCalled;
        event Func<string, JordhiCardsRevealedResponse, Task>? JordhiCardsRevealed;
        event Func<string, FourBallResultResponse, Task>? FourBallResult;
    }
}
