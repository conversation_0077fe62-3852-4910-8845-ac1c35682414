/**
 * Utility functions for Thunee ball completion and dealer rotation
 */

/**
 * Determine the next dealer based on the ball scores
 *
 * Rule: If the dealer-team's ball-score is greater than or equal to the opponents',
 * the deal passes to the right. If the dealer-team is still behind, the same dealer deals again.
 *
 * @param {Object} lobby - The game lobby
 * @param {Array} allPlayers - All players in the game
 * @returns {String} The ID of the next dealer
 */
function determineNextDealer(lobby, allPlayers) {
  // Get the current dealer
  const currentDealerId = lobby.dealerId;
  if (!currentDealerId) {
    console.error('No current dealer found');
    // Default to the first player if no dealer is set
    return allPlayers[0]?.id;
  }

  const currentDealer = allPlayers.find(p => p.id === currentDealerId);
  if (!currentDealer) {
    console.error(`Current dealer ${currentDealerId} not found in players list`);
    return allPlayers[0]?.id;
  }

  // Get the dealer's team - first check if it's available from a Double result
  let dealerTeam = null;

  // If we have a Double result with dealer team info, use that
  if (lobby.lastDoubleResult && lobby.lastDoubleResult.currentDealerTeam) {
    dealerTeam = lobby.lastDoubleResult.currentDealerTeam;
    console.log(`Using dealer team ${dealerTeam} from lastDoubleResult`);
  } else {
    // Otherwise use the team from the current dealer
    dealerTeam = currentDealer.team;
    console.log(`Using dealer team ${dealerTeam} from currentDealer`);
  }

  if (!dealerTeam) {
    console.error(`Dealer ${currentDealer.name} has no team assigned`);
    return currentDealerId; // Keep the same dealer
  }

  // Get the opposing team
  const opposingTeam = dealerTeam === 1 ? 2 : 1;

  // Get the ball scores
  const dealerTeamBalls = lobby.ballScores?.[`team${dealerTeam}`] || 0;
  const opposingTeamBalls = lobby.ballScores?.[`team${opposingTeam}`] || 0;

  console.log(`Ball scores: Dealer team (${dealerTeam}): ${dealerTeamBalls}, Opposing team (${opposingTeam}): ${opposingTeamBalls}`);

  // Log all players and their teams for debugging
  console.log("All players in determineNextDealer:");
  allPlayers.forEach(p => {
    console.log(`Player: ${p.name} (${p.id}) - Team ${p.team}${p.id === currentDealerId ? ' - Current Dealer' : ''}`);
  });

  // Apply the rule:
  // If dealer-team's ball-score is >= opponents', pass deal to the right
  // If dealer-team is behind, same dealer deals again
  if (dealerTeamBalls >= opposingTeamBalls) {
    console.log('Dealer team is ahead or tied, passing deal to the right');
    // Pass deal to the right (counter-clockwise in Thunee)
    // First, make sure allPlayers is sorted by position
    const sortedPlayers = [...allPlayers].sort((a, b) => a.position - b.position);

    // Log the sorted players for debugging
    console.log("Sorted players by position:");
    sortedPlayers.forEach(p => {
      console.log(`Position ${p.position}: ${p.name} (${p.id}) - Team ${p.team}${p.id === currentDealerId ? ' - Current Dealer' : ''}`);
    });

    // Find the current dealer in the sorted list
    const dealerIndex = sortedPlayers.findIndex(p => p.id === currentDealerId);
    if (dealerIndex === -1) {
      console.error(`Current dealer ${currentDealerId} not found in sorted players list`);
      return currentDealerId; // Keep the same dealer as a fallback
    }

    console.log(`Current dealer ${currentDealer.name} (${currentDealerId}) found at index ${dealerIndex} in sorted players list`);

    // Get the next dealer (to the right, which is the next index in the sorted array)
    const nextDealerIndex = (dealerIndex + 1) % sortedPlayers.length;
    const nextDealer = sortedPlayers[nextDealerIndex];

    console.log(`Next dealer: ${nextDealer.name} (${nextDealer.id}) - Team ${nextDealer.team} at position ${nextDealer.position}`);
    console.log(`Dealer rotation: ${currentDealer.name} (Team ${currentDealer.team}, Position ${currentDealer.position}) -> ${nextDealer.name} (Team ${nextDealer.team}, Position ${nextDealer.position})`);
    return nextDealer.id;
  } else {
    console.log(`Dealer team (${dealerTeam}) is behind with ${dealerTeamBalls} balls vs. ${opposingTeamBalls} balls for team ${opposingTeam}`);
    console.log(`Same dealer deals again: ${currentDealer.name} (${currentDealerId}) - Team ${currentDealer.team} at position ${currentDealer.position}`);
    return currentDealerId; // Keep the same dealer
  }
}

/**
 * Calculate the winner of a ball based on the points, bid, Jordhi calls, and final hand winner
 *
 * @param {Object} lobby - The game lobby
 * @param {Number} team1Points - Points scored by team 1 in this ball
 * @param {Number} team2Points - Points scored by team 2 in this ball
 * @param {Number} lastHandWinningTeam - The team that won the last hand (for final hand adjustment)
 * @returns {Object} The winner, updated ball scores, and additional data for the client
 */
function calculateBallWinner(lobby, team1Points, team2Points, lastHandWinningTeam) {
  // Get the bidding team and bid amount
  // Use trumperTeam if available, otherwise fall back to biddingTeam
  let biddingTeam = lobby.trumperTeam || lobby.biddingTeam || null;
  const bidAmount = lobby.currentBid || 0;

  // Log the trumping team for debugging
  console.log(`Ball completion: trumpingTeam=${biddingTeam}, trumperTeam=${lobby.trumperTeam}`);

  // Ensure we have a valid trumping team (either 1 or 2)
  if (biddingTeam !== 1 && biddingTeam !== 2) {
    console.error(`Invalid trumping team: ${biddingTeam}, defaulting to team 1`);
    // Default to team 1 as the trumping team if we don't have a valid value
    biddingTeam = 1;
  }

  // Get the initial target scores based on bidding
  const initialTeam1Target = biddingTeam === 1 ? 105 : (105 - bidAmount);
  const initialTeam2Target = biddingTeam === 2 ? 105 : (105 - bidAmount);

  console.log(`Initial target scores - Team 1: ${initialTeam1Target}, Team 2: ${initialTeam2Target}`);

  // Calculate Jordhi adjustments
  const jordhiCalls = lobby.jordhiCalls || [];
  let team1JordhiAdjustment = 0;
  let team2JordhiAdjustment = 0;

  // Process all Jordhi calls (both valid and invalid)
  // Use a Set to track unique player+value combinations to avoid double-counting
  const processedCalls = new Set();

  jordhiCalls.forEach(call => {
    const callKey = `${call.playerId}-${call.value}-${call.jordhiSuit}`;
    if (!processedCalls.has(callKey)) {
      processedCalls.add(callKey);
      const jordhiValue = parseInt(call.value, 10);
      if (call.playerTeam === 1) {
        team1JordhiAdjustment += jordhiValue;
      } else if (call.playerTeam === 2) {
        team2JordhiAdjustment += jordhiValue;
      }
    }
  });

  console.log(`Jordhi adjustments - Team 1: ${team1JordhiAdjustment}, Team 2: ${team2JordhiAdjustment}`);

  // Calculate final hand adjustment (±10 points)
  const finalHandAdjustment = lastHandWinningTeam ? (lastHandWinningTeam === biddingTeam ? 10 : -10) : 0;
  console.log(`Final hand adjustment: ${finalHandAdjustment} (last hand won by Team ${lastHandWinningTeam || 'unknown'})`);

  // Calculate final target scores
  // For the non-trumping team, we subtract their Jordhi calls and add the trumping team's Jordhi calls
  // The non-trumping team is trying to reach their target score
  const nonBiddingTeam = biddingTeam === 1 ? 2 : 1;

  // Apply Jordhi adjustments to the non-bidding team's target score
  let team1FinalTarget = initialTeam1Target;
  let team2FinalTarget = initialTeam2Target;

  if (biddingTeam === 1) {
    // Team 1 is the bidding/trumping team
    // Team 2 is the non-trumping team trying to reach their target
    team2FinalTarget -= team2JordhiAdjustment; // Decrease by their own Jordhi calls
    team2FinalTarget += team1JordhiAdjustment; // Increase by opposing team's Jordhi calls
  } else {
    // Team 2 is the bidding/trumping team
    // Team 1 is the non-trumping team trying to reach their target
    team1FinalTarget -= team1JordhiAdjustment; // Decrease by their own Jordhi calls
    team1FinalTarget += team2JordhiAdjustment; // Increase by opposing team's Jordhi calls
  }

  // Apply final hand adjustment
  // If the non-trumping team wins the last hand, decrease their target by 10
  // If the trumping team wins the last hand, increase the non-trumping team's target by 10
  if (biddingTeam === 1) {
    // Team 2 is the non-trumping team
    team2FinalTarget += finalHandAdjustment;
  } else {
    // Team 1 is the non-trumping team
    team1FinalTarget += finalHandAdjustment;
  }

  console.log(`Final target scores after all adjustments - Team 1: ${team1FinalTarget}, Team 2: ${team2FinalTarget}`);

  // Determine the winner
  let winner = null;

  // In Thunee, the non-trumping team is trying to reach their target score
  // If they reach it, they win the ball; if not, the trumping team wins
  // nonBiddingTeam is already declared above
  const nonBiddingTeamPoints = nonBiddingTeam === 1 ? team1Points : team2Points;
  const nonBiddingTeamTarget = nonBiddingTeam === 1 ? team1FinalTarget : team2FinalTarget;

  // Log detailed information for debugging
  console.log(`Non-trumping team: ${nonBiddingTeam}, Points: ${nonBiddingTeamPoints}, Target: ${nonBiddingTeamTarget}`);
  console.log(`Trumping team: ${biddingTeam}`);

  // We'll determine the winner after calculating the points from hands
  // This will be set after we calculate the points from the hands won

  // We'll update the ball scores after determining the winner

  // Prepare data for the client
  const targetScore = biddingTeam === 1 ? team2FinalTarget : team1FinalTarget;
  const initialTargetScore = biddingTeam === 1 ? initialTeam2Target : initialTeam1Target;
  const nonTrumpingTeamPoints = biddingTeam === 1 ? team2Points : team1Points;

  // Get hands won by non-trumping team
  const handsWonByNonTrumpingTeam = [];
  if (lobby.hands && Array.isArray(lobby.hands)) {
    // Create a Set to track unique hand IDs to avoid duplicates
    const uniqueHandIds = new Set();

    lobby.hands.forEach(hand => {
      if (hand.winningTeam === nonBiddingTeam && !uniqueHandIds.has(hand.id)) {
        uniqueHandIds.add(hand.id);
        handsWonByNonTrumpingTeam.push(hand);
      }
    });
  }

  // Double-check the points calculation
  const calculatedPoints = handsWonByNonTrumpingTeam.reduce((sum, hand) => sum + (hand.points || 0), 0);
  if (calculatedPoints !== nonBiddingTeamPoints) {
    console.error(`Points mismatch in ballUtils! Calculated ${calculatedPoints} but using ${nonBiddingTeamPoints}`);
    console.log(`Using calculated points (${calculatedPoints}) instead of nonBiddingTeamPoints (${nonBiddingTeamPoints})`);
  }

  // Use the calculated points for the return value
  const finalNonBiddingTeamPoints = calculatedPoints;

  // Now determine the winner based on the calculated points
  if (finalNonBiddingTeamPoints >= nonBiddingTeamTarget) {
    // Non-trumping team reached their target, they win
    winner = nonBiddingTeam;
    console.log(`Team ${nonBiddingTeam} (non-trumping) reached their target of ${nonBiddingTeamTarget} with ${finalNonBiddingTeamPoints} points and wins the ball`);
  } else {
    // Non-trumping team didn't reach their target, trumping team wins
    winner = biddingTeam;
    console.log(`Team ${nonBiddingTeam} (non-trumping) failed to reach their target of ${nonBiddingTeamTarget} with ${finalNonBiddingTeamPoints} points. Team ${biddingTeam} (trumping) wins the ball`);
  }

  // Double-check the winner determination
  console.log(`Final winner determination: Team ${winner}`);
  if (finalNonBiddingTeamPoints >= nonBiddingTeamTarget && winner !== nonBiddingTeam) {
    console.error(`ERROR: Non-trumping team reached target but was not declared winner!`);
  } else if (finalNonBiddingTeamPoints < nonBiddingTeamTarget && winner !== biddingTeam) {
    console.error(`ERROR: Non-trumping team did not reach target but was declared winner!`);
  }

  // Now update the ball scores based on the determined winner
  const currentBallScores = lobby.ballScores || { team1: 0, team2: 0 };

  // Check for "Call and lost" scenario - if the non-trumping team wins, they get 2 balls
  // This happens when a player bid to trump and is the trumper, but then the non-trumping team wins
  let isCallAndLost = false;
  let ballsToAward = 1; // Default is 1 ball

  if (winner === nonBiddingTeam) {
    // Non-trumping team won - check if this is a "Call and lost" scenario
    // This happens when the trumping team bid to trump (currentBid > 0)
    if (bidAmount > 0) {
      isCallAndLost = true;
      ballsToAward = 2; // Award 2 balls for "Call and lost"
      console.log(`"Call and lost" scenario: Team ${biddingTeam} bid to trump but Team ${nonBiddingTeam} won. Awarding ${ballsToAward} balls to Team ${nonBiddingTeam}`);
    }
  }

  const updatedBallScores = {
    team1: winner === 1 ? currentBallScores.team1 + ballsToAward : currentBallScores.team1,
    team2: winner === 2 ? currentBallScores.team2 + ballsToAward : currentBallScores.team2
  };

  // Log the final data being sent to the client
  console.log(`Final ball completion data:
    winner: ${winner}
    trumpingTeam: ${biddingTeam}
    nonTrumpingTeam: ${nonBiddingTeam}
    nonTrumpingTeamPoints: ${finalNonBiddingTeamPoints}
    targetScore: ${targetScore}
    initialTargetScore: ${initialTargetScore}
    jordhiAdjustments: {
      nonTrumpingTeam: ${biddingTeam === 1 ? team2JordhiAdjustment : team1JordhiAdjustment},
      trumpingTeam: ${biddingTeam === 1 ? team1JordhiAdjustment : team2JordhiAdjustment}
    }
    finalHandAdjustment: ${finalHandAdjustment}
    isCallAndLost: ${isCallAndLost}
    ballsAwarded: ${ballsToAward}
  `);

  return {
    winner,
    ballScores: updatedBallScores,
    // Additional data for the client
    targetScore,
    initialTargetScore,
    jordhiAdjustments: {
      nonTrumpingTeam: biddingTeam === 1 ? team2JordhiAdjustment : team1JordhiAdjustment,
      trumpingTeam: biddingTeam === 1 ? team1JordhiAdjustment : team2JordhiAdjustment
    },
    finalHandAdjustment,
    nonTrumpingTeamPoints: finalNonBiddingTeamPoints,
    trumpingTeam: biddingTeam,
    nonTrumpingTeam: nonBiddingTeam, // Explicitly include the non-trumping team
    handsWonByNonTrumpingTeam,
    isCallAndLost, // Include whether this was a "Call and lost" scenario
    ballsAwarded: ballsToAward // Include how many balls were awarded
  };
}

module.exports = {
  determineNextDealer,
  calculateBallWinner
};
