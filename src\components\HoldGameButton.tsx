import { useState, useEffect } from "react";
import gameService from "@/services/gameService";
import { useGameStore } from "@/store/gameStore";

interface HoldGameButtonProps {
  onHoldComplete: () => void;
  playerRole?: "remaining-player" | "trumper" | "first-remaining" | "last-remaining";
}

export default function HoldGameButton({
  onHoldComplete,
  playerRole = "remaining-player"
}: HoldGameButtonProps) {
  // Determine the appropriate duration based on the player role
  const getDuration = () => {
    if (playerRole === "trumper") return 5;
    if (playerRole === "first-remaining") return 3;
    if (playerRole === "last-remaining") return 2;
    return 3; // Default for remaining-player
  };

  const [isHolding, setIsHolding] = useState(false);
  const [countdown, setCountdown] = useState(getDuration());
  const [autoHideCountdown, setAutoHideCountdown] = useState(getDuration());
  const [showButton, setShowButton] = useState(true);
  const { isCurrentTurn, isTrumpSelector } = useGameStore();

  // Auto-hide the button after the specified time if not clicked
  useEffect(() => {
    if (isHolding) return; // Don't auto-hide if already holding

    // Start the auto-hide timer immediately
    const duration = getDuration();
    console.log(`Starting auto-hide countdown: ${duration} seconds for ${playerRole}`);

    const autoHideTimer = setInterval(() => {
      setAutoHideCountdown((prev) => {
        console.log(`Auto-hide countdown: ${prev - 1} seconds remaining for ${playerRole}`);
        if (prev <= 1) {
          clearInterval(autoHideTimer);
          // When countdown reaches 0, hide the button and complete
          console.log(`Auto-hide countdown complete for ${playerRole}, hiding button`);
          setShowButton(false);
          onHoldComplete();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(autoHideTimer);
  }, [isHolding, onHoldComplete, playerRole]);

  // Handle the hold countdown timer
  useEffect(() => {
    if (!isHolding) return;

    const holdDuration = getDuration();
    console.log(`Starting hold countdown: ${holdDuration} seconds for ${playerRole}`);

    const timer = setInterval(() => {
      setCountdown((prev) => {
        console.log(`Hold countdown: ${prev - 1} seconds remaining for ${playerRole}`);
        if (prev <= 1) {
          clearInterval(timer);
          // When countdown reaches 0, complete the hold
          console.log(`Hold countdown complete for ${playerRole}`);
          setIsHolding(false);
          onHoldComplete();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isHolding, onHoldComplete, playerRole]);

  const handleHoldGame = async () => {
    try {
      setIsHolding(true);
      const holdDuration = getDuration();
      console.log(`Holding game for ${holdDuration} seconds as ${playerRole}...`);

      // Send hold game event to server with player role
      await gameService.sendGameAction("hold_game", {
        playerRole,
        stage: playerRole, // Include the stage information
        duration: holdDuration
      });

      // If this is a first-remaining player, also show the Thunee calling prompt
      if (playerRole === "first-remaining" || playerRole === "last-remaining") {
        console.log(`${playerRole} player clicked Hold Game, showing Thunee calling prompt`);
        // The server will handle showing the Thunee calling prompt to this player
      }

      // The countdown will automatically complete after the specified duration
    } catch (error) {
      console.error("Error holding game:", error);
      setIsHolding(false);
      setCountdown(getDuration());
    }
  };

  if (!showButton) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      <div className="pointer-events-auto">
        {!isHolding ? (
          <button
            className="h-16 px-8 bg-black text-[#E1C760] text-xl font-medium rounded-lg border-2 border-[#E1C760] hover:bg-[#E1C760]/10 shadow-[0_0_10px_rgba(225,199,96,0.5)] animate-pulse"
            onClick={handleHoldGame}
            disabled={isHolding}
          >
            {playerRole === "trumper" ? `Call Thunee (${autoHideCountdown}s)` :
             playerRole === "first-remaining" ? `Call Thunee (${autoHideCountdown}s)` :
             playerRole === "last-remaining" ? `Last Chance to Call (${autoHideCountdown}s)` :
             `Hold Game (${autoHideCountdown}s)`}
          </button>
        ) : (
          <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 text-center shadow-[0_0_10px_rgba(225,199,96,0.5)]">
            <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Game Paused</h2>
            <p className="text-white mb-6">
              Game is paused for {countdown} seconds. Players can call Thunee or Royal Thunee during this time.
            </p>
            <div className="text-[#E1C760] text-4xl font-bold">{countdown}</div>
          </div>
        )}
      </div>
    </div>
  );
}
