import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '@/store/gameStore';
import { getCardImagePath } from '@/utils/cardUtils';
import '../styles/KhanakBallResultsDisplay.css';

interface HandWon {
  handId: number;
  winner: {
    id: string;
    name: string;
    team: 1 | 2;
  };
  cards: {
    suit: string;
    value: string;
  }[];
  points: number;
}

interface KhanakBallResultsDisplayProps {
  isVisible: boolean;
  onClose: () => void;
  khanakCallerId?: string;
  khanakCallerTeam?: 1 | 2;
  handWinnerId?: string;
  handWinnerTeam?: 1 | 2;
  opposingTeamHands?: HandWon[];
  threshold?: number;
  opposingTeamPoints?: number;
  teamJordhiPoints?: number;
  opposingTeamJordhiPoints?: number;
  outcome?: 'points_below_threshold' | 'points_above_threshold' | 'last_hand_lost' | 'opposing_team_no_hands' | 'no_jordhi_calls';
  ballsAwarded?: number;
  winningTeam?: 1 | 2;
  displayDuration?: number;
  onContinueGame?: () => void;
}

export default function KhanakBallResultsDisplay({
  isVisible,
  onClose,
  khanakCallerId,
  khanakCallerTeam,
  handWinnerId,
  handWinnerTeam,
  opposingTeamHands = [],
  threshold = 0,
  opposingTeamPoints = 0,
  teamJordhiPoints = 0,
  opposingTeamJordhiPoints = 0,
  outcome,
  ballsAwarded = 0,
  winningTeam,
  displayDuration = 15000, // Default to 15 seconds
  onContinueGame,
}: KhanakBallResultsDisplayProps) {
  const { players, teamNames } = useGameStore();
  const [timeRemaining, setTimeRemaining] = useState(displayDuration / 1000);
  const [showContinueButton, setShowContinueButton] = useState(false);

  // Find the khanak caller and hand winner players
  const khanakCallerPlayer = players.find(p => p.id === khanakCallerId);
  const handWinnerPlayer = players.find(p => p.id === handWinnerId);

  // Determine the opposing team
  const opposingTeam = khanakCallerTeam === 1 ? 2 : 1;

  useEffect(() => {
    if (!isVisible) return;

    // Reset timer when component becomes visible
    setTimeRemaining(displayDuration / 1000);
    setShowContinueButton(false);

    // Set up timer to count down
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Show continue button after 5 seconds
    const showContinueTimer = setTimeout(() => {
      setShowContinueButton(true);
    }, 5000);

    // Auto-close after display duration
    const closeTimer = setTimeout(() => {
      onClose();
    }, displayDuration);

    return () => {
      clearInterval(timer);
      clearTimeout(showContinueTimer);
      clearTimeout(closeTimer);
    };
  }, [isVisible, displayDuration, onClose]);

  // Generate appropriate message based on the outcome
  const getMessage = () => {
    if (!khanakCallerPlayer) return "";

    if (outcome === 'opposing_team_no_hands') {
      return `Team ${opposingTeam} (${teamNames[opposingTeam]}) has not won any hands this ball. Team ${khanakCallerTeam || 1} (${teamNames[khanakCallerTeam || 1]}) wins 4 balls!`;
    }

    if (outcome === 'no_jordhi_calls') {
      return `Team ${khanakCallerTeam || 1} (${teamNames[khanakCallerTeam || 1]}) has not called any Jodhi this ball. Team ${opposingTeam} (${teamNames[opposingTeam]}) wins 4 balls!`;
    }

    if (outcome === 'last_hand_lost') {
      return `${khanakCallerPlayer.name} called Khanak but Team ${khanakCallerTeam || 1} (${teamNames[khanakCallerTeam || 1]}) did not win the last hand. Team ${opposingTeam} (${teamNames[opposingTeam]}) wins 4 balls!`;
    }

    if (outcome === 'points_below_threshold') {
      return `${khanakCallerPlayer.name} called Khanak and Team ${opposingTeam} (${teamNames[opposingTeam]}) has ${opposingTeamPoints} points, which is less than the threshold of ${threshold}. Team ${khanakCallerTeam || 1} (${teamNames[khanakCallerTeam || 1]}) wins 3 balls!`;
    }

    if (outcome === 'points_above_threshold') {
      return `${khanakCallerPlayer.name} called Khanak but Team ${opposingTeam} (${teamNames[opposingTeam]}) has ${opposingTeamPoints} points, which is equal to or greater than the threshold of ${threshold}. Team ${opposingTeam} (${teamNames[opposingTeam]}) wins 4 balls!`;
    }

    return "";
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="khanak-results-container"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="khanak-results-card-wrapper"
          >
            <div className="khanak-results-card">
              <div className="khanak-results-header">
                <h2 className="khanak-results-title">Khanak Call Results</h2>
                <div className="khanak-results-timer">
                  {timeRemaining}s
                </div>
              </div>

              <div className="khanak-results-content">
                <div className="khanak-results-section">
                  <h3 className="khanak-results-section-title">Khanak Threshold Calculation</h3>
                  <div>
                    <div className="khanak-results-row">
                      <span>Team {khanakCallerTeam || 1} ({teamNames[khanakCallerTeam || 1]}) Jodhi Points:</span>
                      <span className="khanak-results-value">{teamJordhiPoints}</span>
                    </div>
                    <div className="khanak-results-row">
                      <span>Last Hand Bonus:</span>
                      <span className="khanak-results-value">+10</span>
                    </div>
                    <div className="khanak-results-row">
                      <span>Team {opposingTeam} ({teamNames[opposingTeam]}) Jodhi Points:</span>
                      <span className="khanak-results-value">-{opposingTeamJordhiPoints}</span>
                    </div>
                    <div className="khanak-results-divider khanak-results-row">
                      <span>Final Threshold:</span>
                      <span className="khanak-results-value-highlight">{threshold}</span>
                    </div>
                  </div>
                </div>

                {/* Display hands won by opposite team */}
                <div className="khanak-results-section">
                  <h3 className="khanak-results-section-title">
                    Hands Won by Team {opposingTeam} ({teamNames[opposingTeam]}):
                  </h3>
                  {opposingTeamHands.length === 0 ? (
                    <p style={{ color: '#9ca3af', fontStyle: 'italic', textAlign: 'center', padding: '1rem 0' }}>No hands won</p>
                  ) : (
                    <div className="khanak-results-hands-grid">
                      {opposingTeamHands.map((hand) => (
                        <div key={hand.handId} className="khanak-results-hand-card">
                          <div className="khanak-results-hand-header">
                            <span>Hand #{hand.handId}</span>
                            <span className="khanak-results-hand-winner">
                              Winner: {hand.winner?.name || `Player ${hand.winner?.id}`} ({hand.points || 0} pts)
                            </span>
                          </div>
                          <div className="khanak-results-cards-container">
                            {hand.cards?.map((card, index) => (
                              <div key={index} className="khanak-results-card-image">
                                <img
                                  src={getCardImagePath(card.value, card.suit)}
                                  alt={`${card.value} of ${card.suit}`}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Points Summary */}
                <div className="khanak-results-section">
                  <div className="khanak-results-summary">
                    Team {opposingTeam} ({teamNames[opposingTeam]}) needs less than {threshold} points for Team {khanakCallerTeam || 1} ({teamNames[khanakCallerTeam || 1]}) to win
                  </div>

                  <div className="khanak-results-row">
                    <span className="text-lg">Team {opposingTeam} ({teamNames[opposingTeam]}) Total Points:</span>
                    <span className={`khanak-results-points ${
                      opposingTeamPoints < threshold ? 'khanak-results-points-success' : 'khanak-results-points-failure'
                    }`}>
                      {opposingTeamPoints}
                    </span>
                  </div>

                  <div className="khanak-results-row">
                    <span className="text-lg">Khanak Threshold:</span>
                    <span className="khanak-results-value-highlight">{threshold}</span>
                  </div>

                  <div className="khanak-results-outcome">
                    {opposingTeamPoints < threshold
                      ? `Team ${opposingTeam} (${teamNames[opposingTeam]}) has ${opposingTeamPoints} points, which is less than the threshold of ${threshold}. Team ${khanakCallerTeam || 1} (${teamNames[khanakCallerTeam || 1]}) wins!`
                      : `Team ${opposingTeam} (${teamNames[opposingTeam]}) has ${opposingTeamPoints} points, which is equal to or greater than the threshold of ${threshold}. Team ${opposingTeam} (${teamNames[opposingTeam]}) wins!`}
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: '1.5rem' }}>
                <div className="khanak-results-final">
                  <h3 className="khanak-results-final-title">Final Result</h3>
                  <p className="khanak-results-final-result">
                    Team {winningTeam || 1} ({teamNames[winningTeam || 1]}) awarded {ballsAwarded} ball{ballsAwarded !== 1 ? 's' : ''}
                  </p>
                  <p className="khanak-results-final-description">
                    {getMessage()}
                  </p>
                </div>

                {showContinueButton && (
                  <button
                    onClick={() => {
                      if (onContinueGame) {
                        onContinueGame();
                      }
                      onClose();
                    }}
                    className="khanak-results-continue-button"
                    style={{ animation: 'pulse 2s infinite' }}
                  >
                    Continue Game
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
