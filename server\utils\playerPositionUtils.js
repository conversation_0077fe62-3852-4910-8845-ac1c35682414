/**
 * Player position utilities for Thunee
 *
 * In Thunee, we use fixed positions 1-4 where:
 * - Position 1: Top left player
 * - Position 2: Top right player
 * - Position 3: Bottom left player (dealer)
 * - Position 4: Bottom right player
 *
 * Team 1 consists of positions 1 and 3
 * Team 2 consists of positions 2 and 4
 *
 * Play moves counter-clockwise, so the player to the right of position X
 * is position (X % 4) + 1
 */

/**
 * Get the next player position in counter-clockwise order
 *
 * According to the specific rule set:
 * Position 1: Player 1 (Team A)
 * Position 2: Player 3 (Team B)
 * Position 3: Player 2 (Team A)
 * Position 4: Player 4 (Team B)
 *
 * Play order:
 * - If Player 1 (position 1) plays first → Player 3 (position 2) → Player 2 (position 3) → Player 4 (position 4)
 * - If Player 3 (position 2) plays first → Player 2 (position 3) → Player 4 (position 4) → Player 1 (position 1)
 * - If Player 2 (position 3) plays first → Player 4 (position 4) → Player 1 (position 1) → Player 3 (position 2)
 * - If Player 4 (position 4) plays first → Player 1 (position 1) → Player 3 (position 2) → Player 2 (position 3)
 */
function getPositionToRight(position) {
  // Ensure position is a number between 1 and 4
  const pos = Number(position);
  if (isNaN(pos) || pos < 1 || pos > 4) {
    console.error(`Invalid position: ${position}`);
    return 1; // Default to position 1 if invalid
  }

  // Map of positions to the next position in counter-clockwise order
  // Based on the specific rule set provided
  const nextPositions = {
    1: 2, // From Player 1 (position 1) to Player 3 (position 2)
    2: 3, // From Player 3 (position 2) to Player 2 (position 3)
    3: 4, // From Player 2 (position 3) to Player 4 (position 4)
    4: 1  // From Player 4 (position 4) to Player 1 (position 1)
  };

  console.log(`Getting next position after ${pos}: ${nextPositions[pos]}`);
  return nextPositions[pos];
}

/**
 * Get the previous player position in clockwise order
 *
 * According to the specific rule set:
 * Position 1: Player 1 (Team A)
 * Position 2: Player 3 (Team B)
 * Position 3: Player 2 (Team A)
 * Position 4: Player 4 (Team B)
 *
 * Reverse play order:
 * - Position 1 (Player 1) -> Position 4 (Player 4) is previous
 * - Position 2 (Player 3) -> Position 1 (Player 1) is previous
 * - Position 3 (Player 2) -> Position 2 (Player 3) is previous
 * - Position 4 (Player 4) -> Position 3 (Player 2) is previous
 */
function getPositionToLeft(position) {
  // Ensure position is a number between 1 and 4
  const pos = Number(position);
  if (isNaN(pos) || pos < 1 || pos > 4) {
    console.error(`Invalid position: ${position}`);
    return 1; // Default to position 1 if invalid
  }

  // Map of positions to the previous position in clockwise order
  // Based on the specific rule set provided
  const previousPositions = {
    1: 4, // From Player 1 (position 1) to Player 4 (position 4)
    2: 1, // From Player 3 (position 2) to Player 1 (position 1)
    3: 2, // From Player 2 (position 3) to Player 3 (position 2)
    4: 3  // From Player 4 (position 4) to Player 2 (position 3)
  };

  console.log(`Getting previous position before ${pos}: ${previousPositions[pos]}`);
  return previousPositions[pos];
}

/**
 * Get the position of the partner player
 *
 * According to the specific rule set:
 * - Position 1 (Player 1, Team A) is partnered with Position 3 (Player 2, Team A)
 * - Position 2 (Player 3, Team B) is partnered with Position 4 (Player 4, Team B)
 * - Position 3 (Player 2, Team A) is partnered with Position 1 (Player 1, Team A)
 * - Position 4 (Player 4, Team B) is partnered with Position 2 (Player 3, Team B)
 */
function getOppositePosition(position) {
  // Ensure position is a number between 1 and 4
  const pos = Number(position);
  if (isNaN(pos) || pos < 1 || pos > 4) {
    console.error(`Invalid position: ${position}`);
    return 1; // Default to position 1 if invalid
  }

  // Map of positions to their partner positions
  const partnerPositions = {
    1: 3, // Player 1 (Team A) <-> Player 2 (Team A)
    2: 4, // Player 3 (Team B) <-> Player 4 (Team B)
    3: 1, // Player 2 (Team A) <-> Player 1 (Team A)
    4: 2  // Player 4 (Team B) <-> Player 3 (Team B)
  };

  console.log(`Getting partner position for ${pos}: ${partnerPositions[pos]}`);
  return partnerPositions[pos];
}

/**
 * Get the team number (1 or 2) for a given position
 *
 * According to the specific rule set:
 * - Team A (1): positions 1 and 3 (Player 1 and Player 2)
 * - Team B (2): positions 2 and 4 (Player 3 and Player 4)
 */
function getTeamForPosition(position) {
  // Team A (1): positions 1 and 3 (Player 1 and Player 2)
  // Team B (2): positions 2 and 4 (Player 3 and Player 4)
  const teamMap = {
    1: 1, // Player 1 - Team A
    2: 2, // Player 3 - Team B
    3: 1, // Player 2 - Team A
    4: 2  // Player 4 - Team B
  };

  return teamMap[position] || 1; // Default to team 1 if invalid
}

/**
 * Get the partner position for a given position
 *
 * According to the specific rule set:
 * - Position 1 (Player 1, Team A) is partnered with Position 3 (Player 2, Team A)
 * - Position 2 (Player 3, Team B) is partnered with Position 4 (Player 4, Team B)
 * - Position 3 (Player 2, Team A) is partnered with Position 1 (Player 1, Team A)
 * - Position 4 (Player 4, Team B) is partnered with Position 2 (Player 3, Team B)
 */
function getPartnerPosition(position) {
  // This is the same as getting the opposite position
  return getOppositePosition(position);
}

/**
 * Get the dealer position (can be any position 1-4)
 * This is a placeholder as the dealer can be any player
 */
function getDealerPosition() {
  // In this implementation, the dealer can be any player
  // The actual dealer is determined during the game
  return 3; // Default to position 3 for compatibility
}

/**
 * Get the first player position after trump selection
 *
 * According to the specific rule set:
 * - If Player 1 (position 1) selects trump → Player 3 (position 2) plays first
 * - If Player 2 (position 3) selects trump → Player 4 (position 4) plays first
 * - If Player 3 (position 2) selects trump → Player 2 (position 3) plays first
 * - If Player 4 (position 4) selects trump → Player 1 (position 1) plays first
 *
 * @param {number} trumperPosition - The position of the player who selected trump
 * @returns {number} - The position of the player who should play first
 */
function getFirstPlayerAfterTrump(trumperPosition) {
  // Ensure position is a number between 1 and 4
  const pos = Number(trumperPosition);
  if (isNaN(pos) || pos < 1 || pos > 4) {
    console.error(`Invalid trumper position: ${trumperPosition}`);
    return 1; // Default to position 1 if invalid
  }

  // Map of trumper positions to first player positions
  const firstPlayerMap = {
    1: 2, // If Player 1 (position 1) selects trump → Player 3 (position 2) plays first
    3: 4, // If Player 2 (position 3) selects trump → Player 4 (position 4) plays first
    2: 3, // If Player 3 (position 2) selects trump → Player 2 (position 3) plays first
    4: 1  // If Player 4 (position 4) selects trump → Player 1 (position 1) plays first
  };

  console.log(`Getting first player after trump selection by player at position ${pos}: ${firstPlayerMap[pos]}`);
  return firstPlayerMap[pos];
}

/**
 * Get the initial trumper position based on the dealer position
 *
 * In Thunee, the player to the right of the dealer selects trump.
 * Since the dealer is always at position 3 (bottom left), the trumper
 * is always at position 2 (top right).
 *
 * @param {number} dealerPosition - The position of the dealer (should always be 3)
 * @returns {number} - The position of the initial trumper (always 2)
 */
function getInitialTrumperPosition(dealerPosition) {
  // In our fixed position system, the dealer is always at position 3
  // and the trumper is always at position 2 (to the right of dealer)

  // Log the input for debugging
  console.log(`Getting initial trumper position for dealer at position ${dealerPosition}`);

  // For backward compatibility, check if a position was provided
  const pos = Number(dealerPosition);
  if (!isNaN(pos) && pos >= 1 && pos <= 4) {
    // If a valid position was provided, use the map for backward compatibility
    const trumperMap = {
      1: 2, // If Player 1 is dealer → Player 3 (position 2) selects trump
      2: 4, // If Player 2 is dealer → Player 4 (position 4) selects trump
      3: 2, // If Player 3 is dealer → Player 2 (position 2) selects trump
      4: 1  // If Player 4 is dealer → Player 1 (position 1) selects trump
    };

    console.log(`Using compatibility map: trumper position for dealer at position ${pos} is ${trumperMap[pos]}`);
    return trumperMap[pos];
  }

  // In the standard case, the dealer is at position 3 and the trumper is at position 2
  console.log('Using standard position: trumper is at position 2 (top right)');
  return 2;
}

/**
 * Assign fixed positions to players based on the dealer
 *
 * According to the specific rule set:
 * - Position 3: Dealer (always bottom left)
 * - Position 1: Dealer's partner (always top left)
 * - Positions 2 and 4: Opposing team players (top right and bottom right)
 *
 * @param {Array} players - Array of player objects
 * @param {String} dealerId - ID of the dealer
 * @returns {Array} - Array of players with positions assigned
 */
function assignPositionsBasedOnDealer(players, dealerId) {
  if (!dealerId || players.length !== 4) {
    console.error('Cannot assign positions: Invalid dealer ID or player count');
    return players;
  }

  // Find the dealer
  const dealerIndex = players.findIndex(p => p.id === dealerId);
  if (dealerIndex === -1) {
    console.error(`Dealer with ID ${dealerId} not found in players list`);
    return players;
  }

  // Get the dealer's team
  const dealerTeam = players[dealerIndex].team;
  if (!dealerTeam) {
    console.error('Dealer has no team assigned');
    return players;
  }

  // Create a copy of the players array
  const positionedPlayers = [...players];

  // Get the dealer
  const dealer = players[dealerIndex];

  // Find the dealer's partner (same team, but not the dealer)
  const dealerPartner = players.find(p => p.team === dealerTeam && p.id !== dealerId);
  if (!dealerPartner) {
    console.error('Dealer partner not found');
    return players;
  }

  // Find the opposing team players
  const opposingTeam = dealerTeam === 1 ? 2 : 1;
  const opposingTeamPlayers = players.filter(p => p.team === opposingTeam);
  if (opposingTeamPlayers.length !== 2) {
    console.error('Invalid opposing team distribution');
    return players;
  }

  // Assign positions according to the specific rule set
  // Position 3: Dealer (bottom left)
  dealer.position = 3;

  // Position 1: Dealer's partner (top left)
  dealerPartner.position = 1;

  // Position 2: First opposing team player (top right)
  opposingTeamPlayers[0].position = 2;

  // Position 4: Second opposing team player (bottom right)
  opposingTeamPlayers[1].position = 4;

  console.log('Assigned positions to players:',
    positionedPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`)
  );

  return positionedPlayers;
}

module.exports = {
  getPositionToRight,
  getPositionToLeft,
  getOppositePosition,
  getTeamForPosition,
  getPartnerPosition,
  getDealerPosition,
  getInitialTrumperPosition,
  getFirstPlayerAfterTrump,
  assignPositionsBasedOnDealer
};
