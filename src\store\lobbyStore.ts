import { create } from 'zustand';
import gameService from '../services/gameService';
import { useTimeSettingsStore } from './timeSettingsStore';

export type Player = {
  id: string;
  name: string;
  avatar: string;
  isReady?: boolean;
  isHost?: boolean;
  team?: number; // 1 or 2
};

export type LobbyState = {
  lobbyCode: string | null;
  partnerInviteCode: string | null; // New code for inviting a partner (Team 1)
  opponentInviteCode: string | null; // New code for inviting opponents (Team 2)
  players: Player[];
  playerName: string;
  teamName: string;
  isHost: boolean;
  isConnected: boolean;
  isGameStarted: boolean;
  teamReady: {
    1: boolean;
    2: boolean;
  };
  teamNames: {
    1: string;
    2: string;
  };
  isFindingMatch: boolean;
  matchedLobby: string | null;
  matchedTeam: Player[] | null;
  matchedTeamName: string | null;
  error: string | null;
};

export type LobbyActions = {
  setPlayerName: (name: string) => void;
  setTeamName: (name: string) => void;
  updateTeamName: (teamNumber: 1 | 2, name: string) => Promise<void>;
  createLobby: () => Promise<string>;
  joinLobby: (code: string) => Promise<void>;
  startGame: () => Promise<void>;
  leaveLobby: () => void;
  updatePlayers: (players: Player[]) => void;
  setTeamReady: (ready: boolean) => Promise<void>;
  switchTeam: () => Promise<void>;
  findMatch: () => Promise<void>;
  cancelFindMatch: () => Promise<void>;
  setError: (error: string | null) => void;
  setGameStarted: (started: boolean) => void;
  setLobbyCode: (code: string) => void; // Added for spectator mode
  reset: () => void;
};

// Initial state
const initialState: LobbyState = {
  lobbyCode: null,
  partnerInviteCode: null,
  opponentInviteCode: null,
  players: [],
  playerName: '',
  teamName: '',
  isHost: false,
  isConnected: false,
  isGameStarted: false,
  teamReady: {
    1: false,
    2: false
  },
  teamNames: {
    1: 'Team 1',
    2: 'Team 2'
  },
  isFindingMatch: false,
  matchedLobby: null,
  matchedTeam: null,
  matchedTeamName: null,
  error: null,
};

// Create the store
export const useLobbyStore = create<LobbyState & LobbyActions>((set, get) => ({
  ...initialState,

  setPlayerName: (name) => set({ playerName: name }),

  setTeamName: (name) => set({ teamName: name }),

  updateTeamName: async (teamNumber, name) => {
    const { lobbyCode, isHost } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    if (!isHost) {
      set({ error: 'Only the host can update team names' });
      return Promise.reject('Only the host can update team names');
    }

    try {
      await gameService.updateTeamName(lobbyCode, teamNumber, name);

      // Update local state (will be overwritten by server event, but this makes the UI more responsive)
      set(state => ({
        teamNames: {
          ...state.teamNames,
          [teamNumber]: name
        }
      }));

      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to update team name' });
      return Promise.reject(error);
    }
  },

  createLobby: async () => {
    const { playerName, teamName } = get();

    if (!playerName) {
      set({ error: 'Please enter a player name' });
      return Promise.reject('Please enter a player name');
    }

    try {
      // Connect to the game service
      await gameService.connect(playerName);

      // Get the current user's time settings to send to the server
      const timeSettings = useTimeSettingsStore.getState().settings;

      // Create a new lobby with team name and time settings
      const response = await gameService.createLobby(playerName, teamName, timeSettings);
      const { lobbyCode, partnerInviteCode, opponentInviteCode } = response;

      // Set up event listeners
      setupEventListeners(set, get);

      // Update state
      set({
        lobbyCode,
        partnerInviteCode,
        opponentInviteCode,
        isHost: true,
        isConnected: true,
        players: [{
          id: gameService.getSocketId() || 'host',
          name: playerName,
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${playerName}`,
          isHost: true,
          team: 1
        }],
        teamNames: {
          1: teamName || 'Team 1',
          2: 'Team 2'
        },
        error: null
      });

      return lobbyCode;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to create lobby' });
      return Promise.reject(error);
    }
  },

  joinLobby: async (code) => {
    const { playerName } = get();

    if (!playerName) {
      set({ error: 'Please enter a player name' });
      return Promise.reject('Please enter a player name');
    }

    try {
      // Connect to the game service
      await gameService.connect(playerName);

      // Join the lobby
      const response = await gameService.joinLobby(code, playerName);

      // Set up event listeners
      setupEventListeners(set, get);

      // Update state
      set({
        lobbyCode: response.actualLobbyCode || code, // Use the actual lobby code if this was an invite code
        isHost: false,
        isConnected: true,
        error: null
      });

      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to join lobby' });
      return Promise.reject(error);
    }
  },

  startGame: async () => {
    const { lobbyCode, isHost } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    if (!isHost) {
      set({ error: 'Only the host can start the game' });
      return Promise.reject('Only the host can start the game');
    }

    try {
      await gameService.startGame(lobbyCode);
      set({ isGameStarted: true, error: null });
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to start game' });
      return Promise.reject(error);
    }
  },

  leaveLobby: () => {
    gameService.disconnect();
    set(initialState);
  },

  updatePlayers: (players) => set({ players }),

  setTeamReady: async (ready) => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      await gameService.setTeamReady(lobbyCode, ready);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to set team ready status' });
      return Promise.reject(error);
    }
  },

  switchTeam: async () => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      await gameService.switchTeam(lobbyCode);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to switch team' });
      return Promise.reject(error);
    }
  },

  findMatch: async () => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      set({ isFindingMatch: true, error: null });
      await gameService.findMatch(lobbyCode);
      return Promise.resolve();
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to find match',
        isFindingMatch: false
      });
      return Promise.reject(error);
    }
  },

  cancelFindMatch: async () => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      set({ isFindingMatch: false, error: null });
      await gameService.cancelFindMatch(lobbyCode);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to cancel match finding' });
      return Promise.reject(error);
    }
  },

  setError: (error) => set({ error }),

  setGameStarted: (started) => set({ isGameStarted: started }),

  setLobbyCode: (code) => set({ lobbyCode: code }),

  reset: () => {
    gameService.disconnect();
    set(initialState);
  }
}));

// Set up SignalR event listeners
function setupEventListeners(
  set: (state: Partial<LobbyState>) => void,
  get: () => LobbyState & LobbyActions
) {
  // Listen for player updates
  gameService.on('PlayersUpdated', (data: { players: Player[], teams?: Record<string, Player[]>, teamReady?: { 1: boolean, 2: boolean } }) => {
    const updateData: Partial<LobbyState> = { players: data.players };

    if (data.teamReady) {
      updateData.teamReady = data.teamReady;
    }

    set(updateData);
  });

  // Listen for team ready updates
  gameService.on('TeamReadyUpdated', (data: { teamReady: { 1: boolean, 2: boolean }, players: Player[] }) => {
    set({
      teamReady: data.teamReady,
      players: data.players
    });
  });

  // Listen for team name updates
  gameService.on('TeamNamesUpdated', (data: { teamNames: { 1: string, 2: string } }) => {
    set({
      teamNames: data.teamNames
    });
  });

  // Listen for game start
  gameService.on('GameStarted', () => {
    set({ isGameStarted: true, isFindingMatch: false });
  });

  // Listen for match status updates
  gameService.on('MatchStatusUpdate', (data: { isFindingMatch: boolean, matchedLobby: string | null, matchedTeam: Player[] | null, matchedTeamName: string | null }) => {
    console.log('Match status update:', data);
    // Use a timeout to ensure this runs after any other state updates
    setTimeout(() => {
      set({
        isFindingMatch: data.isFindingMatch,
        matchedLobby: data.matchedLobby,
        matchedTeam: data.matchedTeam,
        matchedTeamName: data.matchedTeamName
      });

      // Force a refresh of the page if we have a match
      if (data.matchedLobby && data.matchedTeam) {
        console.log('Match confirmed via status update, forcing UI update');
        // Trigger a UI refresh by toggling a state value
        window.dispatchEvent(new Event('storage')); // Hack to force React to re-render
      }
    }, 500); // Increased timeout for more reliability
  });

  // Listen for match found
  gameService.on('MatchFound', (data: { matchedLobby: string, matchedTeam: Player[], matchedTeamName: string }) => {
    console.log('Match found!', data);

    // Use a timeout to ensure state updates properly
    setTimeout(() => {
      set({
        matchedLobby: data.matchedLobby,
        matchedTeam: data.matchedTeam,
        matchedTeamName: data.matchedTeamName,
        isFindingMatch: false
      });

      console.log('Match found event processed, forcing UI update');
      // Trigger a UI refresh
      window.dispatchEvent(new Event('storage')); // Hack to force React to re-render
    }, 500);
  });

  // Listen for match canceled
  gameService.on('MatchCanceled', () => {
    console.log('Match finding canceled');
    set({
      matchedLobby: null,
      matchedTeam: null,
      matchedTeamName: null,
      isFindingMatch: false
    });
  });

  // Listen for players updated with finding match status
  gameService.on('PlayersUpdated', (data: { players: Player[], isFindingMatch?: boolean }) => {
    console.log('Players updated with finding match status:', data);
    if (data.isFindingMatch !== undefined) {
      set({
        players: data.players,
        isFindingMatch: data.isFindingMatch
      });
    } else {
      set({
        players: data.players
      });
    }
  });

  // Listen for errors
  gameService.on('Error', (data: { message: string }) => {
    set({ error: data.message });
  });

  // Listen for disconnection
  gameService.on('Disconnected', () => {
    set({ isConnected: false });
  });
}

export default useLobbyStore;
