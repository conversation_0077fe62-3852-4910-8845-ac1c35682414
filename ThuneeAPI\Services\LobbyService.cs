using System.Collections.Concurrent;
using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class LobbyService : ILobbyService
    {
        private readonly ConcurrentDictionary<string, Lobby> _lobbies = new();
        private readonly ConcurrentDictionary<string, string> _socketToLobby = new();
        private readonly ConcurrentDictionary<string, GameLobby> _gameLobbies = new();
        private readonly ConcurrentDictionary<string, string> _socketToGameLobby = new();
        private readonly List<string> _matchQueue = new();
        private readonly object _matchQueueLock = new();
        private readonly ITimeframeService _timeframeService;

        // Store invite code mappings
        private readonly ConcurrentDictionary<string, InviteCodeInfo> _inviteCodes = new();

        public LobbyService(ITimeframeService timeframeService)
        {
            _timeframeService = timeframeService;
        }

        // Events
        public event Func<string, PlayersUpdatedResponse, Task>? PlayersUpdated;
        public event Func<string, TeamNamesUpdatedResponse, Task>? TeamNamesUpdated;
        public event Func<string, TeamReadyUpdatedResponse, Task>? TeamReadyUpdated;
        public event Func<string, GameStartedResponse, Task>? GameStarted;
        public event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        public event Func<string, MatchFoundResponse, Task>? MatchFound;
        public event Func<string, MatchStatusUpdateResponse, Task>? MatchStatusUpdated;

        public class InviteCodeInfo
        {
            public string LobbyCode { get; set; } = string.Empty;
            public int TeamToJoin { get; set; }
        }

        public async Task<CreateLobbyResponse> CreateLobbyAsync(string connectionId, string playerName, string? teamName = null, TimeSettings? timeSettings = null)
        {
            var lobbyCode = GenerateLobbyCode();
            var partnerInviteCode = GenerateLobbyCode();
            var opponentInviteCode = GenerateLobbyCode();

            var team1Name = teamName ?? "Team 1";

            var defaultTimeSettings = new TimeSettings
            {
                PlayTimeframeOptions = new List<int> { 3, 4, 5, 6, 60 },
                VotingTimeLimit = 15,
                ThuneeCallingDurations = new ThuneeCallingDurations
                {
                    Trumper = 5,
                    FirstRemaining = 3,
                    LastRemaining = 2
                },
                TrumpDisplayDuration = 10,
                CardDealingSpeed = 300,
                TimerUpdateInterval = 100
            };

            var player = new Player
            {
                Id = connectionId,
                Name = playerName,
                Avatar = GetAvatarUrl(playerName),
                IsHost = true,
                Team = 1,
                IsReady = false
            };

            var lobby = new Lobby
            {
                LobbyCode = lobbyCode,
                PartnerInviteCode = partnerInviteCode,
                OpponentInviteCode = opponentInviteCode,
                Players = new List<Player> { player },
                GameStarted = false,
                Teams = new Dictionary<int, List<Player>>
                {
                    { 1, new List<Player> { player } },
                    { 2, new List<Player>() }
                },
                TeamNames = new Dictionary<int, string>
                {
                    { 1, team1Name },
                    { 2, "Team 2" }
                },
                TeamReady = new Dictionary<int, bool>
                {
                    { 1, false },
                    { 2, false }
                },
                IsFindingMatch = false,
                MatchedLobby = null,
                ReadyToStart = false,
                TimeSettings = timeSettings ?? defaultTimeSettings,
                HostId = connectionId,
                BallScores = new Dictionary<string, int>
                {
                    { "team1", 0 },
                    { "team2", 0 }
                },
                BallPoints = new Dictionary<string, int>
                {
                    { "team1", 0 },
                    { "team2", 0 }
                }
            };

            _lobbies[lobbyCode] = lobby;
            _socketToLobby[connectionId] = lobbyCode;

            // Store invite code mappings
            _inviteCodes[partnerInviteCode] = new InviteCodeInfo { LobbyCode = lobbyCode, TeamToJoin = 1 };
            _inviteCodes[opponentInviteCode] = new InviteCodeInfo { LobbyCode = lobbyCode, TeamToJoin = 2 };

            return new CreateLobbyResponse
            {
                LobbyCode = lobbyCode,
                PartnerInviteCode = partnerInviteCode,
                OpponentInviteCode = opponentInviteCode
            };
        }

        public async Task<JoinLobbyResponse> JoinLobbyAsync(string connectionId, string lobbyCode, string playerName)
        {
            // Check if the code is an invite code
            if (_inviteCodes.TryGetValue(lobbyCode, out var inviteInfo))
            {
                var actualLobbyCode = inviteInfo.LobbyCode;
                var teamToJoin = inviteInfo.TeamToJoin;

                if (!_lobbies.TryGetValue(actualLobbyCode, out var lobby))
                {
                    throw new InvalidOperationException("Lobby not found");
                }

                if (lobby.GameStarted)
                {
                    throw new InvalidOperationException("Game already in progress");
                }

                if (lobby.Teams[teamToJoin].Count >= 2)
                {
                    throw new InvalidOperationException($"Team {teamToJoin} is already full");
                }

                var player = new Player
                {
                    Id = connectionId,
                    Name = playerName,
                    Avatar = GetAvatarUrl(playerName),
                    IsHost = false,
                    Team = teamToJoin,
                    IsReady = false
                };

                lobby.Players.Add(player);
                lobby.Teams[teamToJoin].Add(player);
                _socketToLobby[connectionId] = actualLobbyCode;

                // Notify all players in the lobby
                if (PlayersUpdated != null)
                {
                    await PlayersUpdated(actualLobbyCode, new PlayersUpdatedResponse
                    {
                        Players = lobby.Players,
                        Teams = lobby.Teams,
                        TeamReady = lobby.TeamReady
                    });
                }

                return new JoinLobbyResponse
                {
                    ActualLobbyCode = actualLobbyCode,
                    IsInviteCode = true
                };
            }

            // Regular lobby code handling
            if (!_lobbies.TryGetValue(lobbyCode, out var regularLobby))
            {
                throw new InvalidOperationException("Lobby not found");
            }

            if (regularLobby.GameStarted)
            {
                throw new InvalidOperationException("Game already in progress");
            }

            if (regularLobby.Players.Count >= 4)
            {
                throw new InvalidOperationException("Lobby is full");
            }

            // Determine team (ensure each team has exactly 2 players)
            var team1Count = regularLobby.Teams[1].Count;
            var team2Count = regularLobby.Teams[2].Count;

            // If team 1 has less than 2 players, assign to team 1
            // Otherwise, assign to team 2
            var team = team1Count < 2 ? 1 : 2;

            // Check if lobby is full (both teams have 2 players)
            if (team1Count >= 2 && team2Count >= 2)
            {
                throw new InvalidOperationException("Lobby is full (both teams have 2 players)");
            }

            var regularPlayer = new Player
            {
                Id = connectionId,
                Name = playerName,
                Avatar = GetAvatarUrl(playerName),
                IsHost = false,
                Team = team,
                IsReady = false
            };

            regularLobby.Players.Add(regularPlayer);
            regularLobby.Teams[team].Add(regularPlayer);
            _socketToLobby[connectionId] = lobbyCode;

            // Notify all players in the lobby
            if (PlayersUpdated != null)
            {
                await PlayersUpdated(lobbyCode, new PlayersUpdatedResponse
                {
                    Players = regularLobby.Players,
                    Teams = regularLobby.Teams,
                    TeamReady = regularLobby.TeamReady
                });
            }

            return new JoinLobbyResponse
            {
                ActualLobbyCode = lobbyCode,
                IsInviteCode = false
            };
        }

        public async Task<bool> UpdateTeamNameAsync(string connectionId, string? lobbyCode, int teamNumber, string teamName)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null || !player.IsHost) return false;

            if (teamNumber != 1 && teamNumber != 2) return false;

            lobby.TeamNames[teamNumber] = teamName ?? $"Team {teamNumber}";

            // Trigger event
            if (TeamNamesUpdated != null)
            {
                await TeamNamesUpdated(lobby.LobbyCode, new TeamNamesUpdatedResponse
                {
                    TeamNames = lobby.TeamNames
                });
            }

            return true;
        }

        public async Task<bool> SetTeamReadyAsync(string connectionId, string? lobbyCode, bool ready)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            player.IsReady = ready;

            // Check if all players in the team are ready
            var teamPlayers = lobby.Teams[player.Team];
            var allTeamPlayersReady = teamPlayers.Count > 0 && teamPlayers.All(p => p.IsReady);

            lobby.TeamReady[player.Team] = allTeamPlayersReady;

            // Trigger event
            if (TeamReadyUpdated != null)
            {
                await TeamReadyUpdated(lobby.LobbyCode, new TeamReadyUpdatedResponse
                {
                    TeamReady = lobby.TeamReady,
                    Players = lobby.Players
                });
            }

            // Check if both teams are ready to start the game
            if (lobby.TeamReady[1] && lobby.TeamReady[2] &&
                lobby.Teams[1].Count == 2 && lobby.Teams[2].Count == 2)
            {
                lobby.GameStarted = true;

                // Trigger game started event
                if (GameStarted != null)
                {
                    await GameStarted(lobby.LobbyCode, new GameStartedResponse
                    {
                        Players = lobby.Players,
                        Teams = lobby.Teams
                    });
                }

                // Initialize timeframe voting
                _timeframeService.InitTimeframeVoting(lobby);

                // Trigger game phase updated event
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "play-timeframe-voting",
                        Players = lobby.Players
                    });
                }
            }

            return true;
        }

        public async Task<bool> SwitchTeamAsync(string connectionId, string? lobbyCode)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Don't allow switching if game has started
            if (lobby.GameStarted) return false;

            // Remove player from current team
            var currentTeam = player.Team;
            var newTeam = currentTeam == 1 ? 2 : 1;

            // Check if switching would result in more than 2 players in the new team
            if (lobby.Teams[newTeam].Count >= 2) return false;

            // Check if switching would leave the current team empty
            if (lobby.Teams[currentTeam].Count <= 1) return false;

            var teamIndex = lobby.Teams[currentTeam].FindIndex(p => p.Id == connectionId);
            if (teamIndex != -1)
            {
                lobby.Teams[currentTeam].RemoveAt(teamIndex);
            }

            // Add player to new team
            lobby.Teams[newTeam].Add(player);

            // Update player's team
            player.Team = newTeam;

            // Reset team ready status when someone switches teams
            lobby.TeamReady[currentTeam] = false;
            lobby.TeamReady[newTeam] = false;

            // Reset all players' ready status in both teams
            foreach (var teamPlayer in lobby.Teams[currentTeam])
            {
                teamPlayer.IsReady = false;
            }
            foreach (var teamPlayer in lobby.Teams[newTeam])
            {
                teamPlayer.IsReady = false;
            }

            // Notify all players in the lobby
            if (PlayersUpdated != null)
            {
                await PlayersUpdated(lobby.LobbyCode, new PlayersUpdatedResponse
                {
                    Players = lobby.Players,
                    Teams = lobby.Teams,
                    TeamReady = lobby.TeamReady
                });
            }

            return true;
        }

        public Task<bool> StartGameAsync(string connectionId, string? lobbyCode)
        {
            // Implementation for starting game
            return Task.FromResult(false); // Placeholder
        }

        public async Task<bool> FindMatchAsync(string connectionId, string? lobbyCode)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null || !player.IsHost) return false;

            if (lobby.Teams[1].Count != 2) return false;

            lobby.IsFindingMatch = true;
            AddToMatchQueue(lobby.LobbyCode);

            await CheckForMatchesAsync();

            return true;
        }

        public Task<bool> CancelFindMatchAsync(string connectionId, string? lobbyCode)
        {
            var lobby = GetLobbyByConnectionId(connectionId) ?? GetLobby(lobbyCode ?? "");
            if (lobby == null) return Task.FromResult(false);

            lobby.IsFindingMatch = false;
            RemoveFromMatchQueue(lobby.LobbyCode);

            return Task.FromResult(true);
        }

        public Lobby? GetLobby(string lobbyCode)
        {
            _lobbies.TryGetValue(lobbyCode, out var lobby);
            return lobby;
        }

        public Lobby? GetLobbyByConnectionId(string connectionId)
        {
            if (_socketToLobby.TryGetValue(connectionId, out var lobbyCode))
            {
                return GetLobby(lobbyCode);
            }
            return null;
        }

        public string? GetLobbyCodeByConnectionId(string connectionId)
        {
            _socketToLobby.TryGetValue(connectionId, out var lobbyCode);
            return lobbyCode;
        }

        public void SetLobbyForConnection(string connectionId, string lobbyCode)
        {
            _socketToLobby[connectionId] = lobbyCode;
        }

        public void RemoveConnectionFromLobby(string connectionId)
        {
            _socketToLobby.TryRemove(connectionId, out _);
        }

        public void AddToMatchQueue(string lobbyCode)
        {
            lock (_matchQueueLock)
            {
                if (!_matchQueue.Contains(lobbyCode))
                {
                    _matchQueue.Add(lobbyCode);
                }
            }
        }

        public void RemoveFromMatchQueue(string lobbyCode)
        {
            lock (_matchQueueLock)
            {
                _matchQueue.Remove(lobbyCode);
            }
        }

        public async Task CheckForMatchesAsync()
        {
            List<string> matchedLobbies = new List<string>();

            lock (_matchQueueLock)
            {
                if (_matchQueue.Count >= 2)
                {
                    // Take the first two lobbies from the queue
                    var lobby1Code = _matchQueue[0];
                    var lobby2Code = _matchQueue[1];

                    matchedLobbies.Add(lobby1Code);
                    matchedLobbies.Add(lobby2Code);

                    // Remove them from the queue
                    _matchQueue.RemoveAt(0);
                    _matchQueue.RemoveAt(0);
                }
            }

            // Process matches outside the lock
            for (int i = 0; i < matchedLobbies.Count; i += 2)
            {
                if (i + 1 < matchedLobbies.Count)
                {
                    await ProcessMatchAsync(matchedLobbies[i], matchedLobbies[i + 1]);
                }
            }
        }

        private async Task ProcessMatchAsync(string lobby1Code, string lobby2Code)
        {
            var lobby1 = GetLobby(lobby1Code);
            var lobby2 = GetLobby(lobby2Code);

            if (lobby1 == null || lobby2 == null) return;

            // Create game lobby
            var gameLobby = CreateGameLobby(lobby1Code, lobby2Code);
            if (gameLobby == null) return;

            // Combine all players from both lobbies
            var allPlayers = new List<Player>();
            allPlayers.AddRange(lobby1.Teams[1]);
            allPlayers.AddRange(lobby2.Teams[1]);

            // Assign team 2 to the matched lobby's players
            foreach (var player in lobby2.Teams[1])
            {
                player.Team = 2;
            }

            // Sort by ID for consistent order across clients
            allPlayers = allPlayers.OrderBy(p => p.Id).ToList();

            // Update both lobbies with combined player data
            lobby1.Players = allPlayers;
            lobby2.Players = allPlayers;

            // Set up teams
            var teams = new Dictionary<int, List<Player>>
            {
                { 1, lobby1.Teams[1] },
                { 2, lobby2.Teams[1] }
            };

            var teamNames = new Dictionary<int, string>
            {
                { 1, lobby1.TeamNames[1] },
                { 2, lobby2.TeamNames[1] }
            };

            // Mark both lobbies as matched
            lobby1.IsFindingMatch = false;
            lobby2.IsFindingMatch = false;
            lobby1.GameStarted = true;
            lobby2.GameStarted = true;

            // Notify match found
            if (MatchFound != null)
            {
                var matchData = new MatchFoundResponse
                {
                    Players = allPlayers,
                    Teams = teams,
                    TeamNames = teamNames,
                    GameLobbyCode = gameLobby.GameLobbyCode
                };

                await MatchFound(lobby1Code, matchData);
                await MatchFound(lobby2Code, matchData);
            }

            // Initialize timeframe voting for both lobbies
            _timeframeService.InitTimeframeVoting(lobby1);
            _timeframeService.InitTimeframeVoting(lobby2);

            // Transition to dealer determination phase
            if (GamePhaseUpdated != null)
            {
                var phaseData = new GamePhaseUpdatedResponse
                {
                    Phase = "dealer-determination",
                    Players = allPlayers
                };

                await GamePhaseUpdated(lobby1Code, phaseData);
                await GamePhaseUpdated(lobby2Code, phaseData);
            }
        }

        public string GenerateLobbyCode()
        {
            const string characters = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
            var random = new Random();
            var result = new char[6];

            for (int i = 0; i < 6; i++)
            {
                result[i] = characters[random.Next(characters.Length)];
            }

            return new string(result);
        }

        public string GetAvatarUrl(string name)
        {
            return $"https://api.dicebear.com/7.x/avataaars/svg?seed={name}";
        }

        public GameLobby? CreateGameLobby(string lobby1Code, string lobby2Code)
        {
            var lobby1 = GetLobby(lobby1Code);
            var lobby2 = GetLobby(lobby2Code);

            if (lobby1 == null || lobby2 == null) return null;

            var gameLobbyCode = GenerateLobbyCode();

            var gameLobby = new GameLobby
            {
                GameLobbyCode = gameLobbyCode,
                LobbyCode = gameLobbyCode,
                Lobby1Code = lobby1Code,
                Lobby2Code = lobby2Code,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _gameLobbies[gameLobbyCode] = gameLobby;

            // Set game lobby references for both lobbies
            SetGameLobbyForConnection(lobby1Code, gameLobbyCode);
            SetGameLobbyForConnection(lobby2Code, gameLobbyCode);

            return gameLobby;
        }

        public GameLobby? GetGameLobby(string gameLobbyCode)
        {
            _gameLobbies.TryGetValue(gameLobbyCode, out var gameLobby);
            return gameLobby;
        }

        public void SetGameLobbyForConnection(string connectionId, string gameLobbyCode)
        {
            _socketToGameLobby[connectionId] = gameLobbyCode;
        }

        public string? GetGameLobbyCodeByConnectionId(string connectionId)
        {
            _socketToGameLobby.TryGetValue(connectionId, out var gameLobbyCode);
            return gameLobbyCode;
        }
    }
}
