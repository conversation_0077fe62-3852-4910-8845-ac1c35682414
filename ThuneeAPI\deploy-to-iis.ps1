# PowerShell script to deploy Thunee ASP.NET Core API to IIS
# Run this script as Administrator

param(
    [string]$IISPath = "C:\inetpub\Thunee-API",
    [string]$AppPoolName = "ThuneeAPI",
    [string]$SiteName = "ThuneeAPI",
    [int]$Port = 3001,
    [string]$Environment = "Production"
)

Write-Host "Deploying Thunee ASP.NET Core API to IIS..." -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Exiting..."
    exit 1
}

# Import WebAdministration module
Import-Module WebAdministration -ErrorAction SilentlyContinue
if (-not (Get-Module WebAdministration)) {
    Write-Error "IIS WebAdministration module not available. Please install IIS and ASP.NET Core Hosting Bundle."
    exit 1
}

try {
    # Step 1: Build and publish the application
    Write-Host "Building and publishing application..." -ForegroundColor Yellow
    dotnet publish -c Release -o "./publish" --self-contained false
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }

    # Step 2: Stop existing application pool if it exists
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Write-Host "Stopping existing application pool: $AppPoolName" -ForegroundColor Yellow
        Stop-WebAppPool -Name $AppPoolName
        Start-Sleep -Seconds 5
    }

    # Step 3: Create IIS directory
    Write-Host "Creating IIS directory: $IISPath" -ForegroundColor Yellow
    if (Test-Path $IISPath) {
        Remove-Item -Path $IISPath -Recurse -Force
    }
    New-Item -ItemType Directory -Path $IISPath -Force | Out-Null

    # Step 4: Copy published files
    Write-Host "Copying published files to IIS directory..." -ForegroundColor Yellow
    Copy-Item -Path "./publish/*" -Destination $IISPath -Recurse -Force

    # Step 5: Create Application Pool
    Write-Host "Creating application pool: $AppPoolName" -ForegroundColor Yellow
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Remove-WebAppPool -Name $AppPoolName
    }
    
    New-WebAppPool -Name $AppPoolName -Force
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"

    # Step 6: Create or update website
    Write-Host "Creating website: $SiteName" -ForegroundColor Yellow
    if (Get-Website -Name $SiteName -ErrorAction SilentlyContinue) {
        Remove-Website -Name $SiteName
    }
    
    New-Website -Name $SiteName -Port $Port -PhysicalPath $IISPath -ApplicationPool $AppPoolName

    # Step 7: Set environment variable
    Write-Host "Setting environment variable: ASPNETCORE_ENVIRONMENT=$Environment" -ForegroundColor Yellow
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.environmentVariables" -Value @{
        "ASPNETCORE_ENVIRONMENT" = $Environment
    }

    # Step 8: Set permissions
    Write-Host "Setting permissions..." -ForegroundColor Yellow
    $acl = Get-Acl $IISPath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IUSR", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $IISPath -AclObject $acl

    # Step 9: Start application pool and website
    Write-Host "Starting application pool and website..." -ForegroundColor Yellow
    Start-WebAppPool -Name $AppPoolName
    Start-Website -Name $SiteName

    # Step 10: Test the deployment
    Write-Host "Testing deployment..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 30
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ Deployment successful! API is running on http://localhost:$Port" -ForegroundColor Green
        } else {
            Write-Warning "Deployment completed but HTTP test returned status code: $($response.StatusCode)"
        }
    } catch {
        Write-Warning "Deployment completed but HTTP test failed: $($_.Exception.Message)"
        Write-Host "Check the Event Viewer and IIS logs for more details." -ForegroundColor Yellow
    }

    # Display useful information
    Write-Host "`n=== Deployment Summary ===" -ForegroundColor Cyan
    Write-Host "Application Pool: $AppPoolName" -ForegroundColor White
    Write-Host "Website: $SiteName" -ForegroundColor White
    Write-Host "Physical Path: $IISPath" -ForegroundColor White
    Write-Host "Port: $Port" -ForegroundColor White
    Write-Host "Environment: $Environment" -ForegroundColor White
    Write-Host "URL: http://localhost:$Port" -ForegroundColor White
    Write-Host "SignalR Game Hub: http://localhost:$Port/gameHub" -ForegroundColor White
    Write-Host "SignalR Video Hub: http://localhost:$Port/videoHub" -ForegroundColor White
    Write-Host "Test Client: http://localhost:$Port/test-client.html" -ForegroundColor White
    Write-Host "`nTo check logs:" -ForegroundColor Yellow
    Write-Host "- Event Viewer > Windows Logs > Application" -ForegroundColor Gray
    Write-Host "- IIS Manager > $SiteName > Logging" -ForegroundColor Gray
    Write-Host "- $IISPath\logs (if configured)" -ForegroundColor Gray

} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    Write-Host "Check the following:" -ForegroundColor Yellow
    Write-Host "1. ASP.NET Core Hosting Bundle is installed" -ForegroundColor Gray
    Write-Host "2. .NET 8.0 Runtime is installed" -ForegroundColor Gray
    Write-Host "3. IIS is installed and running" -ForegroundColor Gray
    Write-Host "4. Running PowerShell as Administrator" -ForegroundColor Gray
    Write-Host "5. Port $Port is not in use by another application" -ForegroundColor Gray
    exit 1
}

Write-Host "`nDeployment completed!" -ForegroundColor Green
