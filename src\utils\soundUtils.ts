// Utility functions for playing sounds in the game

// Play a sound effect
export const playSound = (soundUrl: string, volume: number = 1.0): void => {
  try {
    const audio = new Audio(soundUrl);
    audio.volume = volume;
    audio.play().catch(error => {
      console.error('Error playing sound:', error);
    });
  } catch (error) {
    console.error('Error creating audio element:', error);
  }
};

// Sound URLs
export const SOUNDS = {
  JORDHI_CALL: '/sounds/jordhi-call.mp3',
  CARD_PLAYED: '/sounds/card-played.mp3',
  GAME_WIN: '/sounds/game-win.mp3',
  GAME_LOSE: '/sounds/game-lose.mp3',
  SUCCESS: '/sounds/game-win.mp3',  // Reuse game-win sound for success
  ERROR: '/sounds/game-lose.mp3',   // Reuse game-lose sound for error
};
