using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface ISpectatorService
    {
        Task<SpectatorJoinResponse> JoinAsSpectatorAsync(string connectionId, string gameCode, string spectatorName);
        Task<bool> RemoveSpectatorAsync(string connectionId);
        Task<AvailableGamesResponse> GetAvailableGamesAsync();
        Task SendGameStateToSpectatorAsync(string connectionId, string gameCode);
    }
}
