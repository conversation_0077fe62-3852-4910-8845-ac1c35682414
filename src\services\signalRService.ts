import * as signalR from '@microsoft/signalr';

// Environment configuration
const getConfig = () => {
  const isDevelopment = import.meta.env.VITE_APP_ENV === 'development';

  return {
    isDevelopment,
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',
    gameHubUrl: import.meta.env.VITE_SIGNALR_GAME_HUB_URL || 'http://localhost:5000/gameHub',
    videoHubUrl: import.meta.env.VITE_SIGNALR_VIDEO_HUB_URL || 'http://localhost:5000/videoHub',
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true'
  };
};

class SignalRService {
  private gameConnection: signalR.HubConnection | null = null;
  private videoConnection: signalR.HubConnection | null = null;
  private config = getConfig();
  private listeners: Map<string, Array<(data: any) => void>> = new Map();

  constructor() {
    if (this.config.debugMode) {
      console.log('SignalR Service Configuration:', this.config);
    }
  }

  // Check if game hub is connected
  isConnected(): boolean {
    return this.gameConnection?.state === signalR.HubConnectionState.Connected;
  }

  // Connect to Game Hub
  async connect(playerName: string): Promise<void> {
    if (this.gameConnection?.state === signalR.HubConnectionState.Connected) {
      console.log('Already connected to Game Hub');
      return;
    }

    try {
      // Close existing connection if any
      if (this.gameConnection) {
        await this.gameConnection.stop();
      }

      this.gameConnection = new signalR.HubConnectionBuilder()
        .withUrl(this.config.gameHubUrl, {
          skipNegotiation: false,
          transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
          withCredentials: false
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .configureLogging(this.config.debugMode ? signalR.LogLevel.Debug : signalR.LogLevel.Warning)
        .build();

      // Set up connection event handlers
      this.setupGameConnectionHandlers();

      await this.gameConnection.start();
      console.log('Connected to SignalR Game Hub');
    } catch (error) {
      console.error('Failed to connect to Game Hub:', error);
      throw error;
    }
  }

  // Connect to Video Hub
  async connectToVideoHub(): Promise<void> {
    if (this.videoConnection?.state === signalR.HubConnectionState.Connected) {
      console.log('Already connected to Video Hub');
      return;
    }

    try {
      if (this.videoConnection) {
        await this.videoConnection.stop();
      }

      this.videoConnection = new signalR.HubConnectionBuilder()
        .withUrl(this.config.videoHubUrl, {
          skipNegotiation: false,
          transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
          withCredentials: false
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .configureLogging(this.config.debugMode ? signalR.LogLevel.Debug : signalR.LogLevel.Warning)
        .build();

      // Set up video connection event handlers
      this.setupVideoConnectionHandlers();

      await this.videoConnection.start();
      console.log('Connected to SignalR Video Hub');
    } catch (error) {
      console.error('Failed to connect to Video Hub:', error);
      throw error;
    }
  }

  // Disconnect from all hubs
  async disconnect(): Promise<void> {
    try {
      if (this.gameConnection) {
        await this.gameConnection.stop();
        this.gameConnection = null;
      }
      if (this.videoConnection) {
        await this.videoConnection.stop();
        this.videoConnection = null;
      }
      console.log('Disconnected from SignalR hubs');
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }

  // Create a new lobby
  async createLobby(playerName: string, teamName?: string, timeSettings?: any): Promise<{
    lobbyCode: string;
    partnerInviteCode: string;
    opponentInviteCode: string;
  }> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('CreateLobby', {
        playerName,
        teamName,
        timeSettings
      });

      console.log('CreateLobby response:', response);

      if (response.success) {
        return {
          lobbyCode: response.data.lobbyCode,
          partnerInviteCode: response.data.partnerInviteCode,
          opponentInviteCode: response.data.opponentInviteCode
        };
      } else {
        console.error('CreateLobby failed:', response.error);
        throw new Error(response.error || 'Failed to create lobby');
      }
    } catch (error) {
      console.error('Error creating lobby:', error);
      throw error;
    }
  }

  // Join an existing lobby
  async joinLobby(lobbyCode: string, playerName: string): Promise<{ actualLobbyCode?: string, isInviteCode?: boolean }> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('JoinLobby', {
        lobbyCode,
        playerName
      });

      if (response.success) {
        return {
          actualLobbyCode: response.data.actualLobbyCode,
          isInviteCode: response.data.isInviteCode
        };
      } else {
        throw new Error(response.error || 'Failed to join lobby');
      }
    } catch (error) {
      console.error('Error joining lobby:', error);
      throw error;
    }
  }

  // Update team name
  async updateTeamName(lobbyCode: string, teamNumber: 1 | 2, teamName: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('UpdateTeamName', {
        lobbyCode,
        teamNumber,
        teamName
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to update team name');
      }
    } catch (error) {
      console.error('Error updating team name:', error);
      throw error;
    }
  }

  // Set team ready status
  async setTeamReady(lobbyCode: string, ready: boolean): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('SetTeamReady', {
        lobbyCode,
        ready
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to set team ready status');
      }
    } catch (error) {
      console.error('Error setting team ready:', error);
      throw error;
    }
  }

  // Switch team
  async switchTeam(lobbyCode: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('SwitchTeam', {
        lobbyCode
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to switch team');
      }
    } catch (error) {
      console.error('Error switching team:', error);
      throw error;
    }
  }

  // Start the game
  async startGame(lobbyCode: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('StartGame', {
        lobbyCode
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      throw error;
    }
  }

  // Find a match
  async findMatch(lobbyCode: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('FindMatch', {
        lobbyCode
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to find match');
      }
    } catch (error) {
      console.error('Error finding match:', error);
      throw error;
    }
  }

  // Cancel finding a match
  async cancelFindMatch(lobbyCode: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('CancelFindMatch', {
        lobbyCode
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to cancel match finding');
      }
    } catch (error) {
      console.error('Error canceling find match:', error);
      throw error;
    }
  }



  // Play a card
  async playCard(card: any): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('PlayCard', {
        card
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to play card');
      }
    } catch (error) {
      console.error('Error playing card:', error);
      throw error;
    }
  }

  // Select trump
  async selectTrump(trumpSuit: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('SelectTrump', {
        trumpSuit
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to select trump');
      }
    } catch (error) {
      console.error('Error selecting trump:', error);
      throw error;
    }
  }

  // Send chat message
  async sendChatMessage(message: string, lobbyCode?: string): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('SendChatMessage', {
        message,
        lobbyCode
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to send chat message');
      }
    } catch (error) {
      console.error('Error sending chat message:', error);
      throw error;
    }
  }

  // Request timeframe options from server
  async requestTimeframeOptions(): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('RequestTimeframeOptions');
      if (!response.success) {
        throw new Error(response.error || 'Failed to request timeframe options');
      }
    } catch (error) {
      console.error('Error requesting timeframe options:', error);
      throw error;
    }
  }

  // Vote for timeframe
  async voteTimeframe(timeframe: number): Promise<void> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      const response = await this.gameConnection.invoke('VoteTimeframe', {
        timeframe
      });

      // Check for ASP.NET Core ApiResponse format (lowercase)
      if (response && !response.success) {
        throw new Error(response.error || 'Failed to vote for timeframe');
      }
    } catch (error) {
      console.error('Error voting for timeframe:', error);
      throw error;
    }
  }

  // Generic game action method for compatibility
  async sendGameAction(action: string, data: any): Promise<void> {
    // Map old Socket.IO actions to new SignalR methods
    switch (action) {
      case 'vote_timeframe':
        return this.voteTimeframe(data.timeframe);
      case 'play_card':
        return this.playCard(data.card);
      case 'select_trump':
        return this.selectTrump(data.trumpSuit);
      case 'send_chat_message':
        return this.sendChatMessage(data.message, data.lobbyCode);

      // Dealer determination
      case 'start_dealer_determination':
        return this.sendCustomEvent('StartDealerDetermination', {});

      // Card shuffling and cutting
      case 'shuffle_deck':
        return this.sendCustomEvent('ShuffleCards', { shuffleType: data.shuffleType });
      case 'request_cut':
        return this.sendCustomEvent('RequestCut', { playerId: data.playerId });
      case 'cut_deck':
        return this.sendCustomEvent('CutCards', { cutPosition: data.cutPosition });
      case 'skip_cut':
        return this.sendCustomEvent('SkipCut', {});

      // Card dealing
      case 'deal_cards':
        return this.sendCustomEvent('DealCards', { cardsPerPlayer: data.cardsPerPlayer || 4 });
      case 'deal_final_cards':
        return this.sendCustomEvent('DealCards', { cardsPerPlayer: 2 });

      // Bidding
      case 'place_bid':
        return this.sendCustomEvent('Bid', { bidAmount: data.bid });
      case 'pass_bid':
        return this.sendCustomEvent('PassBid', {});

      // Special calls
      case 'call_jordhi':
        return this.sendCustomEvent('CallJordhi', {
          value: data.value,
          jordhiSuit: data.jordhiSuit,
          jordhiCards: data.jordhiCards
        });
      case 'reveal_jordhi':
        return this.sendCustomEvent('RevealJordhi', {
          jordhiValue: data.jordhiValue,
          jordhiSuit: data.jordhiSuit,
          jordhiCards: data.jordhiCards,
          revealCards: data.revealCards
        });
      case 'call_double':
        return this.sendCustomEvent('CallDouble', {});
      case 'call_khanak':
        return this.sendCustomEvent('CallKhanak', {});
      case 'call_thunee':
        return this.sendCustomEvent('CallThunee', { firstCard: data.firstCard });
      case 'pass_thunee':
        return this.sendCustomEvent('PassThunee', {});
      case 'hold_game':
        return this.sendCustomEvent('HoldGame', {});

      // 4-ball calls
      case 'four_ball':
        return this.sendCustomEvent('FourBall', {
          ballType: data.ballType,
          option: data.option,
          accusedPlayerId: data.accusedPlayerId,
          handNumber: data.handNumber
        });

      // Other game actions
      case 'request_missing_cards':
        return this.sendCustomEvent('RequestMissingCards', {});

      default:
        console.warn(`Unknown game action: ${action}`);
        throw new Error(`Unknown game action: ${action}`);
    }
  }

  // Add event listener
  on(event: string, callback: (data: any) => void): void {
    if (!this.gameConnection) {
      console.error('Game hub not connected');
      return;
    }

    // Store the callback in our listeners map
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);

    // Add the listener to the connection
    this.gameConnection.on(event, callback);
  }

  // Remove event listener
  off(event: string, callback?: (data: any) => void): void {
    if (!this.gameConnection) {
      console.error('Game hub not connected');
      return;
    }

    if (callback) {
      this.gameConnection.off(event, callback);

      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    } else {
      this.gameConnection.off(event);
      this.listeners.delete(event);
    }
  }

  // Get connection ID
  getSocketId(): string | null {
    return this.gameConnection?.connectionId || null;
  }

  // Get configuration
  getConfig() {
    return {
      environment: this.config.isDevelopment ? 'development' : 'production',
      gameHubUrl: this.config.gameHubUrl,
      videoHubUrl: this.config.videoHubUrl,
      apiBaseUrl: this.config.apiBaseUrl,
      debugMode: this.config.debugMode
    };
  }

  // Setup game connection event handlers
  private setupGameConnectionHandlers(): void {
    if (!this.gameConnection) return;

    this.gameConnection.onreconnecting(() => {
      console.log('Game hub reconnecting...');
    });

    this.gameConnection.onreconnected(() => {
      console.log('Game hub reconnected');
    });

    this.gameConnection.onclose(() => {
      console.log('Game hub connection closed');
    });
  }

  // Setup video connection event handlers
  private setupVideoConnectionHandlers(): void {
    if (!this.videoConnection) return;

    this.videoConnection.onreconnecting(() => {
      console.log('Video hub reconnecting...');
    });

    this.videoConnection.onreconnected(() => {
      console.log('Video hub reconnected');
    });

    this.videoConnection.onclose(() => {
      console.log('Video hub connection closed');
    });
  }



  async sendCustomEvent(event: string, data: any): Promise<any> {
    if (!this.gameConnection) {
      throw new Error('Game hub not connected');
    }

    try {
      return await this.gameConnection.invoke(event, data);
    } catch (error) {
      console.error(`Error sending custom event ${event}:`, error);
      throw error;
    }
  }

  // Update server URL (for compatibility)
  updateServerUrl(_port: string): void {
    console.warn('updateServerUrl is deprecated. Use environment variables instead.');
    // This method is kept for compatibility but doesn't do anything
    // as URLs are now managed through environment variables
  }
}

// Create a singleton instance
const signalRService = new SignalRService();

export default signalRService;
