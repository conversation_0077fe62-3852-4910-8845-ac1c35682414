"use client";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function ScrollIndicator() {
  return (
    <div className="absolute bottom-0 left-0 right-0 flex justify-center items-center pointer-events-none">
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: [0.3, 0.7, 0.3] }}
        transition={{ 
          repeat: Infinity, 
          duration: 2,
        }}
        className="bg-black/50 text-[#E1C760] px-3 py-1 rounded-full flex items-center gap-2 text-xs"
      >
        <ChevronLeft size={14} />
        <span>Scroll to see all cards</span>
        <ChevronRight size={14} />
      </motion.div>
    </div>
  );
}
