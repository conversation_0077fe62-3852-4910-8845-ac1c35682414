@echo off
echo ========================================
echo Thunee Production Deployment Script
echo ========================================

set SOURCE_DIR=%~dp0
set DEPLOY_DIR=%USERPROFILE%\Desktop\Thunee-Production
set FRONTEND_DIR=C:\inetpub\Thunee-FE

echo Source directory: %SOURCE_DIR%
echo Deployment directory: %DEPLOY_DIR%
echo Frontend directory: %FRONTEND_DIR%

echo.
echo Step 1: Creating deployment directory...
if not exist "%DEPLOY_DIR%" (
    mkdir "%DEPLOY_DIR%"
    echo Created %DEPLOY_DIR%
) else (
    echo Directory %DEPLOY_DIR% already exists
)

echo.
echo Step 2: Installing/updating dependencies...
echo Installing npm dependencies in source directory...
call npm install --legacy-peer-deps

if %ERRORLEVEL% NEQ 0 (
    echo Error: npm install failed in source directory
    pause
    exit /b 1
)

echo.
echo Step 3: Building React frontend...
echo Building with production environment...
set NODE_ENV=production
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo Error: Frontend build failed
    pause
    exit /b 1
)

echo.
echo Step 4: Copying built frontend to deployment directory...
if exist "%SOURCE_DIR%dist" (
    xcopy "%SOURCE_DIR%dist" "%DEPLOY_DIR%\dist" /E /I /Y
    echo Copied built frontend to dist directory
) else (
    echo Error: dist directory not found - build may have failed
    pause
    exit /b 1
)

echo.
echo Step 5: Copying server files...
xcopy "%SOURCE_DIR%server" "%DEPLOY_DIR%\server" /E /I /Y
echo Copied server directory

echo.
echo Step 5.1: Installing server dependencies...
cd /d "%SOURCE_DIR%server"
if not exist "node_modules" (
    echo Installing server dependencies in source directory...
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Server npm install failed in source directory
        pause
        exit /b 1
    )
) else (
    echo Server dependencies already installed in source directory
)

echo.
echo Step 5.2: Installing server dependencies in deployment directory...
cd /d "%DEPLOY_DIR%\server"
call npm install --production
if %ERRORLEVEL% NEQ 0 (
    echo Error: Server npm install failed in deployment directory
    pause
    exit /b 1
)
echo Server dependencies installed successfully

echo.
echo Step 6: Copying configuration files...
copy "%SOURCE_DIR%web.config" "%DEPLOY_DIR%\" /Y
copy "%SOURCE_DIR%production-package.json" "%DEPLOY_DIR%\package.json" /Y
echo Copied configuration files

echo.
echo Step 7: Installing production dependencies...
cd /d "%DEPLOY_DIR%"
call npm install --production --legacy-peer-deps
if %ERRORLEVEL% NEQ 0 (
    echo Error: npm install failed in deployment directory
    pause
    exit /b 1
)

echo.
echo Step 8: Setting up environment...
echo NODE_ENV=production > "%DEPLOY_DIR%\.env"
echo PORT=80 >> "%DEPLOY_DIR%\.env"
echo VIDEO_PORT=3002 >> "%DEPLOY_DIR%\.env"

echo.
echo Step 9: Setting permissions...
icacls "%DEPLOY_DIR%" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "%DEPLOY_DIR%" /grant "IUSR:(OI)(CI)R" /T
icacls "%DEPLOY_DIR%" /grant "Everyone:(OI)(CI)R" /T

echo.
echo Step 10: Creating IIS application pool and site...
echo Please run the following PowerShell commands as Administrator:
echo.
echo # Import WebAdministration module
echo Import-Module WebAdministration
echo.
echo # Create Application Pool
echo New-WebAppPool -Name "ThuneeAppPool" -Force
echo Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
echo Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "enable32BitAppOnWin64" -Value $false
echo Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "managedRuntimeVersion" -Value ""
echo.
echo # Create Website (remove existing if it exists)
echo Remove-Website -Name "Thunee" -ErrorAction SilentlyContinue
echo New-Website -Name "Thunee" -Port 96 -PhysicalPath "%DEPLOY_DIR%" -ApplicationPool "ThuneeAppPool"
echo.
echo # Enable WebSocket support
echo Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All
echo.

echo.
echo ========================================
echo Local Deployment Package Created!
echo ========================================
echo.
echo IMPORTANT: This is a LOCAL deployment package.
echo You need to transfer files to your IIS server.
echo.
echo Files created in: %DEPLOY_DIR%
echo.
echo NEXT STEPS FOR REMOTE IIS SERVER:
echo.
echo 1. TRANSFER these files to your IIS server:
echo    - Entire folder: %DEPLOY_DIR%
echo    - Copy to: C:\inetpub\Thunee-Production (on IIS server)
echo    - Setup scripts: setup-iis.ps1, test-deployment.bat
echo    - Video script: start-video-server-production.bat
echo.
echo 2. ON THE IIS SERVER (via Remote Desktop):
echo    a) Open PowerShell as Administrator
echo    b) Run: .\setup-iis.ps1
echo    c) Run: .\test-deployment.bat
echo    d) Optional: .\start-video-server-production.bat
echo.
echo 3. ACCESS your application at:
echo    http://**************:96
echo.
echo 4. CHECK logs if needed:
echo    http://**************:96/iisnode/
echo.
echo Press any key to exit...
pause >nul
