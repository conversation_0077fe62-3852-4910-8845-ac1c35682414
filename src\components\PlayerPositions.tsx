"use client";
import { useGameStore, Player } from "@/store/gameStore";
import { useEffect, useState } from "react";
import gameService from "@/services/gameService";

export default function PlayerPositions() {
  const { players } = useGameStore();
  const [currentPlayerId, setCurrentPlayerId] = useState<string | null>(null);
  const [positionedPlayers, setPositionedPlayers] = useState<Record<number, Player | null>>({
    1: null, // Top left
    2: null, // Top right
    3: null, // Bottom left (dealer)
    4: null, // Bottom right
  });

  // Get the current player ID
  useEffect(() => {
    const id = gameService.getSocketId();
    setCurrentPlayerId(id);
  }, []);

  // Map players to their positions
  useEffect(() => {
    if (players.length === 0) return;

    const positions: Record<number, Player | null> = {
      1: null,
      2: null,
      3: null,
      4: null,
    };

    // Assign players to their positions
    players.forEach(player => {
      if (player.position) {
        positions[player.position] = player;
      }
    });

    setPositionedPlayers(positions);
  }, [players]);

  if (players.length === 0) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full pointer-events-none z-10">
      {/* Top left player (Position 1) */}
      <div className="absolute top-4 left-4">
        <PlayerPositionIndicator 
          player={positionedPlayers[1]} 
          position={1} 
          isCurrentPlayer={positionedPlayers[1]?.id === currentPlayerId}
        />
      </div>

      {/* Top right player (Position 2) */}
      <div className="absolute top-4 right-4">
        <PlayerPositionIndicator 
          player={positionedPlayers[2]} 
          position={2} 
          isCurrentPlayer={positionedPlayers[2]?.id === currentPlayerId}
        />
      </div>

      {/* Bottom left player (Position 3) - Dealer */}
      <div className="absolute bottom-4 left-4">
        <PlayerPositionIndicator 
          player={positionedPlayers[3]} 
          position={3} 
          isCurrentPlayer={positionedPlayers[3]?.id === currentPlayerId}
        />
      </div>

      {/* Bottom right player (Position 4) */}
      <div className="absolute bottom-4 right-4">
        <PlayerPositionIndicator 
          player={positionedPlayers[4]} 
          position={4} 
          isCurrentPlayer={positionedPlayers[4]?.id === currentPlayerId}
        />
      </div>
    </div>
  );
}

interface PlayerPositionIndicatorProps {
  player: Player | null;
  position: number;
  isCurrentPlayer: boolean;
}

function PlayerPositionIndicator({ player, position, isCurrentPlayer }: PlayerPositionIndicatorProps) {
  if (!player) return null;

  const teamColor = player.team === 1 ? 'bg-blue-600' : 'bg-red-600';
  const currentPlayerHighlight = isCurrentPlayer ? 'ring-2 ring-yellow-400 ring-offset-2' : '';
  const dealerIndicator = player.isDealer ? '🎲' : '';
  const trumperIndicator = player.isTrumpSelector ? '♠️' : '';

  return (
    <div className={`rounded-full ${teamColor} text-white px-3 py-1 text-sm ${currentPlayerHighlight} flex items-center space-x-1`}>
      <span>P{position}</span>
      <span>-</span>
      <span>{player.name}</span>
      {dealerIndicator && <span className="ml-1">{dealerIndicator}</span>}
      {trumperIndicator && <span className="ml-1">{trumperIndicator}</span>}
    </div>
  );
}
