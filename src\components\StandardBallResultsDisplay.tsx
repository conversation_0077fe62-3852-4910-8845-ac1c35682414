"use client";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";
import "../styles/StandardBallResultsDisplay.css";

interface HandWon {
  handId: number;
  winner: {
    id: string;
    name: string;
    team: 1 | 2;
  };
  cards: any[];
  points: number;
}

interface StandardBallResultsDisplayProps {
  isVisible: boolean;
  onClose: () => void;
  handsWonByNonTrumpingTeam: HandWon[];
  targetScore: number;
  initialTargetScore: number;
  jordhiAdjustments: {
    nonTrumpingTeam: number;
    trumpingTeam: number;
  };
  finalHandAdjustment: number;
  nonTrumpingTeamPoints: number;
  trumpingTeam: 1 | 2;
  winningTeam?: 1 | 2; // Optional as we determine the winner based on points
  ballsAwarded: number;
  isCallAndLost?: boolean; // Whether this was a "Call and lost" scenario
  displayDuration?: number;
  onContinueGame?: () => void;
}

export default function StandardBallResultsDisplay({
  isVisible,
  onClose,
  handsWonByNonTrumpingTeam = [],
  targetScore,
  initialTargetScore,
  jordhiAdjustments,
  finalHandAdjustment,
  nonTrumpingTeamPoints,
  trumpingTeam,
  winningTeam, // This is no longer used as we determine the winner based on points
  ballsAwarded = 1,
  isCallAndLost = false,
  displayDuration = 10000, // Default to 10 seconds
  onContinueGame,
}: StandardBallResultsDisplayProps) {
  const { teamNames } = useGameStore();
  const [timeRemaining, setTimeRemaining] = useState(displayDuration / 1000);
  const [showContinueButton, setShowContinueButton] = useState(false);
  const [animatedScore, setAnimatedScore] = useState(initialTargetScore);
  const [animationComplete, setAnimationComplete] = useState(false);

  // Make sure we correctly identify the non-trumping team
  // The trumpingTeam prop should be explicitly provided from the server
  // If trumpingTeam is null or undefined, default to team 1 as trumping team
  const validTrumpingTeam = trumpingTeam === 1 || trumpingTeam === 2 ? trumpingTeam : 1;
  const nonTrumpingTeam = validTrumpingTeam === 1 ? 2 : 1;
  console.log(`StandardBallResultsDisplay: trumpingTeam=${trumpingTeam}, validTrumpingTeam=${validTrumpingTeam}, nonTrumpingTeam=${nonTrumpingTeam}`);

  // Force the non-trumping team to be team 2 for testing purposes
  // This is a temporary fix until we can properly debug the issue
  // const forcedNonTrumpingTeam = 2;
  // const forcedTrumpingTeam = 1;

  // IMPORTANT: For debugging purposes, let's log all props
  console.log("StandardBallResultsDisplay props:", {
    trumpingTeam,
    nonTrumpingTeam: nonTrumpingTeam,
    targetScore,
    initialTargetScore,
    jordhiAdjustments,
    finalHandAdjustment,
    nonTrumpingTeamPoints,
    handsWonByNonTrumpingTeam,
    winningTeam
  });

  // Set up timer for showing the continue button
  useEffect(() => {
    if (!isVisible) {
      setShowContinueButton(false);
      setTimeRemaining(displayDuration / 1000);
      setAnimatedScore(initialTargetScore);
      setAnimationComplete(false);
      return;
    }

    console.log("StandardBallResultsDisplay is visible, starting timer");
    setShowContinueButton(false);

    // Start countdown
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          console.log("Timer reached 0, showing continue button");
          setShowContinueButton(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Animate the target score changes
    let currentScore = initialTargetScore;

    // First animate the Jordhi adjustments
    setTimeout(() => {
      if (jordhiAdjustments.nonTrumpingTeam !== 0) {
        currentScore -= jordhiAdjustments.nonTrumpingTeam;
        setAnimatedScore(currentScore);
      }
    }, 2000);

    // Then animate the final hand adjustment
    setTimeout(() => {
      currentScore += finalHandAdjustment;
      setAnimatedScore(currentScore);
    }, 4000);

    // Mark animation as complete
    setTimeout(() => {
      setAnimationComplete(true);
    }, 6000);

    // After displayDuration, show the continue button
    const showContinueTimer = setTimeout(() => {
      console.log("Display duration reached, showing continue button");
      setShowContinueButton(true);
    }, displayDuration);

    return () => {
      clearInterval(timer);
      clearTimeout(showContinueTimer);
    };
  }, [isVisible, displayDuration, initialTargetScore, jordhiAdjustments, finalHandAdjustment]);

  // Calculate total points from hands won - this is just for verification
  const calculatedPoints = handsWonByNonTrumpingTeam.reduce((sum, hand) => sum + (hand.points || 0), 0);

  // Check if the calculated points match the provided points
  if (calculatedPoints !== nonTrumpingTeamPoints) {
    console.error(`Points mismatch: Calculated ${calculatedPoints} but received ${nonTrumpingTeamPoints}`);
    console.error(`Using the provided nonTrumpingTeamPoints value from the server for consistency`);
    // We'll trust the server's value over our calculated value
    // This ensures we're using the correct value for display
  }

  // Log the trumping team and non-trumping team for debugging
  console.log(`Using trumpingTeam=${validTrumpingTeam}, nonTrumpingTeam=${nonTrumpingTeam}`);
  console.log(`nonTrumpingTeamPoints=${nonTrumpingTeamPoints}, targetScore=${targetScore}`);

  // Non-trumping team is already defined at the top of the component

  // Log the winner determination for debugging
  const actualWinner = nonTrumpingTeamPoints >= targetScore ? nonTrumpingTeam : validTrumpingTeam;
  console.log(`Ball winner determined in UI: Team ${actualWinner} (${teamNames[actualWinner]})`);
  console.log(`Non-trumping team: ${nonTrumpingTeam} (${teamNames[nonTrumpingTeam]}), Trumping team: ${validTrumpingTeam} (${teamNames[validTrumpingTeam]})`);
  console.log(`Points: ${calculatedPoints}, Target: ${targetScore}`);

  // Ensure we're using the correct team names
  const winningTeamName = nonTrumpingTeamPoints >= targetScore ? teamNames[nonTrumpingTeam] : teamNames[validTrumpingTeam];
  console.log(`Winning team name: ${winningTeamName}, from team ${nonTrumpingTeamPoints >= targetScore ? nonTrumpingTeam : validTrumpingTeam}`);

  // Additional logging to help debug
  console.log(`Trumping team from props: ${trumpingTeam}`);
  console.log(`Valid trumping team: ${validTrumpingTeam}`);
  console.log(`Non-trumping team calculated: ${nonTrumpingTeam}`);
  console.log(`Non-trumping team points: ${nonTrumpingTeamPoints}`);
  console.log(`Target score: ${targetScore}`);

  // Check if the UI-determined winner matches the provided winner (if any)
  if (winningTeam && actualWinner !== winningTeam) {
    console.error(`Winner mismatch: UI determined Team ${actualWinner} but received Team ${winningTeam}`);
    console.error(`This could indicate a problem with the trumping team information`);
    console.error(`trumpingTeam=${trumpingTeam}, validTrumpingTeam=${validTrumpingTeam}, nonTrumpingTeam=${nonTrumpingTeam}`);
    console.error(`nonTrumpingTeamPoints=${nonTrumpingTeamPoints}, targetScore=${targetScore}`);
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="ball-results-container"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="ball-results-card-wrapper"
          >
            <div className="ball-results-card">
              <div className="ball-results-header">
                <h2 className="ball-results-title">Ball Results</h2>
                <div className="ball-results-timer">
                  {timeRemaining}s
                </div>
              </div>

              <div>
                {/* Target Score Breakdown */}
                <div className="ball-results-section">
                  <h3 className="ball-results-section-title">Target Score Breakdown</h3>

                  <div className="ball-results-row">
                    <span>Initial Target Score:</span>
                    <span className="ball-results-value ball-results-value-highlight">{initialTargetScore}</span>
                  </div>

                  {jordhiAdjustments.nonTrumpingTeam !== 0 && (
                    <div className="ball-results-row">
                      <span>{teamNames[nonTrumpingTeam]} Jordhi Calls:</span>
                      <span className="ball-results-value" style={{ color: '#f87171' }}>-{jordhiAdjustments.nonTrumpingTeam}</span>
                    </div>
                  )}

                  {jordhiAdjustments.trumpingTeam !== 0 && (
                    <div className="ball-results-row">
                      <span>{teamNames[validTrumpingTeam]} Jordhi Calls:</span>
                      <span className="ball-results-value" style={{ color: '#4ade80' }}>+{jordhiAdjustments.trumpingTeam}</span>
                    </div>
                  )}

                  <div className="ball-results-row">
                    <span>Final Hand Adjustment:</span>
                    <span className="ball-results-value" style={{ color: finalHandAdjustment < 0 ? '#f87171' : '#4ade80' }}>
                      {finalHandAdjustment < 0 ? '-' : '+'}10
                    </span>
                  </div>

                  <div className="ball-results-divider ball-results-row">
                    <span className="text-lg">Final Target Score:</span>
                    <motion.span
                      className="ball-results-value-highlight"
                      key={animatedScore}
                      initial={{ scale: 1 }}
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 0.5 }}
                    >
                      {animatedScore}
                    </motion.span>
                  </div>
                </div>

                {/* Hands won by non-trumping team */}
                <div className="ball-results-section">
                  <h3 className="ball-results-section-title">
                    Hands Won by {teamNames[nonTrumpingTeam]}:
                  </h3>

                  {handsWonByNonTrumpingTeam.length === 0 ? (
                    <p style={{ color: '#9ca3af', fontStyle: 'italic', textAlign: 'center', padding: '1rem 0' }}>No hands won</p>
                  ) : (
                    <div className="ball-results-hands-grid">
                      {handsWonByNonTrumpingTeam.map((hand) => (
                        <div key={hand.handId} className="ball-results-hand-card">
                          <div className="ball-results-hand-header">
                            <span>Hand #{hand.handId}</span>
                            <span className="ball-results-hand-winner">
                              Winner: {hand.winner?.name || `Player ${hand.winner?.id}`} ({hand.points || 0} pts)
                            </span>
                          </div>
                          <div className="ball-results-cards-container">
                            {hand.cards?.map((card, index) => (
                              <div key={index} className="ball-results-card-image">
                                <img
                                  src={getCardImagePath(card.value, card.suit)}
                                  alt={`${card.value} of ${card.suit}`}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Points Summary */}
                <div className="ball-results-section">
                  <div style={{ textAlign: 'center', marginBottom: '0.75rem', color: '#E1C760', fontWeight: '600' }}>
                    {teamNames[nonTrumpingTeam]} needs {targetScore}+ points to win the ball
                  </div>

                  <div className="ball-results-row">
                    <span className="text-lg">{teamNames[nonTrumpingTeam]} Total Points:</span>
                    <motion.span
                      className="ball-results-value"
                      style={{
                        color: animationComplete
                          ? (nonTrumpingTeamPoints >= targetScore ? '#4ade80' : '#f87171')
                          : 'white',
                        fontSize: '1.25rem'
                      }}
                      animate={{ scale: animationComplete ? [1, 1.2, 1] : 1 }}
                      transition={{ duration: 0.5 }}
                    >
                      {nonTrumpingTeamPoints}
                    </motion.span>
                  </div>

                  <div className="ball-results-row">
                    <span className="text-lg">Target Score:</span>
                    <span className="ball-results-value-highlight">{targetScore}</span>
                  </div>

                  {animationComplete && (
                    <div style={{ marginTop: '1rem', textAlign: 'center' }}>
                      <span style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>
                        {nonTrumpingTeamPoints >= targetScore
                          ? `${teamNames[nonTrumpingTeam]} reached the target of ${targetScore} with ${nonTrumpingTeamPoints} points and wins the ball!`
                          : `${teamNames[nonTrumpingTeam]} did not reach the target of ${targetScore} with only ${nonTrumpingTeamPoints} points. ${teamNames[validTrumpingTeam]} wins the ball!`}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <div className="ball-results-final">
                  <h3 className="ball-results-final-title">Final Result</h3>
                  <p className="ball-results-final-result">
                    {`${winningTeamName} awarded ${ballsAwarded} ball${ballsAwarded !== 1 ? 's' : ''}`}
                  </p>
                  <p className="ball-results-final-description">
                    {nonTrumpingTeamPoints >= targetScore
                      ? isCallAndLost
                        ? `${teamNames[nonTrumpingTeam]} reached their target score of ${targetScore} with ${nonTrumpingTeamPoints} points. This is a "Call and lost" - ${teamNames[validTrumpingTeam]} bid to trump but lost!`
                        : `${teamNames[nonTrumpingTeam]} reached their target score of ${targetScore} with ${nonTrumpingTeamPoints} points`
                      : `${teamNames[nonTrumpingTeam]} failed to reach their target score of ${targetScore} with only ${nonTrumpingTeamPoints} points. ${teamNames[validTrumpingTeam]} wins!`}
                  </p>
                </div>

                {showContinueButton && (
                  <button
                    onClick={() => {
                      if (onContinueGame) {
                        onContinueGame();
                      }
                      onClose();
                    }}
                    className="ball-results-continue-button"
                    style={{ animation: 'pulse 2s infinite' }}
                  >
                    Continue Game
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
