"use client";
import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { <PERSON>R<PERSON>, AlertCircle, User, Lock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import BurgerMenu from "@/components/BurgerMenu";
import { useAuthStore } from "@/store/authStore";
// Use public path for GRCard image
const GRCard = "/assets/GRCard.png";

export default function Login() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { login, isLoading } = useAuthStore();
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username) {
      setErrorMessage("Please enter your username");
      return;
    }

    if (!password) {
      setErrorMessage("Please enter your password");
      return;
    }

    setErrorMessage(null);

    try {
      await login(username, password);
      navigate("/"); // Navigate to the lobby page after successful login
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to login');
    }
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <div className="relative w-full p-2 sm:p-4 flex justify-center items-center">
        <h1 className="text-2xl sm:text-3xl font-bold text-[#E1C760]">Thunee</h1>
        <BurgerMenu />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-3 sm:p-4">
        {/* Responsive container - adjusts width based on orientation */}
        <div className="w-full max-w-[90%] sm:max-w-md md:max-w-lg lg:max-w-xl
                        landscape:max-w-[80%] landscape:md:max-w-2xl">

          {/* Card and Title - flex row in landscape, column in portrait */}
          <div className="flex flex-col landscape:flex-row landscape:items-center landscape:justify-between mb-4 sm:mb-6">
            {/* Card Image */}
            <div className="flex justify-center landscape:justify-start mb-4 landscape:mb-0">
              <img
                src={GRCard}
                alt="Thunee Card"
                className="w-24 sm:w-32 h-auto landscape:w-20 landscape:md:w-28"
              />
            </div>

            <h2 className="text-xl sm:text-2xl font-bold text-center landscape:text-right text-[#E1C760] landscape:ml-4">
              Sign In
            </h2>
          </div>

          {errorMessage && (
            <Alert variant="destructive" className="mb-4 bg-red-900/50 border border-red-500">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">{errorMessage}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleLogin} className="space-y-3 sm:space-y-4">
            <div className="space-y-1 sm:space-y-2">
              <Label htmlFor="username" className="text-[#E1C760] text-sm sm:text-base">Username</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 sm:h-5 w-4 sm:w-5" />
                <Input
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter your username"
                  className="pl-10 h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-sm sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="space-y-1 sm:space-y-2">
              <Label htmlFor="password" className="text-[#E1C760] text-sm sm:text-base">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 sm:h-5 w-4 sm:w-5" />
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  className="pl-10 h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-sm sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Responsive button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full h-10 sm:h-12 bg-[#E1C760] text-black hover:bg-[#E1C760]/90 flex items-center justify-center gap-2 text-sm sm:text-base mt-2"
            >
              {isLoading ? "Signing In..." : "Sign In"}
              {!isLoading && <ArrowRight className="h-4 sm:h-5 w-4 sm:w-5" />}
            </Button>

            <div className="text-center mt-3 sm:mt-4">
              <p className="text-gray-400 text-xs sm:text-sm">
                Don't have an account?{" "}
                <Link to="/register" className="text-[#E1C760] hover:underline">
                  Sign Up
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
