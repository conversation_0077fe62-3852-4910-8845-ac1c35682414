/**
 * Handler for dealer-related actions
 */
const playerPositionUtils = require('../utils/playerPositionUtils');

/**
 * Set the dealer for a game
 * @param {Object} io - Socket.io instance
 * @param {Object} socket - Socket instance
 * @param {Object} data - Action data
 * @param {Function} callback - Callback function
 * @param {Map} lobbies - Map of lobbies
 * @param {Map} socketToLobby - Map of socket IDs to lobby codes
 * @param {Map} socketToGameLobby - Map of socket IDs to game lobby codes
 */
function handleSetDealer(io, socket, data, callback, lobbies, socketToLobby, socketToGameLobby) {
  try {
    console.log('set_dealer event received from client:', socket.id);
    console.log('Data:', data);

    // Check if player is in a game lobby
    let gameLobbyCode = socketToGameLobby.get(socket.id);
    let lobby, matchedLobby, lobbyCode;

    if (gameLobbyCode) {
      console.log(`Player ${socket.id} is in game lobby ${gameLobbyCode}`);
      lobby = lobbies.get(gameLobbyCode);
      lobbyCode = gameLobbyCode;

      // Get the matched lobby
      matchedLobby = lobbies.get(lobby.matchedLobby);
      if (!matchedLobby) {
        console.error('Matched lobby not found');
        return callback?.({ success: false, error: 'Matched lobby not found' });
      }
    } else {
      // Check if player is in a regular lobby
      lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      lobby = lobbies.get(lobbyCode);

      // Get the matched lobby
      matchedLobby = lobbies.get(lobby.matchedLobby);
      if (!matchedLobby) {
        console.error('Matched lobby not found');
        return callback?.({ success: false, error: 'Matched lobby not found' });
      }
    }

    console.log(`Processing set_dealer in lobby ${lobbyCode}`);
    console.log(`Current dealer ID: ${lobby.dealerId}`);

    // Get the dealer ID from the data
    const dealerId = data.dealerId;

    if (!dealerId) {
      console.error('No dealer ID provided');
      return callback?.({ success: false, error: 'No dealer ID provided' });
    }

    // Check if the dealer ID is valid (belongs to a player in the lobby)
    const allPlayers = [];
    if (lobby.players && Array.isArray(lobby.players)) {
      allPlayers.push(...lobby.players);
    }
    if (matchedLobby.players && Array.isArray(matchedLobby.players)) {
      allPlayers.push(...matchedLobby.players);
    }

    const dealerPlayer = allPlayers.find(p => p.id === dealerId);
    if (!dealerPlayer) {
      console.error(`Dealer ID ${dealerId} does not belong to any player in the lobby`);
      return callback?.({ success: false, error: 'Invalid dealer ID' });
    }

    // Set the dealer ID in the lobby
    lobby.dealerId = dealerId;
    matchedLobby.dealerId = dealerId;

    console.log(`Dealer set to ${dealerId}`);

    // Determine the trump selector (player to the right of dealer)
    const dealerIndex = allPlayers.findIndex(p => p.id === dealerId);
    if (dealerIndex === -1) {
      console.error(`Dealer ${dealerId} not found in players list`);
      return callback?.({ success: false, error: 'Dealer not found in players list' });
    }

    // The trump selector is the player to the right of the dealer
    // In a 4-player game with counter-clockwise play, this is the player at index (dealerIndex + 3) % 4
    const trumpSelectorIndex = (dealerIndex + 3) % allPlayers.length;
    const trumpSelector = allPlayers[trumpSelectorIndex];

    if (!trumpSelector) {
      console.error(`Trump selector not found at index ${trumpSelectorIndex}`);
      return callback?.({ success: false, error: 'Trump selector not found' });
    }

    // Set the trump selector ID in the lobby
    lobby.trumpSelectorId = trumpSelector.id;
    matchedLobby.trumpSelectorId = trumpSelector.id;

    console.log(`Trump selector set to ${trumpSelector.id} (${trumpSelector.name})`);

    // Update the players array with dealer and trump selector information
    let updatedPlayers = allPlayers.map(player => ({
      ...player,
      isDealer: player.id === dealerId,
      isTrumpSelector: player.id === trumpSelector.id
    }));

    // Assign fixed positions to players based on the dealer
    // Dealer is always position 3, dealer's partner is position 1
    // Opposing team players are positions 2 and 4
    updatedPlayers = playerPositionUtils.assignPositionsBasedOnDealer(updatedPlayers, dealerId);

    // Update the positions in the lobby
    lobby.players = lobby.players.map(player => {
      const updatedPlayer = updatedPlayers.find(p => p.id === player.id);
      return updatedPlayer || player;
    });

    matchedLobby.players = matchedLobby.players.map(player => {
      const updatedPlayer = updatedPlayers.find(p => p.id === player.id);
      return updatedPlayer || player;
    });

    // Notify all players of the dealer update with positions
    io.to(lobbyCode).emit('dealer_updated', {
      dealerId,
      trumpSelectorId: trumpSelector.id,
      players: updatedPlayers
    });
    io.to(matchedLobby.lobbyCode).emit('dealer_updated', {
      dealerId,
      trumpSelectorId: trumpSelector.id,
      players: updatedPlayers
    });

    callback?.({ success: true });
  } catch (error) {
    console.error('Error in set_dealer:', error);
    callback?.({ success: false, error: error.message });
  }
}

module.exports = {
  handleSetDealer
};
