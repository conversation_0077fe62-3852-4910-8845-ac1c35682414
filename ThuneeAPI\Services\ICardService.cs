using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface ICardService
    {
        List<Card> CreateGameDeck();
        List<Card> ShuffleDeck(List<Card> deck);
        List<Card> CutDeck(List<Card> deck, int cutPosition);
        Dictionary<string, List<Card>> DealCardsToPlayers(List<Player> players, List<Card> deck, int cardsPerPlayer, Dictionary<string, List<Card>>? existingPlayerCards = null);
        List<Card> RemoveDuplicateCards(List<Card> deck);
        HashSet<string> GetAllDealtCardKeys(Dictionary<string, List<Card>> playerCards);
        Card? FindCardInPlayerHand(string playerId, Card card, Dictionary<string, List<Card>> playerCards);
        bool RemoveCardFromPlayerHand(string playerId, Card card, Dictionary<string, List<Card>> playerCards);
        string GetCardImagePath(string value, string suit);
        Card? DetermineHandWinner(List<Card> playedCards, string? trumpSuit);
        int GetCardPoints(Card card);
    }
}
