# Thunee Frontend Migration Guide
## From Socket.IO to ASP.NET Core SignalR

### ✅ **Migration Status: READY FOR TESTING**

Your React frontend has been successfully configured to use the new ASP.NET Core SignalR API with automatic environment detection.

---

## 🌍 **Environment Configuration**

### **Development Environment**
- **API Endpoint:** `http://localhost:5000`
- **Game Hub:** `http://localhost:5000/gameHub`
- **Video Hub:** `http://localhost:5000/videoHub`
- **Triggered by:** `npm run dev`

### **Production Environment**
- **API Endpoint:** `https://www.thuneeAPI.easygames.co.za`
- **Game Hub:** `https://www.thuneeAPI.easygames.co.za/gameHub`
- **Video Hub:** `https://www.thuneeAPI.easygames.co.za/videoHub`
- **Triggered by:** `npm run build` or `npm run prod`

---

## 📁 **Files Created/Modified**

### **Environment Files**
- `.env` - Default environment variables
- `.env.development` - Development-specific settings
- `.env.production` - Production-specific settings

### **New Services**
- `src/services/signalRService.ts` - SignalR client service
- `src/services/gameService.ts` - Unified service adapter

### **New Components**
- `src/components/EnvironmentIndicator.tsx` - Shows current environment and connection status

### **Updated Files**
- `package.json` - Updated scripts and removed Socket.IO dependency
- `src/App.tsx` - Added environment indicator
- `src/services/gameService.ts` - Updated with deprecation notice and environment detection

---

## 🚀 **How to Use**

### **Development (Local Testing)**
```bash
# Start your ASP.NET Core API first
cd ThuneeAPI
dotnet run --urls "http://localhost:5000"

# In a new terminal, start your React app
npm run dev
```

### **Production Build**
```bash
# Build for production
npm run build

# Or build and preview
npm run prod
```

---

## 🔧 **Environment Indicator**

The new Environment Indicator component (top-right corner) shows:
- **Environment Badge:** Development (green) or Production (blue)
- **Connection Status:** Connected/Disconnected with icon
- **Service Type:** SignalR or Socket.IO
- **Settings Button:** Click to see detailed configuration

### **Development Tools (Dev Mode Only)**
- **Log Config:** Logs current configuration to console
- **Force SignalR:** Forces the app to use SignalR service

---

## 🔄 **Migration Strategy**

### **Phase 1: Gradual Migration (Current)**
- Socket.IO service still available for backward compatibility
- New SignalR service ready for testing
- Environment indicator shows which service is active
- All new features should use `gameService.ts`

### **Phase 2: Full Migration (Next)**
- Update all stores to use `gameService.ts` instead of `gameService.ts`
- Remove Socket.IO dependencies completely
- Update all components to use SignalR events

---

## 🧪 **Testing Your Migration**

### **1. Test Development Environment**
```bash
# Start ASP.NET Core API
start-dev-api.bat

# Start React app
npm run dev

# Check environment indicator shows:
# - Environment: DEVELOPMENT (green)
# - Service: SignalR
# - Connection: Connected
# - Game Hub: http://localhost:5000/gameHub
```

### **2. Test Production Build**
```bash
# Build for production
npm run build

# Check that build uses production environment variables
# Environment indicator should show:
# - Environment: PRODUCTION (blue)
# - Game Hub: https://www.thuneeAPI.easygames.co.za/gameHub
```

### **3. Test API Connection**
1. Open browser to `http://localhost:3000` (React app)
2. Look for Environment Indicator in top-right corner
3. Click Settings button to see detailed configuration
4. Connection status should show "Connected" with green checkmark

---

## 🔍 **Troubleshooting**

### **Environment Indicator Shows "Disconnected"**
- Make sure ASP.NET Core API is running on `http://localhost:5000`
- Check browser console for connection errors
- Verify firewall isn't blocking the connection

### **Wrong Environment Detected**
- Check that `.env.development` and `.env.production` files exist
- Verify `npm run dev` uses development mode
- Verify `npm run build` uses production mode

### **API Not Found in Production**
- Ensure `www.thuneeAPI.easygames.co.za` is accessible
- Check that production API is deployed and running
- Verify SSL certificates are valid

---

## 📋 **Next Steps**

### **Immediate (Ready Now)**
1. ✅ Test the environment indicator
2. ✅ Verify development environment connects to local API
3. ✅ Test basic lobby creation and joining

### **Short Term (Next Phase)**
1. 🔄 Update lobby store to use `gameService.ts`
2. 🔄 Update game store to use SignalR events
3. 🔄 Update chat store to use SignalR
4. 🔄 Update video call store to use SignalR

### **Long Term (Final Phase)**
1. 🔄 Remove `gameService.ts` completely
2. 🔄 Remove all Socket.IO references
3. 🔄 Deploy to production and test end-to-end

---

## 🎯 **Key Benefits**

- **Automatic Environment Detection:** No manual configuration needed
- **Type Safety:** SignalR provides better TypeScript support
- **Better Performance:** ASP.NET Core SignalR is more efficient
- **Unified Backend:** Single API for all game functionality
- **Easy Debugging:** Environment indicator shows current status
- **Seamless Migration:** Gradual transition without breaking existing functionality

Your frontend is now ready to use the new ASP.NET Core SignalR API! 🎉
