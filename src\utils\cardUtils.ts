/**
 * Utility functions for card operations
 */

/**
 * Generates the path to the SVG file for a card
 * @param value The card value (6, 9, 10, J, Q, K, A)
 * @param suit The card suit (hearts, diamonds, clubs, spades)
 * @returns The path to the SVG file
 */
export function getCardImagePath(value: string, suit: string): string {
  // Validate inputs to prevent empty src attributes
  if (!value || !suit) {
    console.warn('Invalid card value or suit provided:', { value, suit });
    return getCardBackImagePath(); // Return card back as fallback
  }

  // Convert suit to first letter uppercase
  const suitFirstLetter = suit.charAt(0).toUpperCase();

  // Map card values to their SVG file names
  let cardValue = value;
  if (value === '10') {
    cardValue = 'T'; // 10 is represented as T in the SVG filenames
  }

  // Return the path to the SVG file
  return `/CardFace/${cardValue}${suitFirstLetter}.svg`;
}

/**
 * Gets the path to the card back image
 * @returns The path to the card back SVG file
 */
export function getCardBackImagePath(): string {
  return `/CardBack/card-back.svg`;
}
