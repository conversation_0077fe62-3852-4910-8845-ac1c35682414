using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface ITurnService
    {
        Task SetPlayerTurnAsync(string lobbyCode, string matchedLobbyCode, string playerId, bool isHandComplete = false);
        void ClearTurnTimer(Lobby lobby);
        Task StartTurnTimerAsync(Lobby lobby, string playerId);
        Task UpdateTurnTimerAsync(Lobby lobby);
        void StopTurnTimer(Lobby lobby);
        
        // Events
        event Func<string, PlayerTurnResponse, Task>? PlayerTurnUpdated;
        event Func<string, object, Task>? TurnTimerUpdated;
        event Func<string, object, Task>? TurnTimeout;
    }
}
