import { Competition, LeaderboardEntry, LeaderboardFilter } from "@/types/leaderboard";

// API base URL - automatically detects environment
const getApiBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Client-side detection
    const hostname = window.location.hostname;
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'http://localhost:5000/api';
    } else {
      return 'https://www.thuneeAPI.easygames.co.za/api';
    }
  }
  // Server-side fallback
  return 'http://localhost:5000/api';
};

const API_BASE_URL = getApiBaseUrl();

export const leaderboardService = {
  // Get all competitions
  getCompetitions: async (): Promise<Competition[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/competition`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.map((comp: any) => ({
        id: comp.id.toString(),
        name: comp.name,
        description: comp.description,
        startDate: comp.startDate,
        endDate: comp.endDate,
        prizes: {
          first: comp.prizes.first,
          second: comp.prizes.second,
          third: comp.prizes.third
        },
        status: comp.status
      }));
    } catch (error) {
      console.error('Error fetching competitions:', error);
      throw error;
    }
  },

  // Get a specific competition by ID
  getCompetition: async (id: string): Promise<Competition | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/competition/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return {
        id: data.id.toString(),
        name: data.name,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
        prizes: {
          first: data.prizes.first,
          second: data.prizes.second,
          third: data.prizes.third
        },
        status: data.status
      };
    } catch (error) {
      console.error('Error fetching competition:', error);
      throw error;
    }
  },

  // Get leaderboard for a competition with pagination and filters
  getLeaderboard: async (
    competitionId: string,
    page: number = 1,
    itemsPerPage: number = 10,
    filters?: LeaderboardFilter
  ): Promise<{
    entries: LeaderboardEntry[],
    totalItems: number
  }> => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: itemsPerPage.toString()
      });

      if (filters?.sortBy) {
        params.append('sortBy', filters.sortBy);
      }
      if (filters?.sortDirection) {
        params.append('sortDirection', filters.sortDirection);
      }
      if (filters?.minGames !== undefined) {
        params.append('minGames', filters.minGames.toString());
      }

      const response = await fetch(`${API_BASE_URL}/competition/${competitionId}/leaderboard?${params}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        entries: data.entries.map((entry: any) => ({
          id: entry.id.toString(),
          playerId: entry.userId.toString(),
          playerName: entry.playerName,
          rank: entry.rank,
          score: entry.totalScore,
          gamesPlayed: entry.gamesPlayed,
          gamesWon: entry.gamesWon,
          winRate: entry.winRate
        })),
        totalItems: data.totalItems
      };
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      throw error;
    }
  },

  // Save game result (called at the end of each game)
  saveGameResult: async (gameResult: {
    lobbyCode: string;
    competitionId?: number;
    winningTeam: number;
    team1Balls: number;
    team2Balls: number;
    totalBalls: number;
    playerResults: Array<{
      playerName: string;
      team: number;
      position: number;
      isWinner: boolean;
      ballsWon: number;
      totalPoints: number;
      jordhiCalls: number;
      successfulJordhiCalls: number;
      khanakCalls: number;
      successfulKhanakCalls: number;
      thuneeCalls: number;
      successfulThuneeCalls: number;
      doubleCalls: number;
      successfulDoubleCalls: number;
      fourBallPenalties: number;
      handsWon: number;
      totalHands: number;
      ballResults?: Array<{
        ballNumber: number;
        teamScore: number;
        opponentScore: number;
        won: boolean;
        specialCalls?: string;
      }>;
    }>;
    gameSettings?: string;
  }): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE_URL}/competition/save-game-result`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...gameResult,
          competitionId: gameResult.competitionId || 1 // Default to first competition
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Error saving game result:', error);
      return false;
    }
  }
};
