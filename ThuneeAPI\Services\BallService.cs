using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class BallService : IBallService
    {
        public string DetermineNextDealer(Lobby lobby, List<Player> allPlayers)
        {
            // Get the current dealer
            var currentDealerId = lobby.DealerId;
            if (string.IsNullOrEmpty(currentDealerId))
            {
                Console.WriteLine("No current dealer found");
                // Default to the first player if no dealer is set
                return allPlayers.FirstOrDefault()?.Id ?? "";
            }

            var currentDealer = allPlayers.FirstOrDefault(p => p.Id == currentDealerId);
            if (currentDealer == null)
            {
                Console.WriteLine($"Current dealer {currentDealerId} not found in players list");
                return allPlayers.FirstOrDefault()?.Id ?? "";
            }

            // Get ball scores
            var team1Balls = lobby.BallScores.GetValueOrDefault("team1", 0);
            var team2Balls = lobby.BallScores.GetValueOrDefault("team2", 0);

            var dealerTeam = currentDealer.Team;
            var dealerTeamBalls = dealerTeam == 1 ? team1Balls : team2Balls;
            var opponentTeamBalls = dealerTeam == 1 ? team2Balls : team1Balls;

            Console.WriteLine($"Dealer team {dealerTeam} has {dealerTeamBalls} balls, opponent team has {opponentTeamBalls} balls");

            // Rule: If the dealer-team's ball-score is greater than or equal to the opponents',
            // the deal passes to the right. If the dealer-team is still behind, the same dealer deals again.
            if (dealerTeamBalls >= opponentTeamBalls)
            {
                // Deal passes to the right (next player in clockwise order)
                var currentIndex = allPlayers.FindIndex(p => p.Id == currentDealerId);
                var nextIndex = (currentIndex + 1) % allPlayers.Count;
                var nextDealer = allPlayers[nextIndex];

                Console.WriteLine($"Deal passes to the right: {nextDealer.Name} (Team {nextDealer.Team})");
                return nextDealer.Id;
            }
            else
            {
                // Same dealer deals again
                Console.WriteLine($"Same dealer deals again: {currentDealer.Name} (Team {currentDealer.Team})");
                return currentDealerId;
            }
        }

        public BallCompletedResponse CalculateBallWinner(Lobby lobby, List<Player> allPlayers)
        {
            // Calculate points for each team
            var team1Points = lobby.BallPoints.GetValueOrDefault("team1", 0);
            var team2Points = lobby.BallPoints.GetValueOrDefault("team2", 0);

            // Determine winner based on points
            var winner = team1Points > team2Points ? 1 : 2;
            var ballsToAward = 1; // Standard ball award

            // Update ball scores
            var team1Balls = lobby.BallScores.GetValueOrDefault("team1", 0);
            var team2Balls = lobby.BallScores.GetValueOrDefault("team2", 0);

            if (winner == 1)
            {
                team1Balls += ballsToAward;
            }
            else
            {
                team2Balls += ballsToAward;
            }

            lobby.BallScores["team1"] = team1Balls;
            lobby.BallScores["team2"] = team2Balls;

            // Determine next dealer
            var nextDealerId = DetermineNextDealer(lobby, allPlayers);
            lobby.DealerId = nextDealerId;

            var ballId = lobby.CurrentBallId;

            return new BallCompletedResponse
            {
                BallId = ballId,
                Winner = winner,
                Points = new Dictionary<string, int>
                {
                    { "team1", team1Points },
                    { "team2", team2Points }
                },
                NextDealer = nextDealerId,
                BallScores = new Dictionary<string, int>
                {
                    { "team1", team1Balls },
                    { "team2", team2Balls }
                },
                FourBallAwarded = false,
                BallsAwarded = ballsToAward
            };
        }

        public bool CheckGameEnd(Lobby lobby)
        {
            var team1Balls = lobby.BallScores.GetValueOrDefault("team1", 0);
            var team2Balls = lobby.BallScores.GetValueOrDefault("team2", 0);

            // Check if there was a winning Khanak call that extends the game to 13 balls
            var ballLimit = lobby.WinningKhanakCall ? 13 : 12;

            Console.WriteLine($"Checking game end: Team 1: {team1Balls} balls, Team 2: {team2Balls} balls, Ball limit: {ballLimit}");

            // Game ends when a team reaches the ball limit
            return team1Balls >= ballLimit || team2Balls >= ballLimit;
        }

        public void InitializeGameHistory(Lobby lobby)
        {
            lobby.GameHistory = new List<BallHistory>();
        }

        public void AddBallToHistory(Lobby lobby, BallCompletedResponse ballData)
        {
            var historyEntry = new BallHistory
            {
                BallId = ballData.BallId,
                Winner = ballData.Winner,
                Points = ballData.Points,
                NextDealer = ballData.NextDealer,
                BallScores = ballData.BallScores,
                FourBallAwarded = ballData.FourBallAwarded,
                FourBallOption = ballData.FourBallOption,
                FourBallWinningTeam = ballData.FourBallWinningTeam,
                BallsAwarded = ballData.BallsAwarded,
                Timestamp = DateTime.UtcNow
            };

            lobby.GameHistory.Add(historyEntry);
        }

        public List<BallHistory> GetGameHistory(Lobby lobby)
        {
            return lobby.GameHistory ?? new List<BallHistory>();
        }

        public void MarkWinningKhanak(Lobby lobby, object khanakResult)
        {
            // This would check if the Khanak result indicates a winning call
            // For now, just mark as winning (placeholder implementation)
            lobby.WinningKhanakCall = true;
            Console.WriteLine("Marked winning Khanak call - game extended to 13 balls");
        }

        public (bool ApplySpecialRules, int BallsAwarded, int WinningTeam, string Outcome, string Reason) CheckSpecialBallLimitRules(Lobby lobby, int callerTeam, string callType)
        {
            var callerBalls = lobby.BallScores.GetValueOrDefault($"team{callerTeam}", 0);
            var ballLimit = lobby.WinningKhanakCall ? 13 : 12;
            var opposingTeam = callerTeam == 1 ? 2 : 1;

            // Special rules apply if:
            // - In a 12-ball game: Double/Khanak calls at 11-12 balls give opposite team 4 balls
            // - In a 13-ball game: Double calls at 12 balls and Khanak calls at 11-12 balls give opposite team 4 balls
            bool shouldApplySpecialRules = false;

            if (ballLimit == 12)
            {
                // 12-ball game
                shouldApplySpecialRules = (callType == "double" || callType == "khanak") && callerBalls >= 11;
            }
            else if (ballLimit == 13)
            {
                // 13-ball game
                shouldApplySpecialRules = (callType == "double" && callerBalls >= 12) ||
                                        (callType == "khanak" && callerBalls >= 11);
            }

            if (shouldApplySpecialRules)
            {
                Console.WriteLine($"Special ball limit rules apply! Team {callerTeam} called {callType} with {callerBalls} balls in a {ballLimit}-ball game. Opposing team gets 4 balls, caller gets none.");

                return (true, 4, opposingTeam, "special_ball_limit_rule", $"{callType} called with {callerBalls} balls in {ballLimit}-ball game");
            }

            return (false, 0, 0, "", "");
        }
    }
}
