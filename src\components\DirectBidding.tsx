"use client";

interface DirectBiddingProps {
  currentBid: number;
  onPlaceBid: (bid: number) => void;
  isOpposingTeam?: boolean;
  isPlacingBid: boolean;
}

export default function DirectBidding({
  currentBid,
  onPlaceBid,
  isOpposingTeam = false,
  isPlacingBid,
}: DirectBiddingProps) {
  // Calculate the next bid amount
  const getNextBid = () => {
    if (currentBid === 100 && isOpposingTeam) {
      return 104;
    } else if (currentBid >= 90) {
      return 100;
    } else {
      return currentBid + 10;
    }
  };

  const nextBid = getNextBid();
  const buttonText = currentBid === 100 && isOpposingTeam ? "+4" : "+10";

  // Check if we've reached the maximum bid
  const isMaxBid = (currentBid === 100 && !isOpposingTeam) || (currentBid === 104);

  return (
    <div className="w-full">
      <div className="flex flex-col items-center justify-center">
        <div className="text-center mb-4">
          <p className="text-white text-lg mb-2">Current Bid: <span className="text-[#E1C760] font-bold">{currentBid}</span></p>
          <p className="text-white text-sm">Next Bid: <span className="text-[#E1C760] font-bold">{nextBid}</span></p>
        </div>

        <button
          onClick={() => onPlaceBid(nextBid)}
          disabled={isPlacingBid || isMaxBid}
          className={`px-6 py-3 rounded-lg font-bold text-lg ${
            isPlacingBid || isMaxBid
              ? "bg-gray-700 text-gray-400 cursor-not-allowed"
              : "bg-[#E1C760] text-black hover:bg-[#d4bc5a]"
          }`}
        >
          {buttonText}
        </button>
      </div>
    </div>
  );
}
