"use client";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";

// ================= DEALING DIRECTIONS COMPONENT =================
type Direction = 'right' | 'up' | 'left' | 'down';
type DirectionCardState = {
  id: number;
  direction: Direction;
  dealt: boolean;
  x: number;
  y: number;
  rotation: number;
};

const DealingDirectionsComponent = ({
  cardCount,
  customBackUrl
}: {
  cardCount: 2 | 4;
  customBackUrl: string;
}) => {
  const [isDealing, setIsDealing] = useState(false);
  const [cards, setCards] = useState<DirectionCardState[]>([]);

  // Reset cards when card count changes
  useEffect(() => {
    resetCards();
  }, [cardCount]);

  const resetCards = () => {
    const directions: Direction[] = ['right', 'up', 'left', 'down'];
    const newCards: DirectionCardState[] = [];

    for (let i = 0; i < cardCount * 4; i++) {
      newCards.push({
        id: i,
        direction: directions[Math.floor(i / cardCount)],
        dealt: false,
        x: 0,
        y: 0,
        rotation: 0
      });
    }

    setCards(newCards);
    setIsDealing(false);
  };

  const dealCards = () => {
    if (isDealing) return;
    setIsDealing(true);

    // Reset cards to initial position
    resetCards();

    const dealCard = (index: number) => {
      if (index >= cards.length) {
        setIsDealing(false);
        return;
      }

      setTimeout(() => {
        setCards(prevCards => {
          const newCards = [...prevCards];
          const card = newCards[index];

          let x = 0;
          let y = 0;
          let rotation = 0;

          switch (card.direction) {
            case 'right':
              x = 150;
              rotation = Math.random() * 10 - 5;
              break;
            case 'up':
              y = -150;
              rotation = Math.random() * 10 - 5;
              break;
            case 'left':
              x = -150;
              rotation = Math.random() * 10 - 5;
              break;
            case 'down':
              y = 150;
              rotation = Math.random() * 10 - 5;
              break;
          }

          newCards[index] = {
            ...card,
            dealt: true,
            x,
            y,
            rotation
          };

          return newCards;
        });

        dealCard(index + 1);
      }, 800); // Slower animation (800ms per card)
    };

    dealCard(0);
  };

  return (
    <div className="w-full">
      <h2 className="text-xl font-bold text-white mb-4 text-center">Deal in 4 Directions</h2>

      <button
        onClick={dealCards}
        disabled={isDealing}
        className="px-6 py-3 bg-yellow-500 text-black font-bold rounded-lg mb-6 disabled:opacity-50 block mx-auto"
      >
        {isDealing ? 'Dealing...' : 'Deal Cards'}
      </button>

      <div className="relative w-96 h-96 border-2 border-white rounded-lg mx-auto">
        {/* Center point marker */}
        <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-red-500 rounded-full transform -translate-x-1/2 -translate-y-1/2" />

        {/* Cards */}
        {cards.map(card => (
          <motion.div
            key={card.id}
            className="absolute top-1/2 left-1/2 w-16 h-24 rounded-md shadow-lg transition-all duration-800 ease-out"
            style={{
              transform: `translate(-50%, -50%) translate(${card.x}px, ${card.y}px) rotate(${card.rotation}deg)`,
              backgroundImage: `url('${customBackUrl}')`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: card.dealt ? 1 : 0.5,
              zIndex: card.id
            }}
          />
        ))}
      </div>

      <div className="mt-4 text-white text-center">
        <p>Total cards: {cardCount * 4} ({cardCount} in each direction)</p>
        <p>Status: {isDealing ? 'Dealing in progress...' : 'Ready to deal'}</p>
      </div>
    </div>
  );
};

// ================= PLAYER RECEIVING CARDS COMPONENT =================
type PlayerCardState = {
  id: number;
  dealt: boolean;
  x: number;
  y: number;
  rotation: number;
  index: number;
};

const PlayerReceivingCardsComponent = ({
  cardCount,
  customBackUrl
}: {
  cardCount: 2 | 4;
  customBackUrl: string;
}) => {
  const [isDealing, setIsDealing] = useState(false);
  const [cards, setCards] = useState<PlayerCardState[]>([]);

  // Reset cards when card count changes
  useEffect(() => {
    resetCards();
  }, [cardCount]);

  const resetCards = () => {
    const newCards: PlayerCardState[] = [];

    for (let i = 0; i < cardCount; i++) {
      newCards.push({
        id: i,
        dealt: false,
        x: 0,
        y: 0,
        rotation: 0,
        index: i
      });
    }

    setCards(newCards);
    setIsDealing(false);
  };

  const dealCards = () => {
    if (isDealing) return;
    setIsDealing(true);

    // Reset cards to initial position
    resetCards();

    const dealCard = (index: number) => {
      if (index >= cards.length) {
        setIsDealing(false);
        return;
      }

      setTimeout(() => {
        setCards(prevCards => {
          const newCards = [...prevCards];
          const card = newCards[index];

          // Calculate the position for player's hand (fan out the cards)
          const fanWidth = cardCount <= 2 ? 100 : 180;
          const offset = fanWidth / (cardCount - 1 || 1);
          const x = (-fanWidth / 2) + (offset * card.index);
          const y = 120; // Position at bottom of the table
          const rotation = -10 + (20 / (cardCount - 1 || 1)) * card.index; // Slight fan rotation

          newCards[index] = {
            ...card,
            dealt: true,
            x,
            y,
            rotation
          };

          return newCards;
        });

        dealCard(index + 1);
      }, 800); // Slower animation (800ms per card)
    };

    dealCard(0);
  };

  return (
    <div className="w-full">
      <h2 className="text-xl font-bold text-white mb-4 text-center">Deal to Player</h2>

      <button
        onClick={dealCards}
        disabled={isDealing}
        className="px-6 py-3 bg-blue-500 text-white font-bold rounded-lg mb-6 disabled:opacity-50 block mx-auto"
      >
        {isDealing ? 'Dealing...' : 'Deal Cards to Player'}
      </button>

      <div className="relative w-96 h-96 border-2 border-white rounded-lg mx-auto">
        {/* Dealer position */}
        <div className="absolute top-1/2 left-1/2 w-12 h-12 rounded-full bg-yellow-600 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center">
          <span className="text-white font-bold">D</span>
        </div>

        {/* Player position */}
        <div className="absolute bottom-4 left-1/2 w-16 h-16 rounded-full bg-blue-600 transform -translate-x-1/2 flex items-center justify-center">
          <span className="text-white font-bold">P</span>
        </div>

        {/* Cards */}
        {cards.map(card => (
          <motion.div
            key={card.id}
            className="absolute top-1/2 left-1/2 w-16 h-24 rounded-md shadow-lg transition-all duration-800 ease-out"
            style={{
              transform: `translate(-50%, -50%) translate(${card.x}px, ${card.y}px) rotate(${card.rotation}deg)`,
              backgroundImage: `url('${customBackUrl}')`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: card.dealt ? 1 : 0.5,
              zIndex: card.id
            }}
          />
        ))}
      </div>

      <div className="mt-4 text-white text-center">
        <p>Player receiving {cardCount} cards</p>
        <p>Status: {isDealing ? 'Dealing in progress...' : 'Ready to deal'}</p>
      </div>
    </div>
  );
};

// ================= MAIN COMPONENT =================
export default function CardDealingDemo() {
  const [cardCount, setCardCount] = useState<2 | 4>(4);
  const [customBackUrl, setCustomBackUrl] = useState('/CardFaces/card-back.svg');
  const [activeTab, setActiveTab] = useState<'directions' | 'player'>('directions');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setCustomBackUrl(imageUrl);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-green-800 p-4">
      <h1 className="text-3xl font-bold text-white mb-8">Card Dealing Animations</h1>

      {/* Tab Navigation */}
      <div className="mb-6 flex space-x-4">
        <button
          onClick={() => setActiveTab('directions')}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            activeTab === 'directions'
              ? 'bg-yellow-500 text-black'
              : 'bg-gray-600 text-white hover:bg-gray-500'
          }`}
        >
          Deal in 4 Directions
        </button>
        <button
          onClick={() => setActiveTab('player')}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            activeTab === 'player'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-600 text-white hover:bg-gray-500'
          }`}
        >
          Deal to Player
        </button>
      </div>

      {/* Card Count Selection */}
      <div className="mb-6 flex space-x-4">
        <button
          onClick={() => setCardCount(2)}
          className={`px-4 py-2 rounded ${
            cardCount === 2 ? 'bg-white text-green-800' : 'bg-gray-200 text-gray-800'
          }`}
        >
          {activeTab === 'directions' ? '2 Cards per direction' : '2 Cards to player'}
        </button>
        <button
          onClick={() => setCardCount(4)}
          className={`px-4 py-2 rounded ${
            cardCount === 4 ? 'bg-white text-green-800' : 'bg-gray-200 text-gray-800'
          }`}
        >
          {activeTab === 'directions' ? '4 Cards per direction' : '4 Cards to player'}
        </button>
      </div>

      {/* Custom Card Back Upload */}
      <div className="mb-8">
        <label className="block text-white mb-2 font-semibold">Custom Card Back:</label>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="bg-white p-2 rounded border"
        />
      </div>

      {/* Render Active Component */}
      <div className="w-full max-w-md">
        {activeTab === 'directions' ? (
          <DealingDirectionsComponent
            cardCount={cardCount}
            customBackUrl={customBackUrl}
          />
        ) : (
          <PlayerReceivingCardsComponent
            cardCount={cardCount}
            customBackUrl={customBackUrl}
          />
        )}
      </div>

      <div className="mt-8 text-white text-center max-w-md">
        <p>This demo shows the different card dealing animations for the Thunee game.</p>
        <p className="mt-2">The dealer sees a 4-direction dealing animation where cards are dealt counter-clockwise to all 4 players.</p>
        <p className="mt-2">Other players see a simpler animation where cards are dealt directly from the dealer to their hand.</p>
        <p className="mt-4 text-[#E1C760]">In the real game, these animations start immediately when the dealer clicks the deal button and must complete before the next action can be taken.</p>
      </div>
    </div>
  );
}
