using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class GameService : IGameService
    {
        private readonly ILobbyService _lobbyService;
        private readonly ICardService _cardService;
        private readonly IBallService _ballService;
        private readonly ITurnService _turnService;
        private readonly ITimeframeService _timeframeService;

        public GameService(
            ILobbyService lobbyService,
            ICardService cardService,
            IBallService ballService,
            ITurnService turnService,
            ITimeframeService timeframeService)
        {
            _lobbyService = lobbyService;
            _cardService = cardService;
            _ballService = ballService;
            _turnService = turnService;
            _timeframeService = timeframeService;
        }

        // Events
        public event Func<string, TimeframeOptionsResponse, Task>? TimeframeOptionsUpdated;
        public event Func<string, TimeframeVoteResponse, Task>? TimeframeVoteUpdated;
        public event Func<string, CardDealtResponse, Task>? CardDealt;
        public event Func<string, DealerFoundResponse, Task>? DealerFound;
        public event Func<string, CardsDealtResponse, Task>? CardsDealt;
        public event Func<string, TrumpSelectedResponse, Task>? TrumpSelected;
        public event Func<string, PlayerTurnResponse, Task>? PlayerTurn;
        public event Func<string, CardPlayedResponse, Task>? CardPlayed;
        public event Func<string, HandCompletedResponse, Task>? HandCompleted;
        public event Func<string, BallCompletedResponse, Task>? BallCompleted;
        public event Func<string, GameEndedResponse, Task>? GameEnded;
        public event Func<string, JordhiCalledResponse, Task>? JordhiCalled;
        public event Func<string, JordhiCardsRevealedResponse, Task>? JordhiCardsRevealed;
        public event Func<string, FourBallResultResponse, Task>? FourBallResult;
        public event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        public event Func<string, Task>? DealerDeterminationReset;
        public event Func<string, DealerDeterminationStartedResponse, Task>? DealerDeterminationStarted;
        public event Func<string, DealingCardToResponse, Task>? DealingCardTo;
        public event Func<string, DealerDeterminationAbortedResponse, Task>? DealerDeterminationAborted;
        public event Func<string, FirstFourDealtResponse, Task>? FirstFourDealt;
        public event Func<string, ShuffleAnimationResponse, Task>? ShuffleAnimation;
        public event Func<string, ShuffleCompleteResponse, Task>? ShuffleComplete;
        public event Func<string, CutRequestedResponse, Task>? CutRequested;
        public event Func<string, CutCompleteResponse, Task>? CutComplete;

        public async Task<bool> VoteTimeframeAsync(string connectionId, int timeframe)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            var success = _timeframeService.RecordTimeframeVote(lobby, connectionId, timeframe);
            if (!success) return false;

            // Update vote results
            _timeframeService.UpdateVoteResults(lobby);

            // Check if all players have voted
            if (_timeframeService.AllPlayersVoted(lobby))
            {
                var selectedTimeframe = _timeframeService.DetermineSelectedTimeframe(lobby);
                if (selectedTimeframe.HasValue)
                {
                    // Store the selected timeframe
                    if (lobby.TimeframeVoting != null)
                    {
                        lobby.TimeframeVoting.SelectedTimeframe = selectedTimeframe.Value;
                        lobby.TimeframeVoting.IsComplete = true;
                    }

                    // Trigger event to notify all players
                    if (TimeframeVoteUpdated != null)
                    {
                        await TimeframeVoteUpdated(lobby.LobbyCode, new TimeframeVoteResponse
                        {
                            Votes = lobby.TimeframeVoting?.Votes ?? new Dictionary<string, int>(),
                            VoteCounts = lobby.TimeframeVoting?.VoteCounts ?? new Dictionary<int, int>(),
                            AllPlayersVoted = true,
                            SelectedTimeframe = selectedTimeframe.Value
                        });
                    }

                    // After a delay, transition to dealer determination phase
                    _ = Task.Delay(3000).ContinueWith(async _ =>
                    {
                        if (GamePhaseUpdated != null)
                        {
                            await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                            {
                                Phase = "dealer-determination",
                                Players = lobby.Players
                            });
                        }
                    });
                }
            }
            else
            {
                // Trigger event to update vote counts
                if (TimeframeVoteUpdated != null)
                {
                    await TimeframeVoteUpdated(lobby.LobbyCode, new TimeframeVoteResponse
                    {
                        Votes = lobby.TimeframeVoting?.Votes ?? new Dictionary<string, int>(),
                        VoteCounts = lobby.TimeframeVoting?.VoteCounts ?? new Dictionary<int, int>(),
                        AllPlayersVoted = false,
                        SelectedTimeframe = null
                    });
                }
            }

            return true;
        }

        public async Task<bool> RequestTimeframeOptionsAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
            var votingTimeLimit = lobby.TimeSettings?.VotingTimeLimit ?? 15;

            if (TimeframeOptionsUpdated != null)
            {
                await TimeframeOptionsUpdated(lobby.LobbyCode, new TimeframeOptionsResponse
                {
                    TimeOptions = timeOptions,
                    VotingTimeLimit = votingTimeLimit
                });
            }

            return true;
        }

        public async Task<bool> PlayCardAsync(string connectionId, Card card)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that it's the player's turn
            if (lobby.TurnTimerState?.CurrentPlayerId != connectionId)
            {
                return false;
            }

            // Find and remove the card from player's hand
            var playerCard = _cardService.FindCardInPlayerHand(connectionId, card, lobby.PlayerCards);
            if (playerCard == null) return false;

            var success = _cardService.RemoveCardFromPlayerHand(connectionId, card, lobby.PlayerCards);
            if (!success) return false;

            // Add card to current hand
            if (lobby.CurrentHandCards == null)
            {
                lobby.CurrentHandCards = new List<Card>();
            }

            playerCard.PlayedBy = connectionId;
            lobby.CurrentHandCards.Add(playerCard);

            // Trigger card played event
            if (CardPlayed != null)
            {
                await CardPlayed(lobby.LobbyCode, new CardPlayedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    Card = playerCard,
                    HandId = lobby.CurrentHandId,
                    HandCards = lobby.CurrentHandCards
                });
            }

            // Check if hand is complete (4 cards played)
            if (lobby.CurrentHandCards.Count == 4)
            {
                await ProcessHandCompletionAsync(lobby);
            }
            else
            {
                // Set next player's turn
                await SetNextPlayerTurnAsync(lobby);
            }

            return true;
        }

        public async Task<bool> SelectTrumpAsync(string connectionId, string trumpSuit)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that this player can select trump
            if (lobby.TrumpSelectorId != connectionId)
            {
                return false;
            }

            lobby.TrumpSuit = trumpSuit;

            // Trigger trump selected event
            if (TrumpSelected != null)
            {
                await TrumpSelected(lobby.LobbyCode, new TrumpSelectedResponse
                {
                    TrumpSuit = trumpSuit,
                    TrumpSelectorId = connectionId,
                    TrumpSelectorName = player.Name
                });
            }

            return true;
        }

        public async Task<bool> BidAsync(string connectionId, int bidAmount)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Initialize bidding state if not exists
            if (lobby.BiddingState == null)
            {
                lobby.BiddingState = new BiddingState
                {
                    CurrentBid = 0,
                    CurrentBidder = null,
                    BiddingComplete = false,
                    TeamBids = new Dictionary<int, int>(),
                    TeamBidders = new Dictionary<int, string>()
                };
            }

            // Validate bid amount
            if (bidAmount <= lobby.BiddingState.CurrentBid || bidAmount < 10 || bidAmount > 104)
            {
                return false;
            }

            // Check if this team has already bid
            if (lobby.BiddingState.TeamBidders.ContainsKey(player.Team))
            {
                return false;
            }

            // Record the bid
            lobby.BiddingState.CurrentBid = bidAmount;
            lobby.BiddingState.CurrentBidder = connectionId;
            lobby.BiddingState.TeamBids[player.Team] = bidAmount;
            lobby.BiddingState.TeamBidders[player.Team] = connectionId;

            // Set trump selector
            lobby.TrumpSelectorId = connectionId;

            return true;
        }

        public async Task<bool> PassBidAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Initialize bidding state if not exists
            if (lobby.BiddingState == null)
            {
                lobby.BiddingState = new BiddingState
                {
                    CurrentBid = 0,
                    CurrentBidder = null,
                    BiddingComplete = false,
                    TeamBids = new Dictionary<int, int>(),
                    TeamBidders = new Dictionary<int, string>()
                };
            }

            // Mark team as passed (by not adding to TeamBidders)
            // Check if bidding should complete
            var teamsWithBids = lobby.BiddingState.TeamBidders.Count;
            var totalTeams = 2;

            if (teamsWithBids >= 1 && lobby.BiddingState.TeamBidders.Count + 1 >= totalTeams)
            {
                // Bidding is complete
                lobby.BiddingState.BiddingComplete = true;

                // Determine trump selector (highest bidder)
                var highestBidTeam = lobby.BiddingState.TeamBids.OrderByDescending(kvp => kvp.Value).First();
                lobby.TrumpSelectorId = lobby.BiddingState.TeamBidders[highestBidTeam.Key];

                // Update target scores
                var opposingTeam = highestBidTeam.Key == 1 ? 2 : 1;
                lobby.TargetScores = new Dictionary<string, int>
                {
                    { $"team{opposingTeam}", 105 - highestBidTeam.Value }
                };

                // Trigger bidding complete event
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "select-trump",
                        Players = lobby.Players
                    });
                }
            }

            return true;
        }

        public async Task<bool> ShuffleCardsAsync(string connectionId, string shuffleType)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Validate that this player can shuffle (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                return false;
            }

            // Create and shuffle a new deck
            var deck = _cardService.CreateGameDeck();
            lobby.GameDeck = _cardService.ShuffleDeck(deck);

            // Trigger shuffle animation event
            if (ShuffleAnimation != null)
            {
                await ShuffleAnimation(lobby.LobbyCode, new ShuffleAnimationResponse
                {
                    ShuffleType = shuffleType,
                    DealerId = connectionId
                });
            }

            // After animation delay, trigger shuffle complete and move to cut phase
            _ = Task.Delay(3000).ContinueWith(async _ =>
            {
                if (ShuffleComplete != null)
                {
                    await ShuffleComplete(lobby.LobbyCode, new ShuffleCompleteResponse
                    {
                        DealerId = connectionId
                    });
                }

                // Transition to cut phase
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "cut",
                        Players = lobby.Players
                    });
                }

                // Request cut from the player to the left of dealer using position-based logic
                var dealer = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
                if (dealer != null && dealer.Position > 0)
                {
                    // Get the position to the left of the dealer
                    var cutterPosition = GetPositionToLeft(dealer.Position);
                    var cutter = lobby.Players.FirstOrDefault(p => p.Position == cutterPosition);

                    if (cutter != null && CutRequested != null)
                    {
                        await CutRequested(lobby.LobbyCode, new CutRequestedResponse
                        {
                            PlayerId = cutter.Id,
                            PlayerName = cutter.Name
                        });
                    }
                }
                else
                {
                    // Fallback to index-based approach if positions are not set
                    var dealerIndex = lobby.Players.FindIndex(p => p.Id == connectionId);
                    var cutterIndex = (dealerIndex + 1) % lobby.Players.Count;
                    var cutter = lobby.Players[cutterIndex];

                    if (CutRequested != null)
                    {
                        await CutRequested(lobby.LobbyCode, new CutRequestedResponse
                        {
                            PlayerId = cutter.Id,
                            PlayerName = cutter.Name
                        });
                    }
                }
            });

            return true;
        }

        public async Task<bool> RequestCutAsync(string connectionId, string playerId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Validate that this player can request a cut (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                return false;
            }

            // Find the player who should cut using position-based logic
            var dealer = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (dealer == null) return false;

            Player? cutter = null;

            // If a specific player ID is provided, use that player
            if (!string.IsNullOrEmpty(playerId))
            {
                cutter = lobby.Players.FirstOrDefault(p => p.Id == playerId);
            }
            else if (dealer.Position > 0)
            {
                // Use position-based logic to find the player to the left of dealer
                var cutterPosition = GetPositionToLeft(dealer.Position);
                cutter = lobby.Players.FirstOrDefault(p => p.Position == cutterPosition);
            }
            else
            {
                // Fallback to index-based approach if positions are not set
                var dealerIndex = lobby.Players.FindIndex(p => p.Id == connectionId);
                var cutterIndex = (dealerIndex + 1) % lobby.Players.Count;
                cutter = lobby.Players[cutterIndex];
            }

            if (cutter == null) return false;

            // Trigger cut requested event
            if (CutRequested != null)
            {
                await CutRequested(lobby.LobbyCode, new CutRequestedResponse
                {
                    PlayerId = cutter.Id,
                    PlayerName = cutter.Name
                });
            }

            return true;
        }

        public async Task<bool> CutCardsAsync(string connectionId, int cutPosition)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            if (lobby.GameDeck == null || lobby.GameDeck.Count == 0)
            {
                return false;
            }

            // Cut the deck
            lobby.GameDeck = _cardService.CutDeck(lobby.GameDeck, cutPosition);

            // Trigger cut complete event
            if (CutComplete != null)
            {
                await CutComplete(lobby.LobbyCode, new CutCompleteResponse
                {
                    Cut = true,
                    Position = cutPosition.ToString(),
                    PlayerId = connectionId,
                    PlayerName = player.Name
                });
            }

            // After animation delay, move to deal first four phase
            _ = Task.Delay(1500).ContinueWith(async _ =>
            {
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "deal-first-four",
                        Players = lobby.Players
                    });
                }
            });

            return true;
        }

        public async Task<bool> SkipCutAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Trigger cut complete event with no cut
            if (CutComplete != null)
            {
                await CutComplete(lobby.LobbyCode, new CutCompleteResponse
                {
                    Cut = false,
                    Position = null,
                    PlayerId = connectionId,
                    PlayerName = player.Name
                });
            }

            // Move to deal first four phase immediately
            if (GamePhaseUpdated != null)
            {
                await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                {
                    Phase = "deal-first-four",
                    Players = lobby.Players
                });
            }

            return true;
        }

        public async Task<bool> DealCardsAsync(string connectionId, int cardsPerPlayer)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Validate that this player can deal (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                return false;
            }

            if (lobby.GameDeck == null || lobby.GameDeck.Count == 0)
            {
                // Create a new deck if none exists
                lobby.GameDeck = _cardService.CreateGameDeck();
            }

            // Deal cards with individual card events for animation
            await DealCardsWithAnimationAsync(lobby, cardsPerPlayer);

            return true;
        }

        private async Task DealCardsWithAnimationAsync(Lobby lobby, int cardsPerPlayer)
        {
            var deckCopy = new List<Card>(lobby.GameDeck);
            var allDealtCards = new HashSet<string>();

            // Initialize player cards if not already done
            if (lobby.PlayerCards == null)
            {
                lobby.PlayerCards = new Dictionary<string, List<Card>>();
            }

            foreach (var player in lobby.Players)
            {
                if (!lobby.PlayerCards.ContainsKey(player.Id))
                {
                    lobby.PlayerCards[player.Id] = new List<Card>();
                }
            }

            // Deal cards in rounds (one card to each player at a time) with animation
            for (int round = 0; round < cardsPerPlayer; round++)
            {
                foreach (var player in lobby.Players)
                {
                    if (deckCopy.Count > 0)
                    {
                        Card? card = null;
                        string cardKey;

                        // Find a unique card that hasn't been dealt yet
                        do
                        {
                            if (deckCopy.Count == 0) break;

                            card = deckCopy[0];
                            deckCopy.RemoveAt(0);
                            cardKey = $"{card.Value}_{card.Suit}";

                            // If this card has already been dealt, try another
                            if (allDealtCards.Contains(cardKey))
                            {
                                Console.WriteLine($"Card {card.Value} of {card.Suit} already dealt, trying another");
                                card = null;
                            }
                        } while (card == null && deckCopy.Count > 0);

                        if (card != null)
                        {
                            lobby.PlayerCards[player.Id].Add(card);
                            allDealtCards.Add($"{card.Value}_{card.Suit}");

                            // Emit individual card dealt event for animation
                            if (CardDealt != null)
                            {
                                await CardDealt(lobby.LobbyCode, new CardDealtResponse
                                {
                                    PlayerId = player.Id,
                                    Card = card,
                                    IsDealer = player.IsDealer
                                });
                            }

                            // Small delay between each card for animation
                            await Task.Delay(200);
                        }
                    }
                }
            }

            // After all cards are dealt, trigger completion event
            if (CardsDealt != null)
            {
                await CardsDealt(lobby.LobbyCode, new CardsDealtResponse
                {
                    PlayerCards = lobby.PlayerCards,
                    CardsPerPlayer = cardsPerPlayer,
                    DealingComplete = true
                });
            }

            // Update the lobby's deck to remove dealt cards
            lobby.GameDeck = deckCopy;

            // Trigger appropriate phase transitions based on cards dealt
            if (cardsPerPlayer == 4)
            {
                // After dealing first 4 cards, wait and then trigger bidding phase
                _ = Task.Delay(5000).ContinueWith(async _ =>
                {
                    if (FirstFourDealt != null)
                    {
                        await FirstFourDealt(lobby.LobbyCode, new FirstFourDealtResponse());
                    }

                    if (GamePhaseUpdated != null)
                    {
                        await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                        {
                            Phase = "bidding",
                            Players = lobby.Players
                        });
                    }
                });
            }
            else if (cardsPerPlayer == 2)
            {
                // After dealing final 2 cards, wait and then start gameplay
                _ = Task.Delay(3000).ContinueWith(async _ =>
                {
                    // Determine first player (to the right of trump selector)
                    var trumpSelector = lobby.Players.FirstOrDefault(p => p.IsTrumpSelector);
                    if (trumpSelector != null)
                    {
                        var trumpSelectorIndex = lobby.Players.FindIndex(p => p.Id == trumpSelector.Id);
                        var firstPlayerIndex = (trumpSelectorIndex + 1) % lobby.Players.Count;
                        var firstPlayer = lobby.Players[firstPlayerIndex];
                        lobby.FirstPlayerId = firstPlayer.Id;

                        // Start the first player's turn
                        await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, firstPlayer.Id);
                    }
                });
            }
        }

        public async Task<bool> CallJordhiAsync(string connectionId, int value, string jordhiSuit, List<Card> jordhiCards)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate Jordhi call
            var isValidCards = ValidateJordhiCards(jordhiCards, jordhiSuit, value, lobby.TrumpSuit);
            var handsWonByTeam = CountHandsWonByTeam(lobby, player.Team);
            var isValidHandCount = handsWonByTeam > 0; // Team must have won at least one hand
            var isFullyValid = isValidCards && isValidHandCount;

            // Create Jordhi call record
            var jordhiCall = new JordhiCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                Value = value,
                HandNumber = handsWonByTeam,
                IsValidCards = isValidCards,
                IsValidHandCount = isValidHandCount,
                IsFullyValid = isFullyValid,
                JordhiSuit = jordhiSuit,
                JordhiCards = isValidCards ? jordhiCards : new List<Card>()
            };

            lobby.JordhiCalls.Add(jordhiCall);

            // Adjust target scores if valid
            if (isFullyValid)
            {
                AdjustTargetScoresForJordhi(lobby, player.Team, value);
            }

            // Trigger Jordhi called event
            if (JordhiCalled != null)
            {
                await JordhiCalled(lobby.LobbyCode, new JordhiCalledResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    Value = value,
                    HandNumber = handsWonByTeam,
                    TargetScores = lobby.TargetScores ?? new Dictionary<string, int> { { "team1", 105 }, { "team2", 105 } },
                    IsValidCards = isValidCards,
                    IsValidHandCount = isValidHandCount,
                    IsFullyValid = isFullyValid,
                    JordhiSuit = jordhiSuit,
                    JordhiCards = isValidCards ? jordhiCards : new List<Card>()
                });
            }

            return true;
        }

        public async Task<bool> RevealJordhiAsync(string connectionId, int jordhiValue, string jordhiSuit, List<Card> jordhiCards, bool revealCards)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Find the Jordhi call to reveal
            var jordhiCall = lobby.JordhiCalls.LastOrDefault(j => j.PlayerId == connectionId && j.Value == jordhiValue);
            if (jordhiCall == null) return false;

            // Only allow revealing valid Jordhi calls
            if (!jordhiCall.IsFullyValid) return false;

            // Trigger Jordhi cards revealed event
            if (JordhiCardsRevealed != null)
            {
                await JordhiCardsRevealed(lobby.LobbyCode, new JordhiCardsRevealedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    JordhiValue = jordhiValue,
                    JordhiSuit = jordhiSuit,
                    JordhiCards = jordhiCards,
                    RevealCards = revealCards
                });
            }

            return true;
        }

        public async Task<bool> CallDoubleAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate Double call - only valid in last hand if opposite team hasn't taken any hands
            if (lobby.CurrentHandId != 6) return false; // Must be last hand

            var oppositeTeam = player.Team == 1 ? 2 : 1;
            var oppositeTeamHands = CountHandsWonByTeam(lobby, oppositeTeam);
            if (oppositeTeamHands > 0) return false; // Opposite team must not have won any hands

            // Create Double call record
            var doubleCall = new DoubleCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                HandNumber = lobby.CurrentHandId,
                IsValid = true
            };

            lobby.DoubleCalls.Add(doubleCall);

            // Mark that Double was called for ball completion processing
            lobby.DoubleCallerId = connectionId;

            return true;
        }

        public async Task<bool> CallKhanakAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate Khanak call - only valid in last trick if calling team has made at least one valid Jordhi call
            // and opposing team has won at least one trick
            if (lobby.CurrentHandId != 6) return false; // Must be last hand

            var teamJordhiPoints = lobby.JordhiCalls.Where(j => j.PlayerTeam == player.Team && j.IsFullyValid).Sum(j => j.Value);
            if (teamJordhiPoints == 0) return false; // Team must have made at least one valid Jordhi call

            var oppositeTeam = player.Team == 1 ? 2 : 1;
            var oppositeTeamHands = CountHandsWonByTeam(lobby, oppositeTeam);
            if (oppositeTeamHands == 0) return false; // Opposing team must have won at least one trick

            var oppositeTeamJordhiPoints = lobby.JordhiCalls.Where(j => j.PlayerTeam == oppositeTeam && j.IsFullyValid).Sum(j => j.Value);
            var threshold = teamJordhiPoints + 10 - oppositeTeamJordhiPoints;

            // Create Khanak call record
            var khanakCall = new KhanakCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                HandNumber = lobby.CurrentHandId,
                Threshold = threshold,
                IsValid = true
            };

            lobby.KhanakCalls.Add(khanakCall);

            // Mark that Khanak was called for ball completion processing
            lobby.KhanakCallerId = connectionId;

            return true;
        }

        public async Task<bool> CallThuneeAsync(string connectionId, Card firstCard)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that player has the card they want to play
            if (!lobby.PlayerCards.ContainsKey(connectionId) ||
                !lobby.PlayerCards[connectionId].Any(c => c.Suit == firstCard.Suit && c.Value == firstCard.Value))
            {
                return false;
            }

            // Set trump suit to the suit of the first card
            lobby.TrumpSuit = firstCard.Suit;

            // Create Thunee call record
            var thuneeCall = new ThuneeCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                FirstCard = firstCard,
                TrumpSuit = firstCard.Suit
            };

            lobby.ThuneeCalls.Add(thuneeCall);

            // Mark Thunee opportunities as complete
            lobby.ThuneeOpportunitiesComplete = true;

            // Set this player as the first to play
            lobby.FirstPlayerId = connectionId;

            // Start the player's turn with timer
            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, connectionId);

            return true;
        }

        public async Task<bool> FourBallAsync(string connectionId, string ballType, string option, string accusedPlayerId, int handNumber)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var accuser = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            var accused = lobby.Players.FirstOrDefault(p => p.Id == accusedPlayerId);
            if (accuser == null || accused == null) return false;

            bool isValid = false;
            int winningTeam = accused.Team; // Default to accused team winning if accusation is invalid
            string reason = "";

            // Validate the 4-ball claim based on option
            switch (option)
            {
                case "Never follow suit":
                    isValid = ValidateNeverFollowSuit(lobby, accusedPlayerId, handNumber);
                    reason = isValid ? "Player did not follow suit when they could have" : "Player followed suit correctly";
                    break;
                case "Under chopped":
                    isValid = ValidateUnderChopped(lobby, accusedPlayerId, handNumber);
                    reason = isValid ? "Player played lower trump when they had higher trump" : "Player played correctly";
                    break;
                default:
                    return false;
            }

            // Determine winning team - if accusation is valid, accuser's team wins; otherwise accused team wins
            winningTeam = isValid ? accuser.Team : accused.Team;

            // Award 4 balls to the winning team
            var teamKey = $"team{winningTeam}";
            lobby.BallScores[teamKey] = 4;

            // Reset the other team's score to 0
            var otherTeam = winningTeam == 1 ? 2 : 1;
            var otherTeamKey = $"team{otherTeam}";
            lobby.BallScores[otherTeamKey] = 0;

            // Create 4-ball call record
            var fourBallCall = new FourBallCall
            {
                PlayerId = connectionId,
                PlayerName = accuser.Name,
                PlayerTeam = accuser.Team,
                AccusedPlayerId = accusedPlayerId,
                AccusedPlayerName = accused.Name,
                AccusedTeam = accused.Team,
                Option = option,
                HandNumber = handNumber,
                IsValid = isValid
            };

            lobby.FourBallCalls.Add(fourBallCall);

            // Get the selected hand cards for display
            var selectedHand = lobby.Hands.FirstOrDefault(h => h.Id == handNumber);
            var selectedHandCards = selectedHand?.Cards ?? new List<Card>();

            // Trigger 4-ball result event
            if (FourBallResult != null)
            {
                await FourBallResult(lobby.LobbyCode, new FourBallResultResponse
                {
                    BallType = ballType,
                    Option = option,
                    TargetPlayer = accusedPlayerId,
                    TargetPlayerName = accused.Name,
                    TargetTeam = accused.Team,
                    AccuserId = connectionId,
                    AccuserName = accuser.Name,
                    AccuserTeam = accuser.Team,
                    HandNumber = handNumber,
                    IsValid = isValid,
                    WinningTeam = winningTeam,
                    BallsAwarded = 4,
                    BallScores = lobby.BallScores,
                    Reason = reason,
                    SelectedHandCards = selectedHandCards
                });
            }

            return true;
        }

        public Task<bool> SetDealerAsync(string connectionId, string dealerId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return Task.FromResult(false);

            lobby.DealerId = dealerId;
            lobby.DealerFound = true;

            var dealer = lobby.Players.FirstOrDefault(p => p.Id == dealerId);
            if (dealer != null)
            {
                dealer.IsDealer = true;
            }

            return Task.FromResult(true);
        }

        public async Task<bool> StartDealerDeterminationAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Get matched lobby if it exists
            Lobby? matchedLobby = null;
            if (!string.IsNullOrEmpty(lobby.MatchedLobby))
            {
                matchedLobby = _lobbyService.GetLobby(lobby.MatchedLobby);
            }

            // Check if dealer determination is already in progress in either lobby
            if (lobby.DealerDeterminationInProgress ||
                (matchedLobby != null && matchedLobby.DealerDeterminationInProgress))
            {
                return false;
            }

            // Get all players from both lobbies
            var allPlayers = new List<Player>();

            // Add players from current lobby
            if (lobby.Teams.ContainsKey(1))
                allPlayers.AddRange(lobby.Teams[1]);
            if (lobby.Teams.ContainsKey(2))
                allPlayers.AddRange(lobby.Teams[2]);

            // Add players from matched lobby if it exists
            if (matchedLobby != null)
            {
                if (matchedLobby.Teams.ContainsKey(1))
                    allPlayers.AddRange(matchedLobby.Teams[1]);
                if (matchedLobby.Teams.ContainsKey(2))
                    allPlayers.AddRange(matchedLobby.Teams[2]);
            }

            // Remove duplicates and ensure we have exactly 4 players
            allPlayers = allPlayers.GroupBy(p => p.Id).Select(g => g.First()).ToList();
            if (allPlayers.Count != 4)
            {
                return false;
            }

            // Create a shuffled deck for dealer determination
            var dealerDeck = _cardService.CreateGameDeck();

            // Choose a random starting player index (0-3)
            var random = new Random();
            var startingPlayerIndex = random.Next(allPlayers.Count);

            // Set dealer determination state in both lobbies
            lobby.DealerDeck = dealerDeck;
            lobby.CurrentDealerPlayerIndex = startingPlayerIndex;
            lobby.DealerFound = false;
            lobby.DealerId = null;
            lobby.DealerDeterminationInProgress = true;

            // Initialize cards dealt counter for each player
            lobby.CardsDealtPerPlayer = new Dictionary<string, int>();
            foreach (var player in allPlayers)
            {
                lobby.CardsDealtPerPlayer[player.Id] = 0;
            }

            // Sync state with matched lobby
            if (matchedLobby != null)
            {
                matchedLobby.DealerDeck = new List<Card>(dealerDeck); // Copy the deck
                matchedLobby.CurrentDealerPlayerIndex = startingPlayerIndex;
                matchedLobby.DealerFound = false;
                matchedLobby.DealerId = null;
                matchedLobby.DealerDeterminationInProgress = true;
                matchedLobby.CardsDealtPerPlayer = new Dictionary<string, int>(lobby.CardsDealtPerPlayer);
            }

            // First, reset any existing dealer determination state
            if (DealerDeterminationReset != null)
            {
                await DealerDeterminationReset(lobby.LobbyCode);
                if (matchedLobby != null)
                {
                    await DealerDeterminationReset(matchedLobby.LobbyCode);
                }
            }

            // After a short delay, send the dealer determination data
            await Task.Delay(1000);

            // Send the dealer determination data to all clients
            var dealerDeterminationData = new DealerDeterminationStartedResponse
            {
                Players = allPlayers,
                CurrentPlayerIndex = startingPlayerIndex
            };

            if (DealerDeterminationStarted != null)
            {
                await DealerDeterminationStarted(lobby.LobbyCode, dealerDeterminationData);
                if (matchedLobby != null)
                {
                    await DealerDeterminationStarted(matchedLobby.LobbyCode, dealerDeterminationData);
                }
            }

            // Start the dealer determination process after another short delay
            await Task.Delay(1000);
            await DealNextCardAsync(lobby, matchedLobby, allPlayers);

            return true;
        }

        private async Task DealNextCardAsync(Lobby lobby, Lobby? matchedLobby, List<Player> allPlayers)
        {
            // If dealer already found, do nothing
            if (lobby.DealerFound)
            {
                lobby.DealerDeterminationInProgress = false;
                if (matchedLobby != null)
                {
                    matchedLobby.DealerDeterminationInProgress = false;
                }
                return;
            }

            // Safety check for deck
            if (lobby.DealerDeck == null || lobby.DealerDeck.Count == 0)
            {
                // Abort dealer determination
                if (DealerDeterminationAborted != null)
                {
                    await DealerDeterminationAborted(lobby.LobbyCode, new DealerDeterminationAbortedResponse
                    {
                        Reason = "No cards left in deck"
                    });
                    if (matchedLobby != null)
                    {
                        await DealerDeterminationAborted(matchedLobby.LobbyCode, new DealerDeterminationAbortedResponse
                        {
                            Reason = "No cards left in deck"
                        });
                    }
                }
                lobby.DealerDeterminationInProgress = false;
                if (matchedLobby != null)
                {
                    matchedLobby.DealerDeterminationInProgress = false;
                }
                return;
            }

            // Get the current player
            var currentPlayerIndex = lobby.CurrentDealerPlayerIndex;
            if (currentPlayerIndex >= allPlayers.Count)
            {
                lobby.DealerDeterminationInProgress = false;
                return;
            }

            var currentPlayer = allPlayers[currentPlayerIndex];

            // Check if this player already has too many cards (safety check)
            if (lobby.CardsDealtPerPlayer.ContainsKey(currentPlayer.Id) &&
                lobby.CardsDealtPerPlayer[currentPlayer.Id] >= 24)
            {
                // Move to the next player
                lobby.CurrentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.Count;
                if (matchedLobby != null)
                {
                    matchedLobby.CurrentDealerPlayerIndex = lobby.CurrentDealerPlayerIndex;
                }

                // Schedule the next card deal
                await Task.Delay(500);
                await DealNextCardAsync(lobby, matchedLobby, allPlayers);
                return;
            }

            // First, notify all clients that we're about to deal a card to this player
            var dealingCardToResponse = new DealingCardToResponse
            {
                PlayerId = currentPlayer.Id,
                PlayerName = currentPlayer.Name,
                PlayerTeam = currentPlayer.Team,
                PlayerIndex = currentPlayerIndex
            };

            if (DealingCardTo != null)
            {
                await DealingCardTo(lobby.LobbyCode, dealingCardToResponse);
                if (matchedLobby != null)
                {
                    await DealingCardTo(matchedLobby.LobbyCode, dealingCardToResponse);
                }
            }

            // After a short delay, deal the actual card
            await Task.Delay(500);

            // Get the next card from the deck
            var card = lobby.DealerDeck.Last();
            lobby.DealerDeck.RemoveAt(lobby.DealerDeck.Count - 1);

            // Sync deck state with matched lobby
            if (matchedLobby != null)
            {
                matchedLobby.DealerDeck = new List<Card>(lobby.DealerDeck);
            }

            // Check if this is a black Jack (Jack of Clubs or Jack of Spades)
            var isBlackJack = card.Value == "J" && (card.Suit == "clubs" || card.Suit == "spades");

            // Increment the card count for this player
            if (!lobby.CardsDealtPerPlayer.ContainsKey(currentPlayer.Id))
                lobby.CardsDealtPerPlayer[currentPlayer.Id] = 0;
            lobby.CardsDealtPerPlayer[currentPlayer.Id]++;

            // Sync card count with matched lobby
            if (matchedLobby != null)
            {
                matchedLobby.CardsDealtPerPlayer = new Dictionary<string, int>(lobby.CardsDealtPerPlayer);
            }

            // Broadcast the card dealt to all players
            var cardDealtResponse = new CardDealtResponse
            {
                PlayerId = currentPlayer.Id,
                PlayerName = currentPlayer.Name,
                PlayerTeam = currentPlayer.Team,
                PlayerIndex = currentPlayerIndex,
                Card = card,
                IsDealer = isBlackJack,
                CardsDealtToPlayer = lobby.CardsDealtPerPlayer.GetValueOrDefault(currentPlayer.Id, 0),
                TotalCardsDealt = lobby.CardsDealtPerPlayer.Values.Sum()
            };

            if (CardDealt != null)
            {
                await CardDealt(lobby.LobbyCode, cardDealtResponse);
                if (matchedLobby != null)
                {
                    await CardDealt(matchedLobby.LobbyCode, cardDealtResponse);
                }
            }

            // If this is a black Jack, we found our dealer
            if (isBlackJack)
            {
                lobby.DealerFound = true;
                lobby.DealerId = currentPlayer.Id;

                // Sync dealer state with matched lobby
                if (matchedLobby != null)
                {
                    matchedLobby.DealerFound = true;
                    matchedLobby.DealerId = currentPlayer.Id;
                }

                // Assign fixed positions to players based on the dealer
                // This is crucial for determining the initial trumper correctly
                var positionedPlayers = AssignPositionsBasedOnDealer(allPlayers, currentPlayer.Id);

                // Determine the trump selector using position-based logic
                // In Thunee, the trump selector is always at position 2 (to the right of dealer at position 3)
                var trumpSelectorPosition = GetInitialTrumperPosition();
                var trumpSelector = positionedPlayers.FirstOrDefault(p => p.Position == trumpSelectorPosition);

                // If position-based selection fails, fall back to team-based selection
                if (trumpSelector == null)
                {
                    var team = currentPlayer.Team;
                    var oppositeTeam = team == 1 ? 2 : 1;
                    var oppositeTeamPlayers = positionedPlayers.Where(p => p.Team == oppositeTeam).ToList();

                    // Choose the first player from the opposite team as trump selector
                    trumpSelector = oppositeTeamPlayers.FirstOrDefault();
                    if (trumpSelector == null)
                    {
                        // Fallback: use the dealer if no opposite team players
                        trumpSelector = currentPlayer;
                    }
                }

                // Update the players in the lobby with their assigned positions
                foreach (var player in lobby.Players)
                {
                    var positionedPlayer = positionedPlayers.FirstOrDefault(p => p.Id == player.Id);
                    if (positionedPlayer != null)
                    {
                        player.Position = positionedPlayer.Position;
                    }
                }

                // Also update matched lobby if it exists
                if (matchedLobby != null)
                {
                    foreach (var player in matchedLobby.Players)
                    {
                        var positionedPlayer = positionedPlayers.FirstOrDefault(p => p.Id == player.Id);
                        if (positionedPlayer != null)
                        {
                            player.Position = positionedPlayer.Position;
                        }
                    }
                }

                // Broadcast dealer found to all players
                var dealerFoundResponse = new DealerFoundResponse
                {
                    DealerId = currentPlayer.Id,
                    DealerName = currentPlayer.Name,
                    DealerTeam = currentPlayer.Team,
                    TrumpSelectorId = trumpSelector.Id,
                    TrumpSelectorName = trumpSelector.Name,
                    TrumpSelectorTeam = trumpSelector.Team,
                    DealerCard = card,
                    Players = positionedPlayers.Select(p => new Player
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Team = p.Team,
                        Position = p.Position,
                        IsDealer = p.Id == currentPlayer.Id,
                        IsTrumpSelector = p.Id == trumpSelector.Id
                    }).ToList()
                };

                if (DealerFound != null)
                {
                    await DealerFound(lobby.LobbyCode, dealerFoundResponse);
                    if (matchedLobby != null)
                    {
                        await DealerFound(matchedLobby.LobbyCode, dealerFoundResponse);
                    }
                }

                lobby.DealerDeterminationInProgress = false;
                if (matchedLobby != null)
                {
                    matchedLobby.DealerDeterminationInProgress = false;
                }
                return;
            }

            // Move to the next player in clockwise order
            lobby.CurrentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.Count;
            if (matchedLobby != null)
            {
                matchedLobby.CurrentDealerPlayerIndex = lobby.CurrentDealerPlayerIndex;
            }

            // Schedule the next card deal after a delay
            await Task.Delay(1500);
            await DealNextCardAsync(lobby, matchedLobby, allPlayers);
        }

        public async Task<bool> PassThuneeAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Mark that this player has passed Thunee
            if (lobby.ThuneePassedPlayers == null)
            {
                lobby.ThuneePassedPlayers = new List<string>();
            }

            if (!lobby.ThuneePassedPlayers.Contains(connectionId))
            {
                lobby.ThuneePassedPlayers.Add(connectionId);
            }

            // Check if all players have passed Thunee
            if (lobby.ThuneePassedPlayers.Count >= lobby.Players.Count)
            {
                // All players passed, mark Thunee opportunities as complete
                lobby.ThuneeOpportunitiesComplete = true;

                // Start the first player's turn with timer
                if (!string.IsNullOrEmpty(lobby.FirstPlayerId))
                {
                    await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, lobby.FirstPlayerId);
                }
            }

            return true;
        }

        public async Task<bool> HoldGameAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // HoldGame is essentially the same as calling Thunee but without specifying the first card
            // The player will play their first card and that card's suit becomes trump

            // Mark Thunee opportunities as complete
            lobby.ThuneeOpportunitiesComplete = true;

            // Set this player as the first to play
            lobby.FirstPlayerId = connectionId;

            // Start the player's turn with timer
            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, connectionId);

            return true;
        }

        private async Task ProcessHandCompletionAsync(Lobby lobby)
        {
            if (lobby.CurrentHandCards == null || lobby.CurrentHandCards.Count < 4) return;

            // Determine hand winner using proper card game logic
            var winnerCard = _cardService.DetermineHandWinner(lobby.CurrentHandCards, lobby.TrumpSuit);
            if (winnerCard?.PlayedBy == null) return;

            var winner = lobby.Players.FirstOrDefault(p => p.Id == winnerCard.PlayedBy);
            if (winner == null) return;

            // Calculate hand points
            var handPoints = lobby.CurrentHandCards.Sum(c => _cardService.GetCardPoints(c));

            // Create hand record
            var hand = new Hand
            {
                Id = lobby.CurrentHandId,
                Cards = lobby.CurrentHandCards,
                WinnerId = winner.Id,
                WinnerName = winner.Name,
                WinnerTeam = winner.Team,
                Points = handPoints
            };

            lobby.Hands.Add(hand);

            // Update ball points
            var teamKey = $"team{winner.Team}";
            if (!lobby.BallPoints.ContainsKey(teamKey))
            {
                lobby.BallPoints[teamKey] = 0;
            }
            lobby.BallPoints[teamKey] += handPoints;

            // Trigger hand completed event
            if (HandCompleted != null)
            {
                await HandCompleted(lobby.LobbyCode, new HandCompletedResponse
                {
                    HandId = lobby.CurrentHandId,
                    WinnerId = winner.Id,
                    WinnerName = winner.Name,
                    WinnerTeam = winner.Team,
                    HandCards = lobby.CurrentHandCards,
                    Points = handPoints,
                    NextPlayerId = winner.Id // Winner leads next hand
                });
            }

            // Reset for next hand
            lobby.CurrentHandCards = new List<Card>();
            lobby.CurrentHandId++;

            // Check if ball is complete (6 hands)
            if (lobby.Hands.Count >= 6)
            {
                await ProcessBallCompletionAsync(lobby);
            }
            else
            {
                // Set winner as next player to play
                await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, winner.Id);
            }
        }

        private async Task ProcessBallCompletionAsync(Lobby lobby)
        {
            await CompleteBallAsync(lobby.LobbyCode);
        }

        private async Task SetNextPlayerTurnAsync(Lobby lobby)
        {
            // Determine next player in turn order
            // This would use game logic to determine the next player

            // For now, just cycle through players (placeholder)
            var currentPlayerIndex = lobby.Players.FindIndex(p => p.Id == lobby.TurnTimerState?.CurrentPlayerId);
            var nextPlayerIndex = (currentPlayerIndex + 1) % lobby.Players.Count;
            var nextPlayer = lobby.Players[nextPlayerIndex];

            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, nextPlayer.Id);
        }

        // Helper methods for special calls
        private bool ValidateJordhiCards(List<Card> jordhiCards, string jordhiSuit, int value, string? trumpSuit)
        {
            if (jordhiCards == null || jordhiCards.Count == 0) return false;

            // Check if all cards are of the same suit
            if (!jordhiCards.All(c => c.Suit == jordhiSuit)) return false;

            // Validate based on Jordhi value
            switch (value)
            {
                case 20:
                    // K+Q (non-trump) or K+Q (trump)
                    return jordhiCards.Count >= 2 &&
                           jordhiCards.Any(c => c.Value == "K") &&
                           jordhiCards.Any(c => c.Value == "Q");
                case 40:
                    // K+Q+J (non-trump) or K+Q (trump)
                    if (jordhiSuit == trumpSuit)
                    {
                        return jordhiCards.Count >= 2 &&
                               jordhiCards.Any(c => c.Value == "K") &&
                               jordhiCards.Any(c => c.Value == "Q");
                    }
                    else
                    {
                        return jordhiCards.Count >= 3 &&
                               jordhiCards.Any(c => c.Value == "K") &&
                               jordhiCards.Any(c => c.Value == "Q") &&
                               jordhiCards.Any(c => c.Value == "J");
                    }
                case 50:
                    // K+Q+J (trump)
                    return jordhiSuit == trumpSuit &&
                           jordhiCards.Count >= 3 &&
                           jordhiCards.Any(c => c.Value == "K") &&
                           jordhiCards.Any(c => c.Value == "Q") &&
                           jordhiCards.Any(c => c.Value == "J");
                default:
                    return false;
            }
        }

        private int CountHandsWonByTeam(Lobby lobby, int team)
        {
            return lobby.Hands.Count(h => h.WinnerTeam == team);
        }

        private void AdjustTargetScoresForJordhi(Lobby lobby, int team, int jordhiValue)
        {
            if (lobby.TargetScores == null)
            {
                lobby.TargetScores = new Dictionary<string, int> { { "team1", 105 }, { "team2", 105 } };
            }

            // Reduce the target score for the team that called Jordhi
            var teamKey = $"team{team}";
            if (lobby.TargetScores.ContainsKey(teamKey))
            {
                lobby.TargetScores[teamKey] -= jordhiValue;
            }
        }

        // Helper methods for 4-ball validation
        private bool ValidateNeverFollowSuit(Lobby lobby, string accusedPlayerId, int handNumber)
        {
            // Find the specific hand
            var hand = lobby.Hands.FirstOrDefault(h => h.Id == handNumber);
            if (hand == null || hand.Cards.Count == 0) return false;

            // Find the accused player's card in this hand
            var accusedCard = hand.Cards.FirstOrDefault(c => c.PlayedBy == accusedPlayerId);
            if (accusedCard == null) return false;

            // Get the lead suit (first card played in the hand)
            var leadCard = hand.Cards.FirstOrDefault();
            if (leadCard == null) return false;

            var leadSuit = leadCard.Suit;

            // If the accused player played the lead suit, they followed suit correctly
            if (accusedCard.Suit == leadSuit) return false;

            // Check if the accused player had cards of the lead suit at the time of play
            // This would require tracking the player's hand state at each point in time
            // For now, we'll use a simplified validation
            if (lobby.PlayerCards.ContainsKey(accusedPlayerId))
            {
                var playerCards = lobby.PlayerCards[accusedPlayerId];
                var hasLeadSuit = playerCards.Any(c => c.Suit == leadSuit);

                // If they have cards of the lead suit now and didn't play it, it's a violation
                // Note: This is simplified - in a real game, we'd need to track historical hand states
                return hasLeadSuit;
            }

            return false;
        }

        private bool ValidateUnderChopped(Lobby lobby, string accusedPlayerId, int handNumber)
        {
            // Find the specific hand
            var hand = lobby.Hands.FirstOrDefault(h => h.Id == handNumber);
            if (hand == null || hand.Cards.Count == 0) return false;

            // Find the accused player's card in this hand
            var accusedCard = hand.Cards.FirstOrDefault(c => c.PlayedBy == accusedPlayerId);
            if (accusedCard == null) return false;

            // Get the lead suit (first card played in the hand)
            var leadCard = hand.Cards.FirstOrDefault();
            if (leadCard == null) return false;

            var leadSuit = leadCard.Suit;
            var trumpSuit = lobby.TrumpSuit;

            // Under chopping only applies when:
            // 1. Trump was not the lead suit
            // 2. Player didn't play highest trump
            // 3. Player did play a trump
            // 4. Player had non-trump cards available

            if (leadSuit == trumpSuit) return false; // Trump was lead suit
            if (accusedCard.Suit != trumpSuit) return false; // Player didn't play trump

            // Check if player had higher trump cards
            if (lobby.PlayerCards.ContainsKey(accusedPlayerId))
            {
                var playerCards = lobby.PlayerCards[accusedPlayerId];
                var trumpCards = playerCards.Where(c => c.Suit == trumpSuit).ToList();

                // Check if player had higher trump cards using custom trump ranking
                var hasHigherTrump = trumpCards.Any(c => IsHigherTrumpCard(c, accusedCard));

                // Check if player had non-trump cards
                var hasNonTrumpCards = playerCards.Any(c => c.Suit != trumpSuit);

                // Violation if they had higher trump and non-trump cards available
                return hasHigherTrump && hasNonTrumpCards;
            }

            return false;
        }

        private bool IsHigherTrumpCard(Card card1, Card card2)
        {
            // Custom trump ranking: Queen > King > 10 > Ace > 9 > Jack
            var trumpRanking = new Dictionary<string, int>
            {
                { "Q", 6 },
                { "K", 5 },
                { "10", 4 },
                { "A", 3 },
                { "9", 2 },
                { "J", 1 }
            };

            var rank1 = trumpRanking.GetValueOrDefault(card1.Value, 0);
            var rank2 = trumpRanking.GetValueOrDefault(card2.Value, 0);

            return rank1 > rank2;
        }

        /// <summary>
        /// Assign fixed positions to players based on the dealer
        ///
        /// According to the specific rule set:
        /// - Position 3: Dealer (always bottom left)
        /// - Position 1: Dealer's partner (always top left)
        /// - Positions 2 and 4: Opposing team players (top right and bottom right)
        ///
        /// Team 1 consists of positions 1 and 3
        /// Team 2 consists of positions 2 and 4
        /// </summary>
        /// <param name="players">List of player objects</param>
        /// <param name="dealerId">ID of the dealer</param>
        /// <returns>List of players with positions assigned</returns>
        private List<Player> AssignPositionsBasedOnDealer(List<Player> players, string dealerId)
        {
            if (string.IsNullOrEmpty(dealerId) || players.Count != 4)
            {
                Console.WriteLine("Cannot assign positions: Invalid dealer ID or player count");
                return players;
            }

            // Find the dealer
            var dealer = players.FirstOrDefault(p => p.Id == dealerId);
            if (dealer == null)
            {
                Console.WriteLine($"Dealer with ID {dealerId} not found in players list");
                return players;
            }

            // Get the dealer's team
            var dealerTeam = dealer.Team;
            if (dealerTeam == 0)
            {
                Console.WriteLine("Dealer has no team assigned");
                return players;
            }

            // Create a copy of the players list
            var positionedPlayers = players.Select(p => new Player
            {
                Id = p.Id,
                Name = p.Name,
                Avatar = p.Avatar,
                IsHost = p.IsHost,
                Team = p.Team,
                IsReady = p.IsReady,
                IsDealer = p.IsDealer,
                IsTrumpSelector = p.IsTrumpSelector,
                Position = p.Position
            }).ToList();

            // Find the dealer's partner (same team, but not the dealer)
            var dealerPartner = positionedPlayers.FirstOrDefault(p => p.Team == dealerTeam && p.Id != dealerId);
            if (dealerPartner == null)
            {
                Console.WriteLine("Dealer partner not found");
                return players;
            }

            // Find the opposing team players
            var oppositeTeam = dealerTeam == 1 ? 2 : 1;
            var opposingTeamPlayers = positionedPlayers.Where(p => p.Team == oppositeTeam).ToList();
            if (opposingTeamPlayers.Count != 2)
            {
                Console.WriteLine($"Expected 2 opposing team players, found {opposingTeamPlayers.Count}");
                return players;
            }

            // Assign positions according to the specific rule set
            // Position 3: Dealer (bottom left)
            dealer.Position = 3;

            // Position 1: Dealer's partner (top left)
            dealerPartner.Position = 1;

            // Position 2: First opposing team player (top right)
            opposingTeamPlayers[0].Position = 2;

            // Position 4: Second opposing team player (bottom right)
            opposingTeamPlayers[1].Position = 4;

            Console.WriteLine("Assigned positions to players: " +
                string.Join(", ", positionedPlayers.Select(p => $"{p.Name} ({p.Id}) - Team {p.Team} - Position {p.Position}")));

            return positionedPlayers;
        }

        /// <summary>
        /// Get the position to the left of the given position (clockwise)
        ///
        /// According to the specific rule set:
        /// Position 1: Player 1 (Team A)
        /// Position 2: Player 3 (Team B)
        /// Position 3: Player 2 (Team A)
        /// Position 4: Player 4 (Team B)
        ///
        /// Reverse play order:
        /// - Position 1 (Player 1) -> Position 4 (Player 4) is previous
        /// - Position 2 (Player 3) -> Position 1 (Player 1) is previous
        /// - Position 3 (Player 2) -> Position 2 (Player 3) is previous
        /// - Position 4 (Player 4) -> Position 3 (Player 2) is previous
        /// </summary>
        /// <param name="position">Current position (1-4)</param>
        /// <returns>Position to the left (clockwise)</returns>
        private int GetPositionToLeft(int position)
        {
            // Ensure position is a number between 1 and 4
            if (position < 1 || position > 4)
            {
                Console.WriteLine($"Invalid position: {position}");
                return 1; // Default to position 1 if invalid
            }

            // Map of positions to the previous position in clockwise order
            var leftPositions = new Dictionary<int, int>
            {
                { 1, 4 }, // From Player 1 (position 1) to Player 4 (position 4)
                { 2, 1 }, // From Player 3 (position 2) to Player 1 (position 1)
                { 3, 2 }, // From Player 2 (position 3) to Player 3 (position 2)
                { 4, 3 }  // From Player 4 (position 4) to Player 2 (position 3)
            };

            Console.WriteLine($"Getting position to the left of {position}: {leftPositions[position]}");
            return leftPositions[position];
        }

        /// <summary>
        /// Get the position to the right of the given position (counter-clockwise)
        /// </summary>
        /// <param name="position">Current position (1-4)</param>
        /// <returns>Position to the right (counter-clockwise)</returns>
        private int GetPositionToRight(int position)
        {
            // Ensure position is a number between 1 and 4
            if (position < 1 || position > 4)
            {
                Console.WriteLine($"Invalid position: {position}");
                return 1; // Default to position 1 if invalid
            }

            // Map of positions to the next position in counter-clockwise order
            var rightPositions = new Dictionary<int, int>
            {
                { 1, 2 }, // From Player 1 (position 1) to Player 3 (position 2)
                { 2, 3 }, // From Player 3 (position 2) to Player 2 (position 3)
                { 3, 4 }, // From Player 2 (position 3) to Player 4 (position 4)
                { 4, 1 }  // From Player 4 (position 4) to Player 1 (position 1)
            };

            Console.WriteLine($"Getting position to the right of {position}: {rightPositions[position]}");
            return rightPositions[position];
        }

        /// <summary>
        /// Get the team number (1 or 2) for a given position
        ///
        /// According to the specific rule set:
        /// - Team A (1): positions 1 and 3 (Player 1 and Player 2)
        /// - Team B (2): positions 2 and 4 (Player 3 and Player 4)
        /// </summary>
        /// <param name="position">Position (1-4)</param>
        /// <returns>Team number (1 or 2)</returns>
        private int GetTeamForPosition(int position)
        {
            // Team A (1): positions 1 and 3 (Player 1 and Player 2)
            // Team B (2): positions 2 and 4 (Player 3 and Player 4)
            var teamMap = new Dictionary<int, int>
            {
                { 1, 1 }, // Player 1 - Team A
                { 2, 2 }, // Player 3 - Team B
                { 3, 1 }, // Player 2 - Team A
                { 4, 2 }  // Player 4 - Team B
            };

            return teamMap.GetValueOrDefault(position, 1); // Default to team 1 if invalid
        }

        /// <summary>
        /// Get the initial trumper position (always position 2, to the right of dealer at position 3)
        ///
        /// In Thunee, the player to the right of the dealer selects trump.
        /// Since the dealer is always at position 3 (bottom left), the trumper
        /// is always at position 2 (top right).
        /// </summary>
        /// <returns>The position of the initial trumper (always 2)</returns>
        private int GetInitialTrumperPosition()
        {
            return 2;
        }

        // Ball completion logic
        public async Task<bool> CompleteBallAsync(string lobbyCode)
        {
            var lobby = _lobbyService.GetLobby(lobbyCode);
            if (lobby == null) return false;

            // Calculate ball scores
            var ballResult = CalculateBallResult(lobby);

            // Update ball scores
            var teamKey = $"team{ballResult.WinningTeam}";
            if (!lobby.BallScores.ContainsKey(teamKey))
            {
                lobby.BallScores[teamKey] = 0;
            }
            lobby.BallScores[teamKey] += ballResult.BallsAwarded;

            // Determine next dealer
            var nextDealer = DetermineNextDealer(lobby, ballResult.WinningTeam);

            // Create ball history record
            var ballHistory = new BallHistory
            {
                BallId = lobby.CurrentBallId,
                Winner = ballResult.WinningTeam,
                Points = ballResult.Points,
                NextDealer = nextDealer,
                BallScores = new Dictionary<string, int>(lobby.BallScores),
                FourBallAwarded = ballResult.FourBallAwarded,
                FourBallOption = ballResult.FourBallOption,
                FourBallWinningTeam = ballResult.FourBallWinningTeam,
                BallsAwarded = ballResult.BallsAwarded,
                Timestamp = DateTime.UtcNow
            };

            lobby.GameHistory.Add(ballHistory);

            // Trigger ball completed event
            if (BallCompleted != null)
            {
                await BallCompleted(lobbyCode, new BallCompletedResponse
                {
                    BallId = lobby.CurrentBallId,
                    Winner = ballResult.WinningTeam,
                    Points = ballResult.Points,
                    NextDealer = nextDealer,
                    BallScores = lobby.BallScores,
                    DoubleProcessed = ballResult.DoubleProcessed,
                    ThuneeSuccess = ballResult.ThuneeSuccess,
                    ThuneeFailure = ballResult.ThuneeFailure,
                    BallsAwarded = ballResult.BallsAwarded
                });
            }

            // Check if game is complete
            var gameWinner = CheckGameCompletion(lobby);
            if (gameWinner.HasValue)
            {
                await CompleteGameAsync(lobby, gameWinner.Value);
                return true;
            }

            // Reset for next ball
            ResetForNextBall(lobby, nextDealer);

            return true;
        }

        private BallResult CalculateBallResult(Lobby lobby)
        {
            var result = new BallResult();

            // Check for 4-ball penalties first (highest priority)
            if (lobby.FourBallCalls.Any(f => f.IsValid))
            {
                var validFourBall = lobby.FourBallCalls.First(f => f.IsValid);
                result.WinningTeam = validFourBall.PlayerTeam;
                result.BallsAwarded = 4;
                result.FourBallAwarded = true;
                result.FourBallOption = validFourBall.Option;
                result.FourBallWinningTeam = validFourBall.PlayerTeam;
                result.Points = new Dictionary<string, int> { { "team1", 0 }, { "team2", 0 } };
                return result;
            }

            // Check for Thunee calls
            if (lobby.ThuneeCalls.Any())
            {
                var thuneeCall = lobby.ThuneeCalls.First();
                var thuneeTeam = thuneeCall.PlayerTeam;
                var oppositeTeam = thuneeTeam == 1 ? 2 : 1;

                // Check if Thunee was successful (Thunee team won all hands)
                var thuneeTeamHands = CountHandsWonByTeam(lobby, thuneeTeam);
                var oppositeTeamHands = CountHandsWonByTeam(lobby, oppositeTeam);

                if (oppositeTeamHands == 0)
                {
                    // Thunee success - Thunee team gets 4 balls
                    result.WinningTeam = thuneeTeam;
                    result.BallsAwarded = 4;
                    result.ThuneeSuccess = true;
                }
                else
                {
                    // Thunee failure - Opposite team gets 4 balls
                    result.WinningTeam = oppositeTeam;
                    result.BallsAwarded = 4;
                    result.ThuneeFailure = true;
                }

                result.Points = new Dictionary<string, int> { { "team1", 0 }, { "team2", 0 } };
                return result;
            }

            // Normal ball scoring
            var team1Points = CalculateTeamPoints(lobby, 1);
            var team2Points = CalculateTeamPoints(lobby, 2);

            result.Points = new Dictionary<string, int>
            {
                { "team1", team1Points },
                { "team2", team2Points }
            };

            // Determine winner based on target scores
            var team1Target = lobby.TargetScores?.GetValueOrDefault("team1", 105) ?? 105;
            var team2Target = lobby.TargetScores?.GetValueOrDefault("team2", 105) ?? 105;

            var team1Reached = team1Points >= team1Target;
            var team2Reached = team2Points >= team2Target;

            if (team1Reached && team2Reached)
            {
                // Both teams reached target - higher score wins
                result.WinningTeam = team1Points > team2Points ? 1 : 2;
            }
            else if (team1Reached)
            {
                result.WinningTeam = 1;
            }
            else if (team2Reached)
            {
                result.WinningTeam = 2;
            }
            else
            {
                // Neither team reached target - higher score wins
                result.WinningTeam = team1Points > team2Points ? 1 : 2;
            }

            // Check for Double call
            if (lobby.DoubleCalls.Any(d => d.IsValid))
            {
                var doubleCall = lobby.DoubleCalls.First(d => d.IsValid);
                var doubleTeam = doubleCall.PlayerTeam;

                if (result.WinningTeam == doubleTeam)
                {
                    result.BallsAwarded = 2; // Double successful
                    result.DoubleProcessed = true;
                }
                else
                {
                    result.BallsAwarded = 1; // Normal ball
                }
            }
            else
            {
                result.BallsAwarded = 1; // Normal ball
            }

            return result;
        }

        private int CalculateTeamPoints(Lobby lobby, int team)
        {
            var teamHands = lobby.Hands.Where(h => h.WinnerTeam == team);
            var totalPoints = teamHands.Sum(h => h.Points);

            // Add Jordhi points
            var jordhiPoints = lobby.JordhiCalls
                .Where(j => j.PlayerTeam == team && j.IsFullyValid)
                .Sum(j => j.Value);

            return totalPoints + jordhiPoints;
        }

        private string DetermineNextDealer(Lobby lobby, int ballWinningTeam)
        {
            // Thunee dealer rotation rules:
            // - If the team with the current dealer is ahead or tied, deal passes to the right
            // - If the team with the current dealer is behind, same dealer deals again

            var currentDealer = lobby.Players.FirstOrDefault(p => p.IsDealer);
            if (currentDealer == null) return lobby.Players.First().Id;

            var currentDealerTeam = currentDealer.Team;
            var currentDealerTeamScore = lobby.BallScores.GetValueOrDefault($"team{currentDealerTeam}", 0);
            var otherTeam = currentDealerTeam == 1 ? 2 : 1;
            var otherTeamScore = lobby.BallScores.GetValueOrDefault($"team{otherTeam}", 0);

            if (currentDealerTeamScore >= otherTeamScore)
            {
                // Deal passes to the right (next player in rotation)
                var currentIndex = lobby.Players.FindIndex(p => p.Id == currentDealer.Id);
                var nextIndex = (currentIndex + 1) % lobby.Players.Count;
                return lobby.Players[nextIndex].Id;
            }
            else
            {
                // Same dealer deals again
                return currentDealer.Id;
            }
        }

        private int? CheckGameCompletion(Lobby lobby)
        {
            // Game ends when a team reaches 12 balls (or 13 with winning Khanak)
            var team1Balls = lobby.BallScores.GetValueOrDefault("team1", 0);
            var team2Balls = lobby.BallScores.GetValueOrDefault("team2", 0);

            // Check for Khanak win (13 balls)
            if (lobby.KhanakCalls.Any(k => k.IsValid))
            {
                if (team1Balls >= 13) return 1;
                if (team2Balls >= 13) return 2;
            }

            // Normal win (12 balls)
            if (team1Balls >= 12) return 1;
            if (team2Balls >= 12) return 2;

            return null;
        }

        private async Task CompleteGameAsync(Lobby lobby, int winningTeam)
        {
            // Trigger game ended event
            if (GameEnded != null)
            {
                await GameEnded(lobby.LobbyCode, new GameEndedResponse
                {
                    WinningTeam = winningTeam,
                    FinalBallScores = lobby.BallScores,
                    GameHistory = lobby.GameHistory,
                    Reason = "Game completed",
                    Winner = winningTeam,
                    FinalScores = lobby.BallScores,
                    BallLimit = lobby.KhanakCalls.Any(k => k.IsValid) ? 13 : 12,
                    HasWinningKhanak = lobby.KhanakCalls.Any(k => k.IsValid && k.PlayerTeam == winningTeam)
                });
            }
        }

        private async void ResetForNextBall(Lobby lobby, string nextDealerId)
        {
            // Reset ball-specific state
            lobby.CurrentBallId++;
            lobby.CurrentHandId = 1;
            lobby.Hands.Clear();
            lobby.PlayerCards.Clear();
            lobby.JordhiCalls.Clear();
            lobby.DoubleCalls.Clear();
            lobby.KhanakCalls.Clear();
            lobby.ThuneeCalls.Clear();
            lobby.FourBallCalls.Clear();
            lobby.ThuneePassedPlayers.Clear();
            lobby.ThuneeOpportunitiesComplete = false;
            lobby.TrumpSuit = null;
            lobby.FirstPlayerId = null;
            lobby.DoubleCallerId = null;
            lobby.KhanakCallerId = null;

            // Update dealer
            foreach (var player in lobby.Players)
            {
                player.IsDealer = player.Id == nextDealerId;
                player.IsTrumpSelector = false;
            }

            // Reset target scores
            lobby.TargetScores = new Dictionary<string, int> { { "team1", 105 }, { "team2", 105 } };

            // After a delay, transition to shuffle phase for next ball
            _ = Task.Delay(15000).ContinueWith(async _ =>
            {
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "shuffle",
                        Players = lobby.Players
                    });
                }
            });
        }

        private object CreateGameHistoryResponse(Lobby lobby)
        {
            return new
            {
                balls = lobby.GameHistory.Select(b => new
                {
                    ballNumber = b.BallId,
                    winner = b.Winner,
                    ballsAwarded = b.BallsAwarded,
                    points = b.Points,
                    ballScores = b.BallScores,
                    timestamp = b.Timestamp.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    specialCall = b.FourBallAwarded ? "4-Ball" : null,
                    details = new { }
                }).ToArray(),
                startTime = lobby.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                endTime = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                totalBalls = lobby.GameHistory.Count,
                duration = (int)(DateTime.UtcNow - lobby.CreatedAt).TotalMinutes
            };
        }

        // Helper class for ball result calculation
        private class BallResult
        {
            public int WinningTeam { get; set; }
            public int BallsAwarded { get; set; } = 1;
            public Dictionary<string, int> Points { get; set; } = new();
            public bool FourBallAwarded { get; set; }
            public string? FourBallOption { get; set; }
            public int? FourBallWinningTeam { get; set; }
            public bool DoubleProcessed { get; set; }
            public bool ThuneeSuccess { get; set; }
            public bool ThuneeFailure { get; set; }
        }
    }
}
