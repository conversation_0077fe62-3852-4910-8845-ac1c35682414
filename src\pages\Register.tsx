"use client";
import { useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { ArrowR<PERSON>, AlertCircle, User, Mail, Lock, Send } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import BurgerMenu from "@/components/BurgerMenu";
import { useAuthStore } from "@/store/authStore";
// Use public path for GRCard image
const GRCard = "/assets/GRCard.png";

export default function Register() {
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [otp, setOtp] = useState("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { register, verifyOtp, resendOtp, isLoading, tempRegistrationData } = useAuthStore();
  const navigate = useNavigate();

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username) {
      setErrorMessage("Please enter a username");
      return;
    }

    if (!email) {
      setErrorMessage("Please enter your email");
      return;
    }

    if (!password) {
      setErrorMessage("Please enter a password");
      return;
    }

    if (password !== confirmPassword) {
      setErrorMessage("Passwords do not match");
      return;
    }

    setErrorMessage(null);

    try {
      await register(username, email, password);
      // After successful registration, the user will be prompted for OTP
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to register');
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp) {
      setErrorMessage("Please enter the OTP");
      return;
    }

    setErrorMessage(null);

    try {
      await verifyOtp(otp);
      navigate("/"); // Navigate to the lobby page after successful verification
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to verify OTP');
    }
  };

  const handleResendOtp = async () => {
    try {
      await resendOtp();
      setErrorMessage("OTP has been resent to your email");
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to resend OTP');
    }
  };

  // Determine which form to show based on registration progress
  const showOtpForm = tempRegistrationData?.otpSent;

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <div className="relative w-full p-2 sm:p-4 flex justify-center items-center">
        <h1 className="text-2xl sm:text-3xl font-bold text-[#E1C760]">Thunee</h1>
        <BurgerMenu />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-3 sm:p-4">
        {/* Responsive container - adjusts width based on orientation */}
        <div className="w-full max-w-[90%] sm:max-w-md md:max-w-lg lg:max-w-xl
                        landscape:max-w-[80%] landscape:md:max-w-2xl">

          {/* Card and Title - flex row in landscape, column in portrait */}
          <div className="flex flex-col landscape:flex-row landscape:items-center landscape:justify-between mb-4 sm:mb-6">
            {/* Card Image */}
            <div className="flex justify-center landscape:justify-start mb-4 landscape:mb-0">
              <img
                src={GRCard}
                alt="Thunee Card"
                className="w-24 sm:w-32 h-auto landscape:w-20 landscape:md:w-28"
              />
            </div>

            <h2 className="text-xl sm:text-2xl font-bold text-center landscape:text-right text-[#E1C760] landscape:ml-4">
              {showOtpForm ? "Verify Your Account" : "Create Account"}
            </h2>
          </div>

          {errorMessage && (
            <Alert variant="destructive" className="mb-4 bg-red-900/50 border border-red-500">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">{errorMessage}</AlertDescription>
            </Alert>
          )}

          {!showOtpForm ? (
            // Registration Form
            <form onSubmit={handleRegister} className="space-y-3 sm:space-y-4">
              {/* Registration form fields - responsive for both orientations */}
              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="username" className="text-[#E1C760] text-sm sm:text-base">Username</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 sm:h-5 w-4 sm:w-5" />
                  <Input
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Choose a username"
                    className="pl-10 h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-sm sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* In landscape mode, display email and password fields side by side */}
              <div className="landscape:flex landscape:gap-4 landscape:space-y-0">
                <div className="space-y-1 sm:space-y-2 landscape:flex-1">
                  <Label htmlFor="email" className="text-[#E1C760] text-sm sm:text-base">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 sm:h-5 w-4 sm:w-5" />
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      className="pl-10 h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-sm sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="space-y-1 sm:space-y-2 landscape:flex-1 mt-3 landscape:mt-0">
                  <Label htmlFor="password" className="text-[#E1C760] text-sm sm:text-base">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 sm:h-5 w-4 sm:w-5" />
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Create a password"
                      className="pl-10 h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-sm sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                      disabled={isLoading}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="confirmPassword" className="text-[#E1C760] text-sm sm:text-base">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 sm:h-5 w-4 sm:w-5" />
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your password"
                    className="pl-10 h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-sm sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-10 sm:h-12 bg-[#E1C760] text-black hover:bg-[#E1C760]/90 flex items-center justify-center gap-2 text-sm sm:text-base mt-2"
              >
                {isLoading ? "Registering..." : "Register"}
                {!isLoading && <ArrowRight className="h-4 sm:h-5 w-4 sm:w-5" />}
              </Button>

              <div className="text-center mt-3 sm:mt-4">
                <p className="text-gray-400 text-xs sm:text-sm">
                  Already have an account?{" "}
                  <Link to="/login" className="text-[#E1C760] hover:underline">
                    Sign In
                  </Link>
                </p>
              </div>
            </form>
          ) : (
            // OTP Verification Form - responsive for both orientations
            <form onSubmit={handleVerifyOtp} className="space-y-3 sm:space-y-4">
              <p className="text-gray-400 text-center mb-3 sm:mb-4 text-xs sm:text-sm landscape:text-sm">
                We've sent a verification code to your email. Please enter it below to verify your account.
              </p>

              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="otp" className="text-[#E1C760] text-sm sm:text-base">Verification Code</Label>
                <Input
                  id="otp"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  placeholder="Enter verification code"
                  className="h-10 sm:h-12 bg-transparent border border-[#E1C760] rounded-lg text-center text-[#E1C760] text-lg sm:text-xl tracking-widest focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                  disabled={isLoading}
                  maxLength={6}
                />
              </div>

              {/* Responsive layout for landscape mode */}
              <div className="landscape:flex landscape:gap-4 landscape:items-center">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-10 sm:h-12 bg-[#E1C760] text-black hover:bg-[#E1C760]/90 flex items-center justify-center gap-2 text-sm sm:text-base mt-2 landscape:flex-1"
                >
                  {isLoading ? "Verifying..." : "Verify"}
                  {!isLoading && <ArrowRight className="h-4 sm:h-5 w-4 sm:w-5" />}
                </Button>

                <div className="text-center mt-3 sm:mt-4 landscape:mt-2 landscape:flex-1">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleResendOtp}
                    disabled={isLoading}
                    className="text-[#E1C760] hover:text-[#E1C760]/80 flex items-center gap-1 text-xs sm:text-sm mx-auto"
                  >
                    <Send className="h-3 sm:h-4 w-3 sm:w-4" />
                    Resend Code
                  </Button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
