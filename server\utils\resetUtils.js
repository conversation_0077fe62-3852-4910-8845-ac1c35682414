/**
 * Utility functions for resetting game state
 */

/**
 * Reset the game state for a new game or ball
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object (if any)
 */
function resetGameState(lobby, matchedLobby = null) {
  console.log(`Resetting game state for lobby ${lobby.lobbyCode}`);

  // Reset the deck
  lobby.gameDeck = null;
  lobby.dealerDeck = null;
  lobby.remainingDeck = null;

  // Reset player cards - completely clear them
  lobby.playerCards = {};

  // Reset all card tracking
  lobby.allDealtCards = new Set();

  // Reset cards dealt counter
  lobby.cardsDealtPerPlayer = {};

  // Initialize cards dealt counter for each player
  if (lobby.players) {
    lobby.players.forEach(player => {
      lobby.cardsDealtPerPlayer[player.id] = 0;
    });
  }

  // Reset dealer determination state
  lobby.dealerFound = false;
  lobby.dealerId = null;

  // Reset any other game state
  lobby.trumpSuit = null;
  lobby.thuneePlayerId = null;
  lobby.currentBall = 1;
  lobby.currentHand = 1;
  lobby.currentHandCards = [];
  lobby.playedCards = [];

  // If we have a matched lobby, reset it too
  if (matchedLobby && matchedLobby !== lobby) {
    matchedLobby.gameDeck = null;
    matchedLobby.dealerDeck = null;
    matchedLobby.remainingDeck = null;
    matchedLobby.playerCards = {};
    matchedLobby.allDealtCards = new Set();
    matchedLobby.cardsDealtPerPlayer = {};

    // Initialize cards dealt counter for each player
    if (matchedLobby.players) {
      matchedLobby.players.forEach(player => {
        matchedLobby.cardsDealtPerPlayer[player.id] = 0;
      });
    }

    matchedLobby.dealerFound = false;
    matchedLobby.dealerId = null;
    matchedLobby.trumpSuit = null;
    matchedLobby.thuneePlayerId = null;
    matchedLobby.currentBall = 1;
    matchedLobby.currentHand = 1;
    matchedLobby.currentHandCards = [];
    matchedLobby.playedCards = [];
  }

  console.log('Game state reset complete - all card tracking has been reset');
}

/**
 * Reset the ball state for a new ball
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object (if any)
 */
function resetBallState(lobby, matchedLobby = null) {
  console.log(`Resetting ball state for lobby ${lobby.lobbyCode}`);

  // Stop any active turn timers first
  const turnUtils = require('./turnUtils');
  turnUtils.stopTurnTimer(lobby);
  if (matchedLobby && matchedLobby !== lobby) {
    turnUtils.stopTurnTimer(matchedLobby);
  }

  // Reset the deck
  lobby.gameDeck = null;
  lobby.remainingDeck = null;
  lobby.dealerDeck = null;

  // Reset player cards - completely clear them
  lobby.playerCards = {};

  // Reset all card tracking
  lobby.allDealtCards = new Set();

  // Reset cards dealt counter
  lobby.cardsDealtPerPlayer = {};

  // Initialize cards dealt counter for each player
  if (lobby.players) {
    lobby.players.forEach(player => {
      lobby.cardsDealtPerPlayer[player.id] = 0;
    });
  }

  // Reset any other ball-specific state
  lobby.trumpSuit = null;
  lobby.thuneePlayerId = null;
  lobby.currentHand = 1;

  // Clear all played cards
  lobby.currentHandCards = [];
  lobby.playedCards = [];
  lobby.lastPlayedCards = [];

  // Clear any other card-related state
  lobby.currentHandId = 0;
  lobby.currentHandWinner = null;

  // Clear timer state for main lobby
  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }
  if (lobby.updateInterval) {
    clearInterval(lobby.updateInterval);
    lobby.updateInterval = null;
  }
  if (lobby.turnTimerState) {
    lobby.turnTimerState.timerActive = false;
  }

  // Clear ball completion flags
  lobby.ballCompleted = false;
  lobby.fourBallInProgress = false;

  // Reset Jordhi-related state
  lobby.jordhiCalls = [];
  lobby.playerJordhiCalls = {};

  // Reset target scores to default
  lobby.targetScores = {
    team1: 105,
    team2: 105
  };

  // Initialize playerJordhiCalls for each player
  if (lobby.players) {
    lobby.players.forEach(player => {
      if (!lobby.playerJordhiCalls) {
        lobby.playerJordhiCalls = {};
      }
      lobby.playerJordhiCalls[player.id] = [];
    });
  }

  // If we have a matched lobby, reset it too
  if (matchedLobby && matchedLobby !== lobby) {
    matchedLobby.gameDeck = null;
    matchedLobby.remainingDeck = null;
    matchedLobby.dealerDeck = null;
    matchedLobby.playerCards = {};
    matchedLobby.allDealtCards = new Set();
    matchedLobby.cardsDealtPerPlayer = {};

    // Initialize cards dealt counter for each player
    if (matchedLobby.players) {
      matchedLobby.players.forEach(player => {
        matchedLobby.cardsDealtPerPlayer[player.id] = 0;
      });
    }

    // Clear timer state for matched lobby
    if (matchedLobby.turnTimer) {
      clearTimeout(matchedLobby.turnTimer);
      matchedLobby.turnTimer = null;
    }
    if (matchedLobby.updateInterval) {
      clearInterval(matchedLobby.updateInterval);
      matchedLobby.updateInterval = null;
    }
    if (matchedLobby.turnTimerState) {
      matchedLobby.turnTimerState.timerActive = false;
    }

    // Clear ball completion flags for matched lobby
    matchedLobby.ballCompleted = false;
    matchedLobby.fourBallInProgress = false;

    matchedLobby.trumpSuit = null;
    matchedLobby.thuneePlayerId = null;
    matchedLobby.currentHand = 1;

    // Clear all played cards
    matchedLobby.currentHandCards = [];
    matchedLobby.playedCards = [];
    matchedLobby.lastPlayedCards = [];

    // Clear any other card-related state
    matchedLobby.currentHandId = 0;
    matchedLobby.currentHandWinner = null;

    // Reset Jordhi-related state
    matchedLobby.jordhiCalls = [];
    matchedLobby.playerJordhiCalls = {};

    // Reset target scores to default
    matchedLobby.targetScores = {
      team1: 105,
      team2: 105
    };

    // Initialize playerJordhiCalls for each player
    if (matchedLobby.players) {
      matchedLobby.players.forEach(player => {
        if (!matchedLobby.playerJordhiCalls) {
          matchedLobby.playerJordhiCalls = {};
        }
        matchedLobby.playerJordhiCalls[player.id] = [];
      });
    }
  }

  console.log('Ball state reset complete - all card tracking has been reset');
}

module.exports = {
  resetGameState,
  resetBallState
};
