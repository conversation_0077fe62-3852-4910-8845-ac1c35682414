using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Models;
using ThuneeAPI.Services;

namespace ThuneeAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CompetitionController : ControllerBase
    {
        private readonly ICompetitionService _competitionService;
        private readonly ILogger<CompetitionController> _logger;

        public CompetitionController(ICompetitionService competitionService, ILogger<CompetitionController> logger)
        {
            _competitionService = competitionService;
            _logger = logger;
        }

        /// <summary>
        /// Get all competitions
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<CompetitionDto>>> GetCompetitions()
        {
            try
            {
                var competitions = await _competitionService.GetCompetitionsAsync();
                return Ok(competitions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving competitions");
                return StatusCode(500, "An error occurred while retrieving competitions");
            }
        }

        /// <summary>
        /// Get a specific competition by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<CompetitionDto>> GetCompetition(int id)
        {
            try
            {
                var competition = await _competitionService.GetCompetitionAsync(id);
                if (competition == null)
                {
                    return NotFound($"Competition with ID {id} not found");
                }
                return Ok(competition);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving competition {CompetitionId}", id);
                return StatusCode(500, "An error occurred while retrieving the competition");
            }
        }

        /// <summary>
        /// Get leaderboard for a specific competition
        /// </summary>
        [HttpGet("{id}/leaderboard")]
        public async Task<ActionResult<LeaderboardDto>> GetCompetitionLeaderboard(
            int id,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? sortBy = "score",
            [FromQuery] string? sortDirection = "desc",
            [FromQuery] int? minGames = null)
        {
            try
            {
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 10;

                var leaderboard = await _competitionService.GetCompetitionLeaderboardAsync(
                    id, page, pageSize, sortBy, sortDirection, minGames);
                
                return Ok(leaderboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving leaderboard for competition {CompetitionId}", id);
                return StatusCode(500, "An error occurred while retrieving the leaderboard");
            }
        }

        /// <summary>
        /// Save game results (called at the end of each game)
        /// </summary>
        [HttpPost("save-game-result")]
        public async Task<ActionResult> SaveGameResult([FromBody] GameResultRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest("Invalid game result data");
                }

                var success = await _competitionService.SaveGameResultAsync(request);
                
                if (success)
                {
                    return Ok(new { message = "Game result saved successfully" });
                }
                else
                {
                    return StatusCode(500, "Failed to save game result");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving game result for lobby {LobbyCode}", request.LobbyCode);
                return StatusCode(500, "An error occurred while saving the game result");
            }
        }
    }
}
