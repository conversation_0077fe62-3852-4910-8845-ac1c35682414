// components/BetslipSheet.tsx
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useState } from "react";

interface Bet {
  id: number;
  title: string;
  type: string;
  date: string;
  time: string;
  stake: number;
  payout: number;
  odds: string;
}

interface BetslipSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

type TabType = "betslip" | "history";

export default function BetslipSheet({
  open,
  onOpenChange,
  children,
}: BetslipSheetProps) {
  const [activeTab, setActiveTab] = useState<TabType>("betslip");
  const [selectedBetId, setSelectedBetId] = useState<number | null>(null);

  const bets: Bet[] = [
    {
      id: 1,
      title: "Winner of game",
      type: "Outrights",
      date: "16 June 2024",
      time: "4:59:00",
      stake: 100.0,
      payout: 90.0,
      odds: "9/10",
    },
    {
      id: 2,
      title: "Team to win most Thunees",
      type: "Outrights",
      date: "16 June 2024",
      time: "4:59:00",
      stake: 100.0,
      payout: 90.0,
      odds: "9/10",
    },
    {
      id: 3,
      title: "Which team will win first",
      type: "Outrights",
      date: "16 June 2024",
      time: "4:59:00",
      stake: 100.0,
      payout: 90.0,
      odds: "9/10",
    },
  ];

  const historyBets: Bet[] = [
    {
      id: 4,
      title: "Previous Game Winner",
      type: "Outrights",
      date: "15 June 2024",
      time: "3:59:00",
      stake: 150.0,
      payout: 135.0,
      odds: "9/10",
    },
  ];

  const totalStake = bets.reduce((sum, bet) => sum + bet.stake, 0);

  const tabButtonClass = (isActive: boolean) =>
    cn(
      "px-6 py-2 text-white font-medium",
      "bg-gradient-to-b from-[#FEDD18] via-[#E4AF18] to-[#CB9218]",
      "rounded-t-lg flex items-center gap-2",
      "flex-1",
      isActive ? "opacity-100" : "opacity-80 hover:opacity-90"
    );

  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetTrigger asChild>{children}</SheetTrigger>
        <SheetContent side="bottom" className="bg-transparent border-0 p-0">
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className="w-full max-w-xl mx-auto"
          >
            <div className="flex">
              <button
                onClick={() => setActiveTab("betslip")}
                className={tabButtonClass(activeTab === "betslip")}
              >
                <span className="text-lg">📋</span> Betslip
                <span className="bg-red-600 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center">
                  3
                </span>
              </button>
              <button
                onClick={() => setActiveTab("history")}
                className={tabButtonClass(activeTab === "history")}
              >
                <span className="text-lg">📋</span> History
              </button>
              <button className={tabButtonClass(false)}>
                <span className="text-lg">🗑️</span> Delete all
              </button>
            </div>

            <div className="bg-white p-4 space-y-2 max-h-[60vh] overflow-y-auto">
              {activeTab === "betslip" ? (
                <>
                  {bets.map((bet, index) => (
                    <div
                      key={bet.id}
                      onClick={() => setSelectedBetId(bet.id)}
                      className="bg-white rounded-lg p-4 shadow-md relative cursor-pointer hover:bg-gray-50"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-black font-medium">
                              ({index + 1})
                            </span>
                            <h3 className="text-black font-medium">
                              {bet.title}
                            </h3>
                          </div>
                          <p className="text-black/70 text-sm">{bet.type}</p>
                          <p className="text-black/50 text-sm">
                            {bet.date} | {bet.time}
                          </p>
                        </div>
                        <div className="text-black font-medium">{bet.odds}</div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <div>
                          <p className="text-black/70">Stake</p>
                          <div className="flex items-center">
                            <div className="w-2 h-full bg-[#CB9218] mr-2" />
                            <p className="text-black">
                              R{bet.stake.toFixed(2)}
                            </p>
                          </div>
                        </div>
                        <div>
                          <p className="text-black/70">Payout</p>
                          <div className="flex items-center">
                            <div className="w-2 h-full bg-[#CB9218] mr-2" />
                            <p className="text-black">
                              R{bet.payout.toFixed(2)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  <button
                    className={cn(
                      "w-full h-12 font-medium mt-4",
                      "bg-gradient-to-b from-[#FEDD18] via-[#E4AF18] to-[#CB9218]",
                      "hover:from-[#E4AF18] hover:via-[#CB9218] hover:to-[#B27918]",
                      "text-black"
                    )}
                  >
                    Submit R{totalStake.toFixed(2)}
                  </button>
                </>
              ) : (
                <div className="space-y-2">
                  {historyBets.map((bet, index) => (
                    <div
                      key={bet.id}
                      className="bg-white rounded-lg p-4 shadow-md relative"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-black font-medium">
                              ({index + 1})
                            </span>
                            <h3 className="text-black font-medium">
                              {bet.title}
                            </h3>
                          </div>
                          <p className="text-black/70 text-sm">{bet.type}</p>
                          <p className="text-black/50 text-sm">
                            {bet.date} | {bet.time}
                          </p>
                        </div>
                        <div className="text-black font-medium">{bet.odds}</div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <div>
                          <p className="text-black/70">Stake</p>
                          <div className="flex items-center">
                            <div className="w-2 h-full bg-[#CB9218] mr-2" />
                            <p className="text-black">
                              R{bet.stake.toFixed(2)}
                            </p>
                          </div>
                        </div>
                        <div>
                          <p className="text-black/70">Payout</p>
                          <div className="flex items-center">
                            <div className="w-2 h-full bg-[#CB9218] mr-2" />
                            <p className="text-black">
                              R{bet.payout.toFixed(2)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </SheetContent>
      </Sheet>

      <Sheet
        open={selectedBetId !== null}
        onOpenChange={() => setSelectedBetId(null)}
      >
        <SheetContent side="bottom" className="bg-transparent border-0 p-0">
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className="w-full max-w-xl mx-auto bg-gradient-to-b from-[#FEDD18] via-[#E4AF18] to-[#CB9218] p-1 rounded-t-lg"
          >
            <div className="bg-white p-4 space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-black font-medium">Single (x3)</h3>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">Stake</label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded px-2 h-8"
                          placeholder="R100"
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">
                        Total Stakes
                      </label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <div className="w-full border border-gray-300 rounded px-2 h-8 flex items-center bg-gray-50">
                          R300
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-black font-medium">Multiple (x1)</h3>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">Stake</label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded px-2 h-8"
                          placeholder="R100"
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">
                        Potential Payout
                      </label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <div className="w-full border border-gray-300 rounded px-2 h-8 flex items-center bg-gray-50">
                          R585.90
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right text-sm text-black/70">293/50</div>
                </div>
              </div>

              <button
                className={cn(
                  "w-full h-12 font-medium mt-4",
                  "bg-gradient-to-b from-[#FEDD18] via-[#E4AF18] to-[#CB9218]",
                  "hover:from-[#E4AF18] hover:via-[#CB9218] hover:to-[#B27918]",
                  "text-black rounded-lg"
                )}
              >
                Submit R400.00
              </button>
            </div>
          </motion.div>
        </SheetContent>
      </Sheet>
    </>
  );
}
