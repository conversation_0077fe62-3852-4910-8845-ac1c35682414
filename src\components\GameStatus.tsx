"use client";
import { useGameStore } from "@/store/gameStore";
import { motion } from "framer-motion";

export default function GameStatus() {
  const { currentHand, currentBall, scores, teamNames } = useGameStore();
  
  return (
    <div className="fixed top-4 left-4 z-40 pointer-events-none">
      <motion.div 
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-black/80 border-2 border-[#E1C760] rounded-lg p-3 flex flex-col items-center"
      >
        <div className="flex items-center justify-between w-full mb-2">
          <div className="text-center">
            <span className="text-white text-xs block">Ball</span>
            <span className="text-[#E1C760] text-lg font-bold">{currentBall}</span>
          </div>
          <div className="text-center">
            <span className="text-white text-xs block">Hand</span>
            <span className="text-[#E1C760] text-lg font-bold">{currentHand}/6</span>
          </div>
        </div>
        
        <div className="w-full border-t border-[#E1C760]/30 pt-2">
          <div className="flex justify-between items-center">
            <div className="text-center">
              <span className="text-white text-xs block">{teamNames[1]}</span>
              <span className="text-[#E1C760] text-lg font-bold">{scores.team1}</span>
            </div>
            <span className="text-white mx-2">-</span>
            <div className="text-center">
              <span className="text-white text-xs block">{teamNames[2]}</span>
              <span className="text-[#E1C760] text-lg font-bold">{scores.team2}</span>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
