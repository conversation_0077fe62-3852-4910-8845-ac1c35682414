using Microsoft.EntityFrameworkCore;
using ThuneeAPI.Models;

namespace ThuneeAPI.Data
{
    public class ThuneeDbContext : DbContext
    {
        public ThuneeDbContext(DbContextOptions<ThuneeDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Competition> Competitions { get; set; }
        public DbSet<Game> Games { get; set; }
        public DbSet<PlayerGameStat> PlayerGameStats { get; set; }
        public DbSet<PlayerCompetitionStat> PlayerCompetitionStats { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User entity configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Competition entity configuration
            modelBuilder.Entity<Competition>(entity =>
            {
                entity.Property(e => e.Status).HasDefaultValue("upcoming");
            });

            // Game entity configuration
            modelBuilder.Entity<Game>(entity =>
            {
                entity.HasOne(g => g.Competition)
                      .WithMany(c => c.Games)
                      .HasForeignKey(g => g.CompetitionId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // PlayerGameStat entity configuration
            modelBuilder.Entity<PlayerGameStat>(entity =>
            {
                entity.HasOne(pgs => pgs.Game)
                      .WithMany(g => g.PlayerStats)
                      .HasForeignKey(pgs => pgs.GameId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(pgs => pgs.User)
                      .WithMany(u => u.GameStats)
                      .HasForeignKey(pgs => pgs.UserId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // PlayerCompetitionStat entity configuration
            modelBuilder.Entity<PlayerCompetitionStat>(entity =>
            {
                entity.HasOne(pcs => pcs.Competition)
                      .WithMany(c => c.PlayerStats)
                      .HasForeignKey(pcs => pcs.CompetitionId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(pcs => pcs.User)
                      .WithMany(u => u.CompetitionStats)
                      .HasForeignKey(pcs => pcs.UserId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.CompetitionId, e.UserId }).IsUnique();
                entity.Property(e => e.WinRate).HasPrecision(5, 2);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed competitions
            modelBuilder.Entity<Competition>().HasData(
                new Competition
                {
                    Id = 1,
                    Name = "Summer Championship 2025",
                    Description = "The biggest Thunee tournament of the summer! Join now to compete for amazing prizes.",
                    StartDate = new DateTime(2025, 6, 1),
                    EndDate = new DateTime(2025, 8, 31),
                    FirstPrize = "$500",
                    SecondPrize = "$250",
                    ThirdPrize = "$100",
                    Status = "active",
                    CreatedAt = DateTime.UtcNow
                },
                new Competition
                {
                    Id = 2,
                    Name = "Weekly Challenge Cup",
                    Description = "Weekly competition with new challenges every week.",
                    StartDate = new DateTime(2025, 5, 15),
                    EndDate = new DateTime(2025, 5, 22),
                    FirstPrize = "$100",
                    SecondPrize = "$50",
                    ThirdPrize = "$25",
                    Status = "completed",
                    CreatedAt = DateTime.UtcNow
                },
                new Competition
                {
                    Id = 3,
                    Name = "Beginner's Tournament",
                    Description = "Perfect for new players! Only players with less than 10 games can participate.",
                    StartDate = new DateTime(2025, 6, 15),
                    EndDate = new DateTime(2025, 6, 30),
                    FirstPrize = "$200",
                    SecondPrize = "$100",
                    ThirdPrize = "$50",
                    Status = "upcoming",
                    CreatedAt = DateTime.UtcNow
                },
                new Competition
                {
                    Id = 4,
                    Name = "Pro League Season 1",
                    Description = "The most competitive Thunee league for professional players.",
                    StartDate = new DateTime(2025, 7, 1),
                    EndDate = new DateTime(2025, 9, 30),
                    FirstPrize = "$1000",
                    SecondPrize = "$500",
                    ThirdPrize = "$250",
                    Status = "upcoming",
                    CreatedAt = DateTime.UtcNow
                },
                new Competition
                {
                    Id = 5,
                    Name = "Community Cup",
                    Description = "A friendly tournament organized by the community.",
                    StartDate = new DateTime(2025, 5, 1),
                    EndDate = new DateTime(2025, 5, 14),
                    FirstPrize = "$150",
                    SecondPrize = "$75",
                    ThirdPrize = "$25",
                    Status = "completed",
                    CreatedAt = DateTime.UtcNow
                }
            );
        }
    }
}
