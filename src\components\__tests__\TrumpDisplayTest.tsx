"use client";
import { useState } from "react";
import { useGameStore } from "@/store/gameStore";
import { Button } from "../ui/button";
import TrumpDisplay from "../TrumpDisplay";

/**
 * Test component for TrumpDisplay
 * This component allows us to manually test the TrumpDisplay behavior
 * by simulating card plays and checking if the trump is shown/hidden correctly
 */
export default function TrumpDisplayTest() {
  const { updateGameState, playedCards, trumpSuit, currentHand, currentBall } = useGameStore();
  const [testCard, setTestCard] = useState({
    id: "test-card-1",
    value: "A",
    suit: "hearts",
    image: "/CardFaces/AH.svg",
    points: 11,
    playedBy: "test-player-1"
  });

  // Function to simulate playing a card
  const playTestCard = () => {
    // Create a new test card with a unique ID
    const newCard = {
      ...testCard,
      id: `test-card-${playedCards.length + 1}`,
      value: ["A", "K", "Q", "J", "10", "9"][Math.floor(Math.random() * 6)],
      suit: ["hearts", "diamonds", "clubs", "spades"][Math.floor(Math.random() * 4)],
    };

    // Update the game state with the new card
    updateGameState({
      playedCards: [...playedCards, newCard]
    });

    // Update the test card for the next play
    setTestCard(newCard);
  };

  // Function to clear played cards
  const clearPlayedCards = () => {
    updateGameState({
      playedCards: []
    });
  };

  // Function to set trump suit
  const setTrumpSuit = (suit: string) => {
    updateGameState({
      trumpSuit: suit
    });
  };

  // Function to increment hand number
  const incrementHand = () => {
    updateGameState({
      currentHand: currentHand + 1
    });
  };

  // Function to increment ball number
  const incrementBall = () => {
    updateGameState({
      currentBall: currentBall + 1,
      currentHand: 1 // Reset hand number when ball increments
    });
  };

  return (
    <div className="p-6 bg-gray-900 min-h-screen text-white">
      <h1 className="text-2xl font-bold mb-6">Trump Display Test</h1>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Current State</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p><strong>Trump Suit:</strong> {trumpSuit || "None"}</p>
            <p><strong>Current Hand:</strong> {currentHand}</p>
            <p><strong>Current Ball:</strong> {currentBall}</p>
          </div>
          <div>
            <p><strong>Played Cards:</strong> {playedCards.length}</p>
            <ul className="list-disc pl-5">
              {playedCards.map((card, index) => (
                <li key={index}>
                  {card.value} of {card.suit} (played by {(card as any).playedBy})
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Actions</h2>
        <div className="flex flex-wrap gap-4">
          <Button onClick={playTestCard} className="bg-blue-600 hover:bg-blue-700">
            Play Test Card
          </Button>
          <Button onClick={clearPlayedCards} className="bg-red-600 hover:bg-red-700">
            Clear Played Cards
          </Button>
          <Button onClick={() => setTrumpSuit("hearts")} className="bg-red-500 hover:bg-red-600">
            Set Trump: Hearts
          </Button>
          <Button onClick={() => setTrumpSuit("diamonds")} className="bg-red-500 hover:bg-red-600">
            Set Trump: Diamonds
          </Button>
          <Button onClick={() => setTrumpSuit("clubs")} className="bg-gray-700 hover:bg-gray-800">
            Set Trump: Clubs
          </Button>
          <Button onClick={() => setTrumpSuit("spades")} className="bg-gray-700 hover:bg-gray-800">
            Set Trump: Spades
          </Button>
          <Button onClick={incrementHand} className="bg-green-600 hover:bg-green-700">
            Next Hand
          </Button>
          <Button onClick={incrementBall} className="bg-purple-600 hover:bg-purple-700">
            Next Ball
          </Button>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Instructions</h2>
        <ol className="list-decimal pl-5">
          <li>Set a trump suit using one of the "Set Trump" buttons</li>
          <li>Click "Play Test Card" to simulate playing a card</li>
          <li>The trump should appear for 10 seconds and then disappear</li>
          <li>Click "Clear Played Cards" to reset the played cards</li>
          <li>Click "Next Hand" and play a card - the trump should NOT be revealed again</li>
          <li>Click "Next Ball" and play a card - the trump SHOULD be revealed again for 10 seconds</li>
        </ol>
      </div>

      {/* The TrumpDisplay component being tested */}
      <TrumpDisplay />
    </div>
  );
}
