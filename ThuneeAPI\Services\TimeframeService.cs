using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class TimeframeService : ITimeframeService
    {
        public void InitTimeframeVoting(Lobby lobby)
        {
            lobby.TimeframeVoting = new TimeframeVoting
            {
                Votes = new Dictionary<string, int>(),
                VoteCounts = new Dictionary<int, int>(),
                IsComplete = false,
                StartTime = DateTime.UtcNow,
                TimeLimit = lobby.TimeSettings?.VotingTimeLimit ?? 15
            };

            // Initialize vote counts for each timeframe option
            var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
            foreach (var option in timeOptions)
            {
                lobby.TimeframeVoting.VoteCounts[option] = 0;
            }
        }

        public bool RecordTimeframeVote(Lobby lobby, string playerId, int timeframe)
        {
            if (lobby.TimeframeVoting == null)
            {
                return false;
            }

            if (lobby.TimeframeVoting.IsComplete)
            {
                return false;
            }

            // Check if timeframe is valid
            var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
            if (!timeOptions.Contains(timeframe))
            {
                return false;
            }

            // Remove previous vote if exists
            if (lobby.TimeframeVoting.Votes.TryGetValue(playerId, out var previousVote))
            {
                lobby.TimeframeVoting.VoteCounts[previousVote]--;
            }

            // Record new vote
            lobby.TimeframeVoting.Votes[playerId] = timeframe;
            lobby.TimeframeVoting.VoteCounts[timeframe]++;

            return true;
        }

        public void UpdateVoteResults(Lobby lobby)
        {
            if (lobby.TimeframeVoting == null)
            {
                return;
            }

            // Recalculate vote counts
            lobby.TimeframeVoting.VoteCounts.Clear();
            var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
            
            foreach (var option in timeOptions)
            {
                lobby.TimeframeVoting.VoteCounts[option] = 0;
            }

            foreach (var vote in lobby.TimeframeVoting.Votes.Values)
            {
                if (lobby.TimeframeVoting.VoteCounts.ContainsKey(vote))
                {
                    lobby.TimeframeVoting.VoteCounts[vote]++;
                }
            }
        }

        public bool AllPlayersVoted(Lobby lobby)
        {
            if (lobby.TimeframeVoting == null)
            {
                return false;
            }

            return lobby.TimeframeVoting.Votes.Count >= lobby.Players.Count;
        }

        public int? DetermineSelectedTimeframe(Lobby lobby)
        {
            if (lobby.TimeframeVoting == null || !AllPlayersVoted(lobby))
            {
                return null;
            }

            // Find the timeframe with the most votes
            var maxVotes = lobby.TimeframeVoting.VoteCounts.Values.Max();
            var winners = lobby.TimeframeVoting.VoteCounts
                .Where(kvp => kvp.Value == maxVotes)
                .Select(kvp => kvp.Key)
                .ToList();

            if (winners.Count == 1)
            {
                return winners[0];
            }

            // In case of tie, pick the middle value or use some other tie-breaking logic
            return winners.OrderBy(x => x).Skip(winners.Count / 2).First();
        }

        public TimeframeVoting? GetVotingState(Lobby lobby)
        {
            return lobby.TimeframeVoting;
        }

        public void ResetTimeframeVoting(Lobby lobby)
        {
            lobby.TimeframeVoting = null;
        }

        public TurnTimerState InitTurnTimer(Lobby lobby, string playerId)
        {
            // Get the selected timeframe or use default
            var timeframe = lobby.TimeframeVoting?.SelectedTimeframe ?? 5;

            var turnTimerState = new TurnTimerState
            {
                CurrentPlayerId = playerId,
                TimeRemaining = timeframe,
                TimerActive = true,
                StartTime = DateTime.UtcNow,
                InitialTime = timeframe
            };

            lobby.TurnTimerState = turnTimerState;
            return turnTimerState;
        }

        public async Task UpdateTurnTimerAsync(Lobby lobby)
        {
            if (lobby.TurnTimerState == null || !lobby.TurnTimerState.TimerActive)
            {
                return;
            }

            var elapsed = DateTime.UtcNow - lobby.TurnTimerState.StartTime;
            var remaining = lobby.TurnTimerState.InitialTime - (int)elapsed.TotalSeconds;

            lobby.TurnTimerState.TimeRemaining = Math.Max(0, remaining);

            if (lobby.TurnTimerState.TimeRemaining <= 0)
            {
                lobby.TurnTimerState.TimerActive = false;
                // Handle timeout logic here
            }

            await Task.CompletedTask;
        }

        public void StopTurnTimer(Lobby lobby)
        {
            if (lobby.TurnTimerState != null)
            {
                lobby.TurnTimerState.TimerActive = false;
            }
        }
    }
}
