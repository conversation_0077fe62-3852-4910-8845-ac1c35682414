@echo off
echo Starting Thunee Development Environment...
echo.

REM Kill any existing processes on the ports we need
echo Stopping existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do (
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo Starting ASP.NET Core API on http://localhost:5000...
start "Thunee API" cmd /k "cd /d C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI && set ASPNETCORE_ENVIRONMENT=Development && dotnet run --urls http://localhost:5000"

echo Waiting for API to start...
timeout /t 5 /nobreak >nul

echo.
echo Starting React Frontend on http://localhost:5173...
start "Thunee Frontend" cmd /k "cd /d C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE && npm run dev"

echo.
echo Development environment is starting...
echo.
echo API will be available at: http://localhost:5000
echo Frontend will be available at: http://localhost:5173
echo.
echo Both services will open in separate command windows.
echo Close those windows to stop the services.
echo.
pause
