/* FourBallResultDisplay.css */

.four-ball-container {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.four-ball-backdrop {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

.four-ball-card {
  position: relative;
  width: 100%;
  max-width: 600px;
  background-color: black;
  border: 2px solid #E1C760;
  border-radius: 0.5rem;
  padding: 1.5rem;
  z-index: 10;
  color: white;
  max-height: 90vh;
  overflow-y: auto;
}

.four-ball-header {
  text-align: center;
  margin-bottom: 1rem;
}

.four-ball-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #E1C760;
}

.four-ball-subtitle {
  color: white;
  margin-top: 0.5rem;
}

.four-ball-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.four-ball-section {
  background-color: #111827;
  border-radius: 0.5rem;
  padding: 1rem;
}

.four-ball-section-title {
  color: #E1C760;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.four-ball-text {
  color: white;
  margin-bottom: 0.25rem;
}

.four-ball-text-bold {
  font-weight: 600;
}

.four-ball-status {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
}

.four-ball-status-text {
  color: white;
  margin-right: 0.5rem;
}

.four-ball-status-valid {
  display: flex;
  align-items: center;
  color: #10b981;
}

.four-ball-status-invalid {
  display: flex;
  align-items: center;
  color: #ef4444;
}

.four-ball-status-icon {
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.25rem;
}

.four-ball-cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.four-ball-card-wrapper {
  width: 3rem;
  height: 4rem;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s;
  margin-bottom: 1.25rem; /* Add space for player name below */
}

.four-ball-card-wrapper:hover {
  transform: translateY(-5px);
}

.four-ball-card-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.four-ball-card-highlight {
  box-shadow: 0 0 0 3px #10b981;
  border-radius: 0.25rem;
  transform: scale(1.05);
  z-index: 1;
}

.four-ball-card-accused {
  box-shadow: 0 0 0 3px #ef4444;
  border-radius: 0.25rem;
  transform: scale(1.05);
  z-index: 2;
}

.four-ball-card-player {
  position: absolute;
  bottom: -18px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 0.6rem;
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 1px;
  border-radius: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.four-ball-hand-details {
  background-color: #1f2937;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  display: none; /* Hidden by default */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease-in-out;
}

.four-ball-hand-details.visible {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.four-ball-hand-title {
  color: #E1C760;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.four-ball-hand-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #1a1a1a;
  border-radius: 0.5rem;
  border: 1px solid #333;
  margin-bottom: 1rem;
}

.four-ball-footer {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

.four-ball-button {
  background-color: #E1C760;
  color: black;
  font-weight: bold;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.four-ball-button:hover {
  background-color: #c9b052;
}

/* Media queries for responsive design */
@media (max-width: 640px) {
  .four-ball-card {
    padding: 1rem;
  }

  .four-ball-title {
    font-size: 1.25rem;
  }

  .four-ball-section {
    padding: 0.75rem;
  }

  .four-ball-section-title {
    font-size: 1rem;
  }

  .four-ball-card-wrapper {
    width: 2.5rem;
    height: 3.5rem;
    margin-bottom: 1rem;
  }

  .four-ball-card-player {
    font-size: 0.5rem;
    bottom: -15px;
  }

  .four-ball-hand-cards {
    gap: 0.25rem;
  }
}

/* Mobile landscape orientation */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .four-ball-container {
    align-items: flex-start;
    padding-top: 0.5rem;
  }

  .four-ball-card {
    padding: 0.75rem;
    max-height: 85vh;
    font-size: 0.875rem;
  }

  .four-ball-title {
    font-size: 1.125rem;
  }

  .four-ball-content {
    gap: 0.5rem;
  }

  .four-ball-section {
    padding: 0.5rem;
  }

  .four-ball-section-title {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .four-ball-card-wrapper {
    width: 2rem;
    height: 3rem;
    margin-bottom: 0.75rem;
  }

  .four-ball-card-player {
    font-size: 0.45rem;
    bottom: -14px;
  }

  .four-ball-hand-cards {
    gap: 0.2rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .four-ball-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Mobile portrait orientation - additional adjustments */
@media (max-width: 480px) and (orientation: portrait) {
  .four-ball-card {
    padding: 0.75rem;
    max-width: 95%;
  }

  .four-ball-card-wrapper {
    width: 2.2rem;
    height: 3.2rem;
  }

  .four-ball-hand-cards {
    justify-content: center;
  }

  .four-ball-text {
    font-size: 0.9rem;
  }
}
