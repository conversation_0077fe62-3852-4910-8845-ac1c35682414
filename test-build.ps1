# Test build script for ThuneeAPI
Write-Host "Testing ThuneeAPI build..." -ForegroundColor Green

# Navigate to the ThuneeAPI directory
$projectPath = ".\ThuneeAPI"
if (Test-Path $projectPath) {
    Write-Host "Found ThuneeAPI directory" -ForegroundColor Green
    Set-Location $projectPath
    
    # Try to restore packages
    Write-Host "Restoring packages..." -ForegroundColor Yellow
    dotnet restore
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Package restore successful" -ForegroundColor Green
        
        # Try to build
        Write-Host "Building project..." -ForegroundColor Yellow
        dotnet build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Build successful!" -ForegroundColor Green
            
            # Try to run (with timeout)
            Write-Host "Starting application..." -ForegroundColor Yellow
            $job = Start-Job -ScriptBlock { 
                Set-Location $using:PWD
                dotnet run 
            }
            
            # Wait for 10 seconds to see if it starts
            Start-Sleep -Seconds 10
            
            if ($job.State -eq "Running") {
                Write-Host "Application started successfully!" -ForegroundColor Green
                Write-Host "You can now access the API at http://localhost:5000" -ForegroundColor Cyan
                Write-Host "Test client available at http://localhost:5000/test-client.html" -ForegroundColor Cyan
                
                # Stop the job
                Stop-Job $job
                Remove-Job $job
            } else {
                Write-Host "Application failed to start" -ForegroundColor Red
                Receive-Job $job
                Remove-Job $job
            }
        } else {
            Write-Host "Build failed" -ForegroundColor Red
        }
    } else {
        Write-Host "Package restore failed" -ForegroundColor Red
    }
    
    # Return to original directory
    Set-Location ..
} else {
    Write-Host "ThuneeAPI directory not found" -ForegroundColor Red
}

Write-Host "Test completed" -ForegroundColor Green
