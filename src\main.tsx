// Import polyfills first
import "./polyfills";

import React from "react";
import ReactDOM from "react-dom/client";
import { RouterProvider } from "react-router-dom";
import { Toaster } from "sonner";
import router from "./router";
import "./index.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <RouterProvider router={router} />
    <Toaster
      theme="dark"
      position="top-center"
      toastOptions={{
        style: {
          background: '#1a1a1a',
          border: '1px solid #E1C760',
          color: '#E1C760',
        },
      }}
    />
  </React.StrictMode>
);
