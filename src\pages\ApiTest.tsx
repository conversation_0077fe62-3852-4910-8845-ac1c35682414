import React, { useState } from 'react';
import { leaderboardService } from '../services/leaderboardService';
import { authService } from '../services/authService';

const ApiTest: React.FC = () => {
  const [competitions, setCompetitions] = useState<any[]>([]);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authResult, setAuthResult] = useState<any>(null);

  const testGetCompetitions = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await leaderboardService.getCompetitions();
      setCompetitions(data);
      console.log('Competitions:', data);
    } catch (err) {
      setError(`Error fetching competitions: ${err}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const testGetLeaderboard = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await leaderboardService.getLeaderboard('1', 1, 10);
      setLeaderboard(data.entries);
      console.log('Leaderboard:', data);
    } catch (err) {
      setError(`Error fetching leaderboard: ${err}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const testRegister = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await authService.register({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      });
      setAuthResult(result);
      console.log('Register result:', result);
    } catch (err) {
      setError(`Error during registration: ${err}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const testVerifyOtp = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await authService.verifyOtp({
        username: 'testuser',
        otpCode: '1234'
      });
      setAuthResult(result);
      console.log('Verify OTP result:', result);
    } catch (err) {
      setError(`Error during OTP verification: ${err}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const testSaveGameResult = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await leaderboardService.saveGameResult({
        lobbyCode: 'TEST123',
        competitionId: 1,
        winningTeam: 1,
        team1Balls: 12,
        team2Balls: 6,
        totalBalls: 18,
        playerResults: [
          {
            playerName: 'testuser',
            team: 1,
            position: 1,
            isWinner: true,
            ballsWon: 12,
            totalPoints: 150,
            jordhiCalls: 2,
            successfulJordhiCalls: 1,
            khanakCalls: 0,
            successfulKhanakCalls: 0,
            thuneeCalls: 1,
            successfulThuneeCalls: 1,
            doubleCalls: 0,
            successfulDoubleCalls: 0,
            fourBallPenalties: 0,
            handsWon: 8,
            totalHands: 12
          },
          {
            playerName: 'player2',
            team: 1,
            position: 3,
            isWinner: true,
            ballsWon: 12,
            totalPoints: 120,
            jordhiCalls: 1,
            successfulJordhiCalls: 1,
            khanakCalls: 0,
            successfulKhanakCalls: 0,
            thuneeCalls: 0,
            successfulThuneeCalls: 0,
            doubleCalls: 0,
            successfulDoubleCalls: 0,
            fourBallPenalties: 0,
            handsWon: 6,
            totalHands: 12
          },
          {
            playerName: 'player3',
            team: 2,
            position: 2,
            isWinner: false,
            ballsWon: 6,
            totalPoints: 80,
            jordhiCalls: 0,
            successfulJordhiCalls: 0,
            khanakCalls: 0,
            successfulKhanakCalls: 0,
            thuneeCalls: 0,
            successfulThuneeCalls: 0,
            doubleCalls: 0,
            successfulDoubleCalls: 0,
            fourBallPenalties: 1,
            handsWon: 4,
            totalHands: 12
          },
          {
            playerName: 'player4',
            team: 2,
            position: 4,
            isWinner: false,
            ballsWon: 6,
            totalPoints: 70,
            jordhiCalls: 0,
            successfulJordhiCalls: 0,
            khanakCalls: 0,
            successfulKhanakCalls: 0,
            thuneeCalls: 0,
            successfulThuneeCalls: 0,
            doubleCalls: 0,
            successfulDoubleCalls: 0,
            fourBallPenalties: 0,
            handsWon: 2,
            totalHands: 12
          }
        ]
      });
      console.log('Save game result:', result);
      setAuthResult({ success: result, message: result ? 'Game saved successfully' : 'Failed to save game' });
    } catch (err) {
      setError(`Error saving game result: ${err}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>API Test Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Competition API Tests</h2>
        <button onClick={testGetCompetitions} disabled={loading}>
          Test Get Competitions
        </button>
        <button onClick={testGetLeaderboard} disabled={loading} style={{ marginLeft: '10px' }}>
          Test Get Leaderboard
        </button>
        <button onClick={testSaveGameResult} disabled={loading} style={{ marginLeft: '10px' }}>
          Test Save Game Result
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Authentication API Tests</h2>
        <button onClick={testRegister} disabled={loading}>
          Test Register
        </button>
        <button onClick={testVerifyOtp} disabled={loading} style={{ marginLeft: '10px' }}>
          Test Verify OTP (1234)
        </button>
      </div>

      {loading && <p>Loading...</p>}
      {error && <p style={{ color: 'red' }}>Error: {error}</p>}

      {competitions.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Competitions:</h3>
          <pre>{JSON.stringify(competitions, null, 2)}</pre>
        </div>
      )}

      {leaderboard.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Leaderboard:</h3>
          <pre>{JSON.stringify(leaderboard, null, 2)}</pre>
        </div>
      )}

      {authResult && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Auth Result:</h3>
          <pre>{JSON.stringify(authResult, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default ApiTest;
