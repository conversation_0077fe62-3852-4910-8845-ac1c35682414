"use client";
import { useGameStore } from "@/store/gameStore";

export default function TargetScores() {
  const { targetScores, biddingTeam, teamNames, currentBid, biddingComplete } = useGameStore();

  // If bidding hasn't completed yet, don't show anything
  if (!biddingComplete) {
    return null;
  }

  // If no one bid (everyone passed), show the default target scores
  const noBidsPlaced = currentBid === 0;

  const team1IsBidding = biddingTeam === 1;
  const team2IsBidding = biddingTeam === 2;

  return (
    <div className="bg-black/70 border border-[#E1C760]/30 rounded-lg p-3 text-white text-sm">
      <h3 className="text-[#E1C760] font-bold mb-2 text-center">Target Scores</h3>

      <div className="grid grid-cols-2 gap-2">
        <div className={`p-2 rounded ${team1IsBidding && !noBidsPlaced ? 'bg-[#E1C760]/20 border border-[#E1C760]' : ''}`}>
          <div className="flex justify-between items-center">
            <span className="font-medium">{teamNames[1]}:</span>
            <span className="font-bold text-[#E1C760]">{targetScores.team1}</span>
          </div>
          {team1IsBidding && !noBidsPlaced && (
            <div className="mt-1 text-xs text-[#E1C760]/80">
              Bidding Team (Bid: {currentBid})
            </div>
          )}
        </div>

        <div className={`p-2 rounded ${team2IsBidding && !noBidsPlaced ? 'bg-[#E1C760]/20 border border-[#E1C760]' : ''}`}>
          <div className="flex justify-between items-center">
            <span className="font-medium">{teamNames[2]}:</span>
            <span className="font-bold text-[#E1C760]">{targetScores.team2}</span>
          </div>
          {team2IsBidding && !noBidsPlaced && (
            <div className="mt-1 text-xs text-[#E1C760]/80">
              Bidding Team (Bid: {currentBid})
            </div>
          )}
        </div>
      </div>

      <div className="mt-2 text-xs text-center text-gray-400">
        {noBidsPlaced ? (
          "No bids placed - standard target of 105 for both teams"
        ) : (
          "First team to reach their target wins the ball"
        )}
      </div>
    </div>
  );
}
