"use client";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Competition, LeaderboardEntry, LeaderboardFilter } from "@/types/leaderboard";
import { leaderboardService } from "@/services/leaderboardService";
import { ArrowLeft, Trophy, Calendar, Filter, ChevronLeft, ChevronRight, ArrowUpDown } from "lucide-react";
import BurgerMenu from "@/components/BurgerMenu";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function CompetitionLeaderboard() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Filter state
  const [filters, setFilters] = useState<LeaderboardFilter>({
    sortBy: 'score',
    sortDirection: 'desc',
    minGames: 0
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;
      
      setLoading(true);
      try {
        // Fetch competition details
        const competitionData = await leaderboardService.getCompetition(id);
        if (competitionData) {
          setCompetition(competitionData);
          
          // Fetch leaderboard with pagination and filters
          const leaderboardData = await leaderboardService.getLeaderboard(
            id,
            currentPage,
            itemsPerPage,
            filters
          );
          
          setLeaderboard(leaderboardData.entries);
          setTotalItems(leaderboardData.totalItems);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, currentPage, itemsPerPage, filters]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleFilterChange = (newFilters: Partial<LeaderboardFilter>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when changing filters
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const totalPages = Math.ceil(totalItems / itemsPerPage);

  return (
    <div className="min-h-screen bg-dark text-white flex flex-col relative">
      {/* BurgerMenu */}
      <BurgerMenu />

      {/* Main Content */}
      <div className="flex flex-col items-center px-6 pb-16 space-y-6 mt-16 overflow-y-auto h-[calc(100vh-4rem)] z-10 relative">
        {/* Header */}
        <div className="w-full flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
            onClick={() => navigate("/competitions")}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-xl font-bold text-[#E1C760] max-w-[200px]">
            {loading ? "Loading..." : competition?.name || "Leaderboard"}
          </h1>
          <div className="w-[60px]"></div> {/* Spacer for alignment */}
        </div>

        {/* Competition Details */}
        {!loading && competition && (
          <div className="w-full bg-[#1A1A1A] border border-[#333333] rounded-lg p-4 mb-4">
            <h2 className="text-xl font-semibold text-[#E1C760] mb-2">
              {competition.name}
            </h2>
            
            <div className="flex items-center text-sm text-gray-400 mb-3">
              <Calendar className="h-4 w-4 mr-1" />
              {formatDate(competition.startDate)} - {formatDate(competition.endDate)}
            </div>
            
            <p className="text-sm text-gray-300 mb-4">
              {competition.description || "No description available."}
            </p>
            
            <div className="flex flex-wrap gap-3">
              <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                <Trophy className="h-4 w-4 mr-1 text-yellow-500" />
                <span className="text-sm">1st: {competition.prizes.first}</span>
              </div>
              <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                <Trophy className="h-4 w-4 mr-1 text-gray-400" />
                <span className="text-sm">2nd: {competition.prizes.second}</span>
              </div>
              <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                <Trophy className="h-4 w-4 mr-1 text-amber-700" />
                <span className="text-sm">3rd: {competition.prizes.third}</span>
              </div>
            </div>
          </div>
        )}

        {/* Filters and Pagination Controls */}
        <div className="w-full flex flex-wrap justify-between items-center gap-2 mb-2">
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="border-[#E1C760] text-[#E1C760]">
                  <Filter className="h-4 w-4 mr-1" />
                  Filters
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-[#1A1A1A] border-[#333333]">
                <DropdownMenuItem onClick={() => handleFilterChange({ minGames: 0 })}>
                  All Games
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleFilterChange({ minGames: 5 })}>
                  Min 5 Games
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleFilterChange({ minGames: 10 })}>
                  Min 10 Games
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleFilterChange({ minGames: 20 })}>
                  Min 20 Games
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="border-[#E1C760] text-[#E1C760]">
                  <ArrowUpDown className="h-4 w-4 mr-1" />
                  Sort
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-[#1A1A1A] border-[#333333]">
                <DropdownMenuItem onClick={() => handleFilterChange({ sortBy: 'score', sortDirection: 'desc' })}>
                  Score (High to Low)
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleFilterChange({ sortBy: 'winRate', sortDirection: 'desc' })}>
                  Win Rate (High to Low)
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleFilterChange({ sortBy: 'gamesPlayed', sortDirection: 'desc' })}>
                  Games Played (High to Low)
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="flex items-center gap-2">
            <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
              <SelectTrigger className="w-[100px] border-[#E1C760] text-[#E1C760]">
                <SelectValue placeholder="10 per page" />
              </SelectTrigger>
              <SelectContent className="bg-[#1A1A1A] border-[#333333]">
                <SelectItem value="5">5 per page</SelectItem>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="20">20 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Leaderboard Table */}
        {loading ? (
          <div className="flex items-center justify-center h-64 w-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E1C760]"></div>
          </div>
        ) : (
          <div className="w-full overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-[#2A2A2A] text-[#E1C760]">
                  <th className="px-4 py-2 text-left">Rank</th>
                  <th className="px-4 py-2 text-left">Player</th>
                  <th className="px-4 py-2 text-right">Score</th>
                  <th className="px-4 py-2 text-right">Games</th>
                  <th className="px-4 py-2 text-right">Win Rate</th>
                </tr>
              </thead>
              <tbody>
                {leaderboard.map((entry) => (
                  <tr 
                    key={entry.id} 
                    className="border-b border-[#333333] hover:bg-[#2A2A2A]/50 transition-colors"
                  >
                    <td className="px-4 py-3 text-left">
                      {entry.rank === 1 && <span className="text-yellow-500 font-bold">#1</span>}
                      {entry.rank === 2 && <span className="text-gray-400 font-bold">#2</span>}
                      {entry.rank === 3 && <span className="text-amber-700 font-bold">#3</span>}
                      {entry.rank > 3 && <span>#{entry.rank}</span>}
                    </td>
                    <td className="px-4 py-3 text-left font-medium">{entry.playerName}</td>
                    <td className="px-4 py-3 text-right">{entry.score.toLocaleString()}</td>
                    <td className="px-4 py-3 text-right">{entry.gamesPlayed}</td>
                    <td className="px-4 py-3 text-right">{entry.winRate.toFixed(1)}%</td>
                  </tr>
                ))}
                
                {leaderboard.length === 0 && (
                  <tr>
                    <td colSpan={5} className="px-4 py-8 text-center text-gray-400">
                      No leaderboard data available.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination Controls */}
        {!loading && totalPages > 1 && (
          <div className="w-full flex justify-center items-center gap-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              className="border-[#E1C760] text-[#E1C760]"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              className="border-[#E1C760] text-[#E1C760]"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
