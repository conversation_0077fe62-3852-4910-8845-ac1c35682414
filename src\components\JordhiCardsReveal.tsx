"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useGameStore } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";

interface JordhiCardsRevealProps {
  isOpen: boolean;
  onClose: () => void;
  revealData: {
    playerName: string;
    playerTeam: 1 | 2;
    value: number;
    jordhiSuit: string;
    cardsRevealed: boolean;
    jordhiCards?: Array<{ suit: string, value: string }>;
  };
}

export default function JordhiCardsReveal({
  isOpen,
  onClose,
  revealData,
}: JordhiCardsRevealProps) {
  const { teamNames } = useGameStore();

  // Auto-close after 2 seconds
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        onClose();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose]);

  // Format suit name for display
  const formatSuit = (suit: string) => {
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  return (
    <AnimatePresence>
      {isOpen && revealData && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div
            className="absolute inset-0 bg-black/50"
            onClick={onClose}
          />
          <Card className="relative w-full  bg-black border-2 border-[#E1C760] p-4 z-10">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-[#E1C760]">Jordhi Cards Revealed</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-[#E1C760] hover:text-white hover:bg-gray-800"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="space-y-4">
              {revealData.cardsRevealed ? (
                <>
                  <p className="text-white">
                    <span className="font-bold text-[#E1C760]">{revealData.playerName}</span> from {teamNames[revealData.playerTeam]} has revealed their Jordhi cards for a <span className="font-bold text-[#E1C760]">{revealData.value}</span> Jordhi in <span className="font-bold text-[#E1C760]">{formatSuit(revealData.jordhiSuit)}</span>.
                  </p>

                  {/* Display the cards */}
                  <div className="flex justify-center gap-2 my-4">
                    {revealData.jordhiCards?.map((card, index) => (
                      <div key={index} className="w-20 h-28 relative">
                        <Card className="w-full h-full flex items-center justify-center bg-white border-2 border-[#E1C760]">
                          <img
                            src={getCardImagePath(card.value, card.suit)}
                            alt={`${card.value} of ${card.suit}`}
                            className="w-full h-full object-contain"
                          />
                        </Card>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <p className="text-white">
                  <span className="font-bold text-[#E1C760]">{revealData.playerName}</span> from {teamNames[revealData.playerTeam]} has made a <span className="font-bold text-[#E1C760]">{revealData.value}</span> Jordhi call in <span className="font-bold text-[#E1C760]">{formatSuit(revealData.jordhiSuit)}</span> but chose not to reveal their cards.
                </p>
              )}

              <div className="flex justify-center mt-6">
                <Button
                  onClick={onClose}
                  className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                >
                  Close
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
