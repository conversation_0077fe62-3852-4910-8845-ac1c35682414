import { Di<PERSON> } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// This component is for selecting the trump position, not for cutting the deck
export function TrumpPositionSelector() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="h-10 w-10 rounded-full border-yellow-400/50 bg-black text-yellow-400 hover:bg-black/90 hover:text-yellow-400/90"
        >
          <Dices className="h-5 w-5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-auto border-[#a07a4a] border-2 bg-black/95 p-0 shadow-lg shadow-yellow-400/10"
        sideOffset={5}
      >
        <div className="flex flex-col">
          <div className="border-b border-[#a07a4a] bg-black px-8 py-2 text-center rounded-t-md">
            <span className="text-[1.5rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
              Trump Position
            </span>
          </div>

          <div className="flex gap-1 py-5 px-2 bg-blackglass rounded-b-md">
            <button className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-[#] transition-colors hover:bg-[#a07a4a]/20">
              <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                Top
              </span>
            </button>
            <button className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20">
              <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                Middle
              </span>
            </button>
            <button className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20">
              <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                Bottom
              </span>
            </button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default TrumpPositionSelector;
