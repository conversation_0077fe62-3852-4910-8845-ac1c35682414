@echo off
echo Starting Thunee ASP.NET Core API...

REM Navigate to the correct directory
cd /d "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
if errorlevel 1 (
    echo Error: Could not navigate to ThuneeAPI directory
    echo Please ensure the path exists: C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI
    pause
    exit /b 1
)

echo Current directory: %CD%

echo Restoring packages...
dotnet restore
if errorlevel 1 (
    echo Package restore failed
    pause
    exit /b 1
)

echo Building project...
dotnet build
if errorlevel 1 (
    echo Build failed - check the errors above
    pause
    exit /b 1
)

echo Build successful!
echo Starting the application...
echo.
echo The API will be available at:
echo   http://localhost:5000
echo   https://localhost:5001
echo.
echo Test client available at:
echo   http://localhost:5000/test-client.html
echo.
echo Press Ctrl+C to stop the server
echo.

dotnet run
