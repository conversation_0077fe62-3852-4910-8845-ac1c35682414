using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class TurnService : ITurnService
    {
        private readonly ITimeframeService _timeframeService;
        private readonly ILobbyService _lobbyService;
        private readonly Dictionary<string, Timer> _turnTimers = new();

        public TurnService(ITimeframeService timeframeService, ILobbyService lobbyService)
        {
            _timeframeService = timeframeService;
            _lobbyService = lobbyService;
        }

        // Events
        public event Func<string, PlayerTurnResponse, Task>? PlayerTurnUpdated;
        public event Func<string, object, Task>? TurnTimerUpdated;
        public event Func<string, object, Task>? TurnTimeout;

        public async Task SetPlayerTurnAsync(string lobbyCode, string matchedLobbyCode, string playerId, bool isHandComplete = false)
        {
            var lobby = _lobbyService.GetLobby(lobbyCode);
            if (lobby == null) return;

            // Check if this is the first player's turn after Thunee opportunities
            var isFirstPlayerAfterThunee = lobby.ThuneeOpportunitiesComplete &&
                                         !string.IsNullOrEmpty(lobby.FirstPlayerId) &&
                                         lobby.FirstPlayerId == playerId;

            // Clear any existing turn timer for this lobby
            ClearTurnTimer(lobby);

            // Update lobby turn state
            if (lobby.TurnTimerState == null)
            {
                lobby.TurnTimerState = new TurnTimerState();
            }
            lobby.TurnTimerState.CurrentPlayerId = playerId;

            // Trigger player turn event
            if (PlayerTurnUpdated != null)
            {
                await PlayerTurnUpdated(lobbyCode, new PlayerTurnResponse
                {
                    PlayerId = playerId,
                    IsFirstPlayerAfterThunee = isFirstPlayerAfterThunee
                });

                // Also send to matched lobby if different
                if (matchedLobbyCode != lobbyCode)
                {
                    await PlayerTurnUpdated(matchedLobbyCode, new PlayerTurnResponse
                    {
                        PlayerId = playerId,
                        IsFirstPlayerAfterThunee = isFirstPlayerAfterThunee
                    });
                }
            }

            // If this is the end of a hand, don't start a timer
            if (isHandComplete)
            {
                return;
            }

            // Start turn timer
            await StartTurnTimerAsync(lobby, playerId);
        }

        public void ClearTurnTimer(Lobby lobby)
        {
            // Clear any existing turn timer
            if (_turnTimers.TryGetValue(lobby.LobbyCode, out var existingTimer))
            {
                existingTimer.Dispose();
                _turnTimers.Remove(lobby.LobbyCode);
            }

            if (lobby.TurnTimerState != null)
            {
                lobby.TurnTimerState.TimerActive = false;
            }
        }

        public async Task StartTurnTimerAsync(Lobby lobby, string playerId)
        {
            // Initialize the turn timer
            var turnTimerState = _timeframeService.InitTurnTimer(lobby, playerId);
            var timeframe = turnTimerState.TimeRemaining;

            Console.WriteLine($"Starting turn timer for player {playerId} with {timeframe} seconds");

            // Clear any existing timer
            ClearTurnTimer(lobby);

            // Create a new timer that ticks every second
            var timer = new Timer(async _ =>
            {
                await UpdateTurnTimerAsync(lobby);

                // Check if timer expired
                if (lobby.TurnTimerState != null && lobby.TurnTimerState.TimeRemaining <= 0)
                {
                    await HandleTurnTimeoutAsync(lobby);
                }
            }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

            _turnTimers[lobby.LobbyCode] = timer;
        }

        public async Task UpdateTurnTimerAsync(Lobby lobby)
        {
            await _timeframeService.UpdateTurnTimerAsync(lobby);

            // Trigger timer update event
            if (TurnTimerUpdated != null && lobby.TurnTimerState != null)
            {
                await TurnTimerUpdated(lobby.LobbyCode, new
                {
                    PlayerId = lobby.TurnTimerState.CurrentPlayerId,
                    TimeRemaining = lobby.TurnTimerState.TimeRemaining,
                    TimerActive = lobby.TurnTimerState.TimerActive
                });
            }
        }

        public void StopTurnTimer(Lobby lobby)
        {
            ClearTurnTimer(lobby);
            _timeframeService.StopTurnTimer(lobby);
        }

        private async Task HandleTurnTimeoutAsync(Lobby lobby)
        {
            Console.WriteLine($"Turn timeout for lobby {lobby.LobbyCode}");

            // Stop the timer
            StopTurnTimer(lobby);

            // Trigger timeout event
            if (TurnTimeout != null)
            {
                await TurnTimeout(lobby.LobbyCode, new
                {
                    PlayerId = lobby.TurnTimerState?.CurrentPlayerId,
                    Message = "Player turn timed out"
                });
            }

            // In Thunee, if any player doesn't play within the time limit,
            // the opposite team automatically wins a ball
            await HandleTimeoutPenalty(lobby);
        }

        private async Task HandleTimeoutPenalty(Lobby lobby)
        {
            if (lobby.TurnTimerState?.CurrentPlayerId == null) return;

            var timedOutPlayer = lobby.Players.FirstOrDefault(p => p.Id == lobby.TurnTimerState.CurrentPlayerId);
            if (timedOutPlayer == null) return;

            // Award ball to opposite team
            var oppositeTeam = timedOutPlayer.Team == 1 ? 2 : 1;
            var teamKey = $"team{oppositeTeam}";

            if (!lobby.BallScores.ContainsKey(teamKey))
            {
                lobby.BallScores[teamKey] = 0;
            }
            lobby.BallScores[teamKey]++;

            Console.WriteLine($"Player {timedOutPlayer.Name} timed out. Awarding ball to team {oppositeTeam}");
        }
    }
}
