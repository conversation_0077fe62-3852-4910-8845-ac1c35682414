"use client";
import { useState, useEffect } from "react";
import { useGameStore } from "@/store/gameStore";
import gameService from "@/services/gameService";
import { Button } from "./ui/button";

interface CutOptionsProps {
  isVisible: boolean;
  onCutComplete: () => void;
}

export default function CutOptions({ isVisible, onCutComplete }: CutOptionsProps) {
  const [isCutting, setIsCutting] = useState(false);

  // Add useEffect for debugging
  useEffect(() => {
    console.log("CutOptions rendered with isVisible:", isVisible);
    const currentPlayerId = gameService.getSocketId();
    console.log("Current player ID in CutOptions:", currentPlayerId);
  }, [isVisible]);

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      setIsCutting(false);
    }
  }, [isVisible]);

  if (!isVisible) {
    console.log("CutOptions not visible, returning null");
    return null;
  }

  console.log("CutOptions is visible, rendering component");

  const handleCut = async (position: string | null) => {
    try {
      setIsCutting(true);
      const shouldCut = position !== null;
      console.log(`Player chose to ${shouldCut ? `cut at ${position}` : 'not cut'} the deck`);

      // Send cut decision to server immediately
      // The server will handle showing the animation to all players
      await gameService.sendGameAction("cut_deck", {
        cut: shouldCut,
        position: position
      });

      // Brief delay for UI feedback
      setTimeout(() => {
        setIsCutting(false);
        onCutComplete();
      }, 500);
    } catch (error) {
      console.error("Error cutting deck:", error);
      setIsCutting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
      <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full">
        <div className="border-b border-[#a07a4a] bg-black px-8 py-2 text-center rounded-t-md">
          <span className="text-[1.5rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
            Cut
          </span>
        </div>

        {/* Cut Options */}
            <div className="flex gap-1 py-5 px-2 bg-blackglass rounded-b-md justify-center">
              <button
                onClick={() => handleCut('top')}
                disabled={isCutting}
                className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  Top
                </span>
              </button>
              <button
                onClick={() => handleCut('middle')}
                disabled={isCutting}
                className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  Middle
                </span>
              </button>
              <button
                onClick={() => handleCut('bottom')}
                disabled={isCutting}
                className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  Bottom
                </span>
              </button>
            </div>

            <div className="mt-4 flex justify-center">
              <button
                onClick={() => handleCut(null)}
                disabled={isCutting}
                className="w-full rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  No Cut
                </span>
              </button>
            </div>

        {/* Status Message */}
        {isCutting && (
          <div className="mt-4 text-center">
            <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
              Processing...
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
