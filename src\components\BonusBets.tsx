import React from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

const BonusBets: React.FC = () => {
  return (
    <div className="absolute right-16 bottom-0 flex h-[190px] w-[141px]">
      <div className="flex h-full w-[35px] items-center justify-center rounded-l-lg bg-black">
        <span className="whitespace-nowrap text-xs font-semibold text-[#FFD972] transform -rotate-90">
          Bonus Bets
        </span>
      </div>
      <div className="relative flex-1 rounded-r-lg bg-gradient-to-br from-gray-400/50 via-black/50 to-gray-400/50">
        <div className="absolute left-1/2 top-1/2 w-[90px] -translate-x-1/2 -translate-y-1/2 space-y-3">
          {["Thunee", "Khanak", "Double"].map((text, index) => (
            <div
              key={index}
              className="relative flex h-[30px] w-full items-center justify-between rounded-lg bg-[#373737] px-2"
            >
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[#E1B70F] opacity-50"></div>
              <Label className="relative z-10 text-[11px] font-semibold text-white">
                {text}
              </Label>
              <Switch
                className="relative z-10 h-3.5 w-8 rounded-full bg-zinc-700 data-[state=checked]:bg-[#E1B70F] [&>span]:h-2.5 [&>span]:w-2.5 [&>span]:translate-x-[2px] data-[state=checked]:[&>span]:translate-x-[18px]"
                id={`${text.toLowerCase()}-switch`}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BonusBets;
