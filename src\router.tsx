import { createBrowserRouter, Navigate, Outlet } from "react-router-dom";
import App from "./App";
import Lobby from "./pages/Lobby";
import GameLobby from "./pages/GameLobby";
import SpectateGames from "./pages/SpectateGames";
import Competitions from "./pages/Competitions";
import CompetitionLeaderboard from "./pages/CompetitionLeaderboard";
import RulesTest from "./pages/RulesTest";
import DealerTest from "./pages/DealerTest";
import TrumpDisplayTest from "./pages/TrumpDisplayTest";
import TimeSettings from "./pages/TimeSettings";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ApiTest from "./pages/ApiTest";
import { useAuthStore } from "./store/authStore";

// Protected route wrapper
const ProtectedRoute = () => {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <Outlet />;
};

const router = createBrowserRouter([
  // Public routes
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "/register",
    element: <Register />,
  },

  // Protected routes
  {
    element: <ProtectedRoute />,
    children: [
      {
        path: "/",
        element: <Lobby />,
      },
      {
        path: "/lobby",
        element: <GameLobby />,
      },
      {
        path: "/game",
        element: <App />,
      },
      {
        path: "/spectate",
        element: <SpectateGames />,
      },
      {
        path: "/tutorial",
        element: <div className="h-screen flex items-center justify-center text-white">Tutorial Page (Coming Soon)</div>,
      },
      {
        path: "/settings",
        element: <TimeSettings />,
      },
      {
        path: "/competitions",
        element: <Competitions />,
      },
      {
        path: "/competitions/:id",
        element: <CompetitionLeaderboard />,
      },
    ],
  },

  // Test routes (can be protected later if needed)
  {
    path: "/rules-test",
    element: <RulesTest />,
  },
  {
    path: "/dealer-test",
    element: <DealerTest />,
  },
  {
    path: "/trump-test",
    element: <TrumpDisplayTest />,
  },
  {
    path: "/api-test",
    element: <ApiTest />,
  },
]);

export default router;
