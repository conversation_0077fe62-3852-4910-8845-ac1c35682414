@echo off
echo Starting Thunee ASP.NET Core API in Development Mode...
echo.

REM Navigate to the ThuneeAPI directory
cd /d "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"

REM Set environment to Development
set ASPNETCORE_ENVIRONMENT=Development

echo Environment: %ASPNETCORE_ENVIRONMENT%
echo Current directory: %CD%
echo.

echo Building project...
dotnet build
if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Starting API on http://localhost:5000...
echo.
echo Available URLs:
echo   Main API: http://localhost:5000
echo   Test Client: http://localhost:5000/test-client.html
echo   Config: http://localhost:5000/config.js
echo.
echo Press Ctrl+C to stop the server
echo.

REM Run with explicit development URL
dotnet run --urls "http://localhost:5000"
