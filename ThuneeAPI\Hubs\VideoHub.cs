using Microsoft.AspNetCore.SignalR;
using ThuneeAPI.Models;
using ThuneeAPI.Services;

namespace ThuneeAPI.Hubs
{
    public class VideoHub : Hub
    {
        private readonly IVideoService _videoService;

        public VideoHub(IVideoService videoService)
        {
            _videoService = videoService;
        }

        public override async Task OnConnectedAsync()
        {
            Console.WriteLine($"Video server: User connected: {Context.ConnectionId}");
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            Console.WriteLine($"Video server: User disconnected: {Context.ConnectionId}");
            
            // Remove user from all video rooms
            await _videoService.RemoveFromAllRoomsAsync(Context.ConnectionId);
            
            await base.OnDisconnectedAsync(exception);
        }

        public async Task<ApiResponse<object>> Register(string name)
        {
            try
            {
                await _videoService.RegisterUserAsync(Context.ConnectionId, name);
                Console.WriteLine($"Video server: User {Context.ConnectionId} registered as {name}");
                
                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<VideoRoomUsersResponse>> JoinRoom(VideoJoinRoomRequest request)
        {
            try
            {
                Console.WriteLine($"Video server: User {Context.ConnectionId} ({request.Name}) joining room {request.RoomId}");

                // Register user name
                await _videoService.RegisterUserAsync(Context.ConnectionId, request.Name);

                // Add user to room
                await _videoService.AddToRoomAsync(Context.ConnectionId, request.RoomId);

                // Join SignalR group
                await Groups.AddToGroupAsync(Context.ConnectionId, request.RoomId);

                // Get other participants
                var otherParticipants = await _videoService.GetOtherParticipantsAsync(request.RoomId, Context.ConnectionId);

                // Notify other participants about the new user
                await Clients.Group(request.RoomId).SendAsync("user_joined", new VideoUserJoinedResponse
                {
                    Id = Context.ConnectionId,
                    Name = request.Name
                });

                return ApiResponse<VideoRoomUsersResponse>.SuccessResult(new VideoRoomUsersResponse
                {
                    Users = otherParticipants
                });
            }
            catch (Exception ex)
            {
                return ApiResponse<VideoRoomUsersResponse>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> LeaveRoom(VideoLeaveRoomRequest request)
        {
            try
            {
                Console.WriteLine($"Video server: User {Context.ConnectionId} leaving room {request.RoomId}");

                // Remove user from room
                await _videoService.RemoveFromRoomAsync(Context.ConnectionId, request.RoomId);

                // Leave SignalR group
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, request.RoomId);

                // Notify other participants
                await Clients.Group(request.RoomId).SendAsync("user_left", new VideoUserLeftResponse
                {
                    Id = Context.ConnectionId
                });

                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> Signal(VideoSignalRequest request)
        {
            try
            {
                Console.WriteLine($"Video server: Signal from {Context.ConnectionId} to {request.To} in room {request.RoomId}");

                // Validate that both users are in the same room
                var isValidSignal = await _videoService.ValidateSignalAsync(Context.ConnectionId, request.To, request.RoomId);
                
                if (!isValidSignal.IsValid)
                {
                    // Notify sender about the error
                    await Clients.Caller.SendAsync("signal_error", new VideoSignalErrorResponse
                    {
                        To = request.To,
                        Error = isValidSignal.Error ?? "Invalid signal"
                    });
                    
                    return ApiResponse<object>.ErrorResult(isValidSignal.Error ?? "Invalid signal");
                }

                // Get sender name
                var senderName = await _videoService.GetUserNameAsync(Context.ConnectionId);

                // Forward the signal to the recipient
                await Clients.Client(request.To).SendAsync("signal", new VideoSignalResponse
                {
                    From = Context.ConnectionId,
                    Signal = request.Signal,
                    Name = senderName
                });

                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }
    }
}
