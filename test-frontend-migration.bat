@echo off
echo Testing Thunee Frontend Migration...
echo.

echo Step 1: Checking if ASP.NET Core API is running...
curl -s -o nul -w "API Status: %%{http_code}" http://localhost:5000
echo.

echo.
echo Step 2: Checking environment files...
if exist .env (
    echo ✓ .env file exists
) else (
    echo ✗ .env file missing
)

if exist .env.development (
    echo ✓ .env.development file exists
) else (
    echo ✗ .env.development file missing
)

if exist .env.production (
    echo ✓ .env.production file exists
) else (
    echo ✗ .env.production file missing
)

echo.
echo Step 3: Checking new service files...
if exist "src\services\signalRService.ts" (
    echo ✓ SignalR service exists
) else (
    echo ✗ SignalR service missing
)

if exist "src\services\gameService.ts" (
    echo ✓ Game service adapter exists
) else (
    echo ✗ Game service adapter missing
)

if exist "src\components\EnvironmentIndicator.tsx" (
    echo ✓ Environment indicator component exists
) else (
    echo ✗ Environment indicator component missing
)

echo.
echo Step 4: Checking package.json...
findstr /C:"@microsoft/signalr" package.json >nul
if %errorlevel%==0 (
    echo ✓ SignalR client package installed
) else (
    echo ✗ SignalR client package missing
)

findstr /C:"socket.io-client" package.json >nul
if %errorlevel%==0 (
    echo ⚠ Socket.IO client still installed (this is OK for gradual migration)
) else (
    echo ✓ Socket.IO client removed
)

echo.
echo Step 5: Testing development build...
echo Running npm run dev in test mode...
echo (This will start the development server - press Ctrl+C to stop)
echo.
echo After the server starts:
echo 1. Open http://localhost:3000 in your browser
echo 2. Look for the Environment Indicator in the top-right corner
echo 3. It should show:
echo    - Environment: DEVELOPMENT (green badge)
echo    - Connection: Connected (if API is running)
echo    - Service: SignalR
echo 4. Click the Settings button to see detailed configuration
echo.
echo Press any key to start the development server...
pause >nul

npm run dev
