{"Version": 1, "Hash": "fkXjpLPrFvkqhqCqGsz6EYWsdL6q8D5RIGld98Kbt1w=", "Source": "ThuneeAPI", "BasePath": "_content/ThuneeAPI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ThuneeAPI\\wwwroot", "Source": "ThuneeAPI", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\wwwroot\\", "BasePath": "_content/ThuneeAPI", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\wwwroot\\config.js", "SourceId": "ThuneeAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\wwwroot\\", "BasePath": "_content/ThuneeAPI", "RelativePath": "config.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\config.js"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\wwwroot\\test-client.html", "SourceId": "ThuneeAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\Thunee-FE\\ThuneeAPI\\wwwroot\\", "BasePath": "_content/ThuneeAPI", "RelativePath": "test-client.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-client.html"}]}