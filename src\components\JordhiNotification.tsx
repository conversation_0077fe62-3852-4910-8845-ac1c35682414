"use client";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";

type JordhiNotificationProps = {
  playerName: string;
  playerTeam: 1 | 2;
  value: number;
  autoHideDuration?: number;
};

export default function JordhiNotification({
  playerName,
  playerTeam,
  value,
  autoHideDuration = 5000,
}: JordhiNotificationProps) {
  const [visible, setVisible] = useState(true);
  const { teamNames } = useGameStore();

  // Auto-hide after duration
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
    }, autoHideDuration);

    return () => clearTimeout(timer);
  }, [autoHideDuration]);

  const teamName = teamNames[playerTeam] || `Team ${playerTeam}`;

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50"
        >
          <div className="px-6 py-4 rounded-lg shadow-lg border-2 bg-black/90 border-[#E1C760]">
            <div className="flex flex-col items-center">
              <h3 className="text-xl font-bold text-[#E1C760] mb-2">
                {value} JORDHI CALL
              </h3>
              <p className="text-white text-center">
                <span className="font-bold">{playerName}</span> ({teamName}) called a Jordhi
              </p>
              <p className="text-[#E1C760] text-sm mt-2">
                {playerTeam === useGameStore.getState().biddingTeam
                  ? `Opponent team's target score increased by ${value} points.`
                  : `${teamName}'s target score reduced by ${value} points.`}
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
