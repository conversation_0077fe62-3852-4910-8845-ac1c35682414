using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ThuneeAPI.Models
{
    public class User
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        public string PasswordHash { get; set; } = string.Empty;
        
        public bool IsVerified { get; set; } = false;
        
        public string? OtpCode { get; set; }
        
        public DateTime? OtpExpiry { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? LastLoginAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<PlayerGameStat> GameStats { get; set; } = new List<PlayerGameStat>();
        public virtual ICollection<PlayerCompetitionStat> CompetitionStats { get; set; } = new List<PlayerCompetitionStat>();
    }

    public class Competition
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        [Required]
        [StringLength(50)]
        public string FirstPrize { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string SecondPrize { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string ThirdPrize { get; set; } = string.Empty;
        
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "upcoming"; // upcoming, active, completed
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public virtual ICollection<Game> Games { get; set; } = new List<Game>();
        public virtual ICollection<PlayerCompetitionStat> PlayerStats { get; set; } = new List<PlayerCompetitionStat>();
    }

    public class Game
    {
        [Key]
        public int Id { get; set; }
        
        public int CompetitionId { get; set; }
        
        [Required]
        [StringLength(20)]
        public string LobbyCode { get; set; } = string.Empty;
        
        public DateTime StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        public int WinningTeam { get; set; } // 1 or 2
        
        public int Team1Balls { get; set; }
        
        public int Team2Balls { get; set; }
        
        public int TotalBalls { get; set; }
        
        public string? GameSettings { get; set; } // JSON string for game settings
        
        // Navigation properties
        [ForeignKey("CompetitionId")]
        public virtual Competition Competition { get; set; } = null!;
        
        public virtual ICollection<PlayerGameStat> PlayerStats { get; set; } = new List<PlayerGameStat>();
    }

    public class PlayerGameStat
    {
        [Key]
        public int Id { get; set; }
        
        public int GameId { get; set; }
        
        public int UserId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string PlayerName { get; set; } = string.Empty;
        
        public int Team { get; set; } // 1 or 2
        
        public int Position { get; set; } // 1, 2, 3, 4
        
        public bool IsWinner { get; set; }
        
        public int BallsWon { get; set; }
        
        public int TotalPoints { get; set; }
        
        public int JordhiCalls { get; set; }
        
        public int SuccessfulJordhiCalls { get; set; }
        
        public int KhanakCalls { get; set; }
        
        public int SuccessfulKhanakCalls { get; set; }
        
        public int ThuneeCalls { get; set; }
        
        public int SuccessfulThuneeCalls { get; set; }
        
        public int DoubleCalls { get; set; }
        
        public int SuccessfulDoubleCalls { get; set; }
        
        public int FourBallPenalties { get; set; }
        
        public int HandsWon { get; set; }
        
        public int TotalHands { get; set; }
        
        public string? BallResults { get; set; } // JSON string for detailed ball-by-ball results
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        [ForeignKey("GameId")]
        public virtual Game Game { get; set; } = null!;
        
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }

    public class PlayerCompetitionStat
    {
        [Key]
        public int Id { get; set; }
        
        public int CompetitionId { get; set; }
        
        public int UserId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string PlayerName { get; set; } = string.Empty;
        
        public int GamesPlayed { get; set; }
        
        public int GamesWon { get; set; }
        
        public int TotalBallsWon { get; set; }
        
        public int TotalPoints { get; set; }
        
        public decimal WinRate { get; set; }
        
        public int TotalScore { get; set; } // Calculated score for leaderboard
        
        public int Rank { get; set; }
        
        public int TotalJordhiCalls { get; set; }
        
        public int SuccessfulJordhiCalls { get; set; }
        
        public int TotalKhanakCalls { get; set; }
        
        public int SuccessfulKhanakCalls { get; set; }
        
        public int TotalThuneeCalls { get; set; }
        
        public int SuccessfulThuneeCalls { get; set; }
        
        public int TotalDoubleCalls { get; set; }
        
        public int SuccessfulDoubleCalls { get; set; }
        
        public int TotalFourBallPenalties { get; set; }
        
        public int TotalHandsWon { get; set; }
        
        public int TotalHands { get; set; }
        
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        [ForeignKey("CompetitionId")]
        public virtual Competition Competition { get; set; } = null!;
        
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
