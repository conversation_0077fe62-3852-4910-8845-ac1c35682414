// Configuration for T<PERSON>ee React Frontend
// This file helps the frontend determine the correct API endpoints

window.ThuneeConfig = {
  // Determine if we're in development or production
  isDevelopment: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
  
  // API Base URLs
  getApiBaseUrl: function() {
    if (this.isDevelopment) {
      // Development - connect to local ASP.NET Core server
      return 'http://localhost:5000';
    } else {
      // Production - use the same host as the frontend
      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      const port = '3001'; // ASP.NET Core API port on production
      return `${protocol}//${hostname}:${port}`;
    }
  },
  
  // SignalR Hub URLs
  getGameHubUrl: function() {
    return `${this.getApiBaseUrl()}/gameHub`;
  },
  
  getVideoHubUrl: function() {
    return `${this.getApiBaseUrl()}/videoHub`;
  },
  
  // WebSocket configuration
  getSocketConfig: function() {
    return {
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: false,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      maxReconnectionAttempts: 5,
      withCredentials: true
    };
  },
  
  // Environment info
  getEnvironmentInfo: function() {
    return {
      environment: this.isDevelopment ? 'development' : 'production',
      apiBaseUrl: this.getApiBaseUrl(),
      gameHubUrl: this.getGameHubUrl(),
      videoHubUrl: this.getVideoHubUrl(),
      hostname: window.location.hostname,
      port: window.location.port,
      protocol: window.location.protocol
    };
  }
};

// Log configuration on load
console.log('Thunee Configuration:', window.ThuneeConfig.getEnvironmentInfo());
