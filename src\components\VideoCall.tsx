import React, { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, <PERSON>, PhoneOff, Mic, MicOff, Camera, CameraOff } from "lucide-react";
import { useGameStore } from "../store/gameStore";
import { useLobbyStore } from "../store/lobbyStore";
import gameService from "../services/gameService";
import videoService from "../services/videoService";
import VideoStream from "./VideoStream";

export default function VideoCall() {
  const [isOpen, setIsOpen] = useState(false);
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [peers, setPeers] = useState<Map<string, {
    id: string,
    name: string,
    stream: MediaStream | null
  }>>(new Map());
  const [peerConnections, setPeerConnections] = useState<Map<string, RTCPeerConnection>>(new Map());
  const [error, setError] = useState<string | null>(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [useFallbackMode, setUseFallbackMode] = useState(false);

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const { players } = useGameStore();
  const { lobbyCode } = useLobbyStore();

  // Get player name by ID
  const getPlayerName = (playerId: string) => {
    const player = players.find((p) => p.id === playerId);
    return player ? player.name : "Unknown Player";
  };

  // Initialize video call when opened
  useEffect(() => {
    if (isOpen) {
      initializeVideoCall();
    }

    return () => {
      cleanupVideoCall();
    };
  }, [isOpen]);

  // Initialize video call
  const initializeVideoCall = async () => {
    try {
      // Check if browser supports getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Your browser doesn't support video calls. Please try a different browser.");
      }

      // Connect to video service first (this doesn't require camera access)
      const currentPlayerName = getPlayerName(gameService.getSocketId() || "You");
      await videoService.connect(currentPlayerName);

      console.log("Connected to video service, attempting to access camera...");

      try {
        // Try to get local media with more specific constraints
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: "user"
          },
          audio: true
        });

        console.log("Camera access successful:", stream.getVideoTracks().length > 0, "video tracks,",
                    stream.getAudioTracks().length > 0, "audio tracks");

        setLocalStream(stream);
        setIsVideoEnabled(true);
        setIsAudioEnabled(true);

        if (localVideoRef.current) {
          localVideoRef.current.srcObject = stream;
        }
      } catch (mediaError) {
        console.error("Media access error:", mediaError);

        // Try audio only as fallback
        try {
          console.log("Trying audio-only fallback...");
          const audioStream = await navigator.mediaDevices.getUserMedia({
            video: false,
            audio: true
          });

          setLocalStream(audioStream);
          setIsVideoEnabled(false);
          setError(`Camera access failed: ${mediaError.message}. Using audio only.`);
        } catch (audioError) {
          console.error("Audio-only fallback failed:", audioError);
          setError(`Cannot access camera or microphone. ${audioError.message}`);
          // Continue without local media
        }
      }

      // Join room using lobby code as room ID
      if (lobbyCode) {
        await videoService.joinRoom(lobbyCode);
        console.log("Joined video room:", lobbyCode);

        // Set up event listeners
        setupVideoEventListeners();
      } else {
        setError("No lobby code found. Cannot join video call.");
        setUseFallbackMode(true);
      }
    } catch (err) {
      console.error("Failed to initialize video call:", err);
      setError(err instanceof Error ? err.message : "Failed to initialize video call");
      setUseFallbackMode(true);
    }
  };

  // Set up video event listeners
  const setupVideoEventListeners = () => {
    // We can still set up listeners even without a local stream
    // This allows for receiving remote streams in audio-only mode
    if (!localStream) {
      console.warn('Setting up video event listeners without local stream (audio-only mode)');
    }

    // When we receive the list of users already in the room
    videoService.on('room_users', ({ users }) => {
      console.log('Received room users:', users);

      // Create a new map with existing users
      const newPeers = new Map();

      // Initialize peer connections for each existing user
      users.forEach(user => {
        newPeers.set(user.id, {
          id: user.id,
          name: user.name,
          stream: null
        });

        // Create peer connection to this user
        initializePeerConnection(user.id, user.name, true);
      });

      setPeers(newPeers);
    });

    // When a new user joins the room
    videoService.on('user_joined', ({ id, name }) => {
      console.log('User joined:', id, name);

      setPeers(prev => {
        const newPeers = new Map(prev);
        newPeers.set(id, {
          id,
          name,
          stream: null
        });
        return newPeers;
      });

      // Wait for them to initialize their connection to us
      // We don't initiate the connection here
    });

    // When a user leaves the room
    videoService.on('user_left', ({ id, name }) => {
      console.log(`User left: ${name} (${id})`);

      // Close peer connection if it exists
      const peerConnection = peerConnections.get(id);
      if (peerConnection) {
        console.log(`Closing connection to ${id}`);
        peerConnection.close();

        setPeerConnections(prev => {
          const newConnections = new Map(prev);
          newConnections.delete(id);
          return newConnections;
        });
      }

      setPeers(prev => {
        const newPeers = new Map(prev);
        newPeers.delete(id);
        return newPeers;
      });
    });

    // Handle signal errors
    videoService.on('signal_error', ({ to, error }) => {
      console.error(`Signal error to ${to}: ${error}`);

      // If we get a "Recipient not found" error, remove the peer
      if (error === 'Recipient not found' || error === 'Recipient not in room') {
        setPeers(prev => {
          const newPeers = new Map(prev);
          newPeers.delete(to);
          return newPeers;
        });

        // Close peer connection if it exists
        const peerConnection = peerConnections.get(to);
        if (peerConnection) {
          peerConnection.close();

          setPeerConnections(prev => {
            const newConnections = new Map(prev);
            newConnections.delete(to);
            return newConnections;
          });
        }
      }
    });

    // Handle disconnection from video server
    videoService.on('disconnect', ({ reason }) => {
      console.warn(`Disconnected from video server: ${reason}`);
      setError(`Disconnected from video server: ${reason}. Please try again.`);
    });

    // Handle WebRTC signaling
    videoService.on('signal', async ({ from, signal, name }) => {
      console.log('Received signal from:', from, name);

      try {
        // If this is a new offer, create a peer connection
        if (signal.type === 'offer') {
          await handlePeerOffer(from, name, signal);
        }
        // If this is an answer to our offer, handle it
        else if (signal.type === 'answer') {
          await handlePeerAnswer(from, signal);
        }
        // If this is an ICE candidate, add it
        else if (signal.candidate) {
          await handleIceCandidate(from, signal);
        }
      } catch (err) {
        console.error('Error handling signal:', err);
      }
    });
  };

  // Initialize a peer connection (as initiator)
  const initializePeerConnection = async (peerId: string, peerName: string, initiator: boolean) => {
    // We can still create a peer connection without a local stream
    // This allows for receiving remote streams in audio-only mode
    if (!localStream) {
      console.warn(`Creating peer connection to ${peerName} without local stream (receive-only mode)`);
    }

    try {
      console.log(`Initializing ${initiator ? 'initiator' : 'receiver'} peer connection to ${peerName} (${peerId})`);

      // Create a new RTCPeerConnection
      const peerConnection = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' },
        ]
      });

      // Add local tracks to the connection if we have a stream
      if (localStream) {
        localStream.getTracks().forEach(track => {
          peerConnection.addTrack(track, localStream);
        });
        console.log(`Added ${localStream.getTracks().length} local tracks to connection with ${peerName}`);
      } else {
        console.log(`No local tracks to add to connection with ${peerName} (receive-only mode)`);
      }

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          videoService.sendSignal(peerId, event.candidate);
        }
      };

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        console.log(`Connection state with ${peerName}: ${peerConnection.connectionState}`);
      };

      // Handle ICE connection state changes
      peerConnection.oniceconnectionstatechange = () => {
        console.log(`ICE connection state with ${peerName}: ${peerConnection.iceConnectionState}`);
      };

      // Handle remote tracks
      peerConnection.ontrack = (event) => {
        console.log(`Received track from ${peerName}:`, event.track.kind);

        // Create a new MediaStream with the received tracks
        const remoteStream = new MediaStream();
        event.streams[0].getTracks().forEach(track => {
          remoteStream.addTrack(track);
        });

        // Update the peer's stream in state
        setPeers(prev => {
          const newPeers = new Map(prev);
          const peer = newPeers.get(peerId);

          if (peer) {
            newPeers.set(peerId, {
              ...peer,
              stream: remoteStream
            });
          }

          return newPeers;
        });
      };

      // If we're the initiator, create and send an offer
      if (initiator) {
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);
        videoService.sendSignal(peerId, peerConnection.localDescription);
      }

      // Store the peer connection for later use
      setPeerConnections(prev => {
        const newConnections = new Map(prev);
        newConnections.set(peerId, peerConnection);
        return newConnections;
      });

    } catch (err) {
      console.error(`Error initializing peer connection to ${peerName}:`, err);
    }
  };

  // Handle a peer offer (answer an incoming call)
  const handlePeerOffer = async (peerId: string, peerName: string, offer: RTCSessionDescriptionInit) => {
    // We can still answer a call without a local stream (receive-only mode)
    if (!localStream) {
      console.warn(`Answering call from ${peerName} without local stream (receive-only mode)`);
    }

    try {
      // Get or create peer connection
      let peerConnection = peerConnections.get(peerId);

      if (!peerConnection) {
        // Initialize a new peer connection (not as initiator)
        await initializePeerConnection(peerId, peerName, false);
        peerConnection = peerConnections.get(peerId);

        if (!peerConnection) {
          throw new Error('Failed to create peer connection');
        }
      }

      // Set the remote description (their offer)
      await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));

      // Create and send an answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);
      videoService.sendSignal(peerId, peerConnection.localDescription);

    } catch (err) {
      console.error('Error handling peer offer:', err);
    }
  };

  // Handle a peer answer (they've answered our call)
  const handlePeerAnswer = async (peerId: string, answer: RTCSessionDescriptionInit) => {
    try {
      const peerConnection = peerConnections.get(peerId);

      if (peerConnection) {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
      } else {
        console.error('No peer connection found for:', peerId);
      }
    } catch (err) {
      console.error('Error handling peer answer:', err);
    }
  };

  // Handle an ICE candidate
  const handleIceCandidate = async (peerId: string, candidate: RTCIceCandidateInit) => {
    try {
      const peerConnection = peerConnections.get(peerId);

      if (peerConnection) {
        await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      } else {
        console.error('No peer connection found for:', peerId);
      }
    } catch (err) {
      console.error('Error handling ICE candidate:', err);
    }
  };

  // Clean up video call
  const cleanupVideoCall = () => {
    // Stop local stream
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }

    // Close all peer connections
    peerConnections.forEach((connection, peerId) => {
      console.log(`Closing connection to ${peerId}`);
      connection.close();
    });
    setPeerConnections(new Map());

    // Disconnect from video service
    videoService.disconnect();

    // Clear peers
    setPeers(new Map());

    // Reset state
    setError(null);
    setUseFallbackMode(false);
  };

  // Handle opening the video call
  const handleOpen = () => {
    setIsOpen(true);
  };

  // Handle closing the video call
  const handleClose = () => {
    setIsOpen(false);
    cleanupVideoCall();
  };

  // Toggle audio
  const toggleAudio = () => {
    if (localStream && localStream.getAudioTracks().length > 0) {
      localStream.getAudioTracks().forEach(track => {
        track.enabled = !isAudioEnabled;
      });
      setIsAudioEnabled(!isAudioEnabled);
      console.log(`Microphone ${!isAudioEnabled ? 'unmuted' : 'muted'}`);
    } else {
      console.warn('Cannot toggle audio: No audio tracks available');
    }
  };

  // Toggle video
  const toggleVideo = () => {
    if (localStream && localStream.getVideoTracks().length > 0) {
      localStream.getVideoTracks().forEach(track => {
        track.enabled = !isVideoEnabled;
      });
      setIsVideoEnabled(!isVideoEnabled);
      console.log(`Camera ${!isVideoEnabled ? 'enabled' : 'disabled'}`);
    } else {
      console.warn('Cannot toggle video: No video tracks available');
    }
  };

  // Convert peers Map to Array for rendering
  const peersArray = Array.from(peers.values());

  return (
    <>
      {/* Video call button in the UI */}
      {/* <button
        onClick={handleOpen}
        className="fixed bottom-20 right-4 z-40 p-3 rounded-full bg-[#edcf5d] shadow-lg"
      >
        <Video className="text-black" size={24} />
      </button> */}

      {/* Video call modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 flex flex-col"
          >
            {/* Header */}
            <div className="p-4 flex justify-between items-center border-b border-neutral-800">
              <h2 className="text-[#edcf5d] font-semibold">Video Call</h2>
              <button
                onClick={handleClose}
                className="text-white/70 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            {/* Error message */}
            {error && (
              <div className="p-4 bg-red-900/50 text-white m-4 rounded-lg">
                <p>{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="mt-2 text-sm underline"
                >
                  Dismiss
                </button>
              </div>
            )}

            {/* Video grid */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
                {/* Fallback mode message */}
                {useFallbackMode && (
                  <div className="col-span-full bg-yellow-900/50 text-white p-4 rounded-lg mb-4">
                    <p className="font-medium">Limited Video Call Mode</p>
                    <p className="text-sm mt-1">
                      {error || "Camera access issue detected. You may still communicate with audio."}
                    </p>
                    <button
                      onClick={() => initializeVideoCall()}
                      className="mt-2 bg-[#edcf5d] text-black px-3 py-1 rounded text-sm"
                    >
                      Try Again
                    </button>
                  </div>
                )}

                {/* Local video */}
                <div className="aspect-video">
                  <VideoStream
                    stream={localStream}
                    name={getPlayerName(gameService.getSocketId() || "You")}
                    isLocal={true}
                    isAudioEnabled={isAudioEnabled}
                    isVideoEnabled={isVideoEnabled}
                  />
                </div>

                {/* Remote videos */}
                {!useFallbackMode && peersArray.map((peer) => (
                  <div key={peer.id} className="aspect-video">
                    {peer.stream ? (
                      <VideoStream
                        stream={peer.stream}
                        name={peer.name}
                        isAudioEnabled={true} // We don't track remote audio state yet
                        isVideoEnabled={true} // We don't track remote video state yet
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-neutral-800 rounded-lg">
                        <p className="text-neutral-500">Connecting to {peer.name}...</p>
                      </div>
                    )}
                  </div>
                ))}

                {/* Placeholder for empty slots */}
                {!useFallbackMode && [...Array(Math.max(0, 3 - peersArray.length))].map(
                  (_, index) => (
                    <div
                      key={`placeholder-${index}`}
                      className="aspect-video bg-neutral-800 rounded-lg flex items-center justify-center"
                    >
                      <p className="text-neutral-500">Waiting for player...</p>
                    </div>
                  )
                )}

                {/* Placeholder message for fallback mode */}
                {useFallbackMode && (
                  <div className="aspect-video bg-neutral-800 rounded-lg flex items-center justify-center">
                    <p className="text-neutral-500">Other players' videos not available in limited mode</p>
                  </div>
                )}
              </div>
            </div>

            {/* Controls */}
            <div className="p-4 border-t border-neutral-800 flex flex-col items-center">
              <div className="flex justify-center space-x-4 mb-2">
                <button
                  onClick={toggleAudio}
                  className={`p-3 rounded-full ${isAudioEnabled ? 'bg-neutral-700' : 'bg-red-600'}`}
                  disabled={!localStream || localStream.getAudioTracks().length === 0}
                  title={localStream && localStream.getAudioTracks().length > 0 ?
                    (isAudioEnabled ? "Mute microphone" : "Unmute microphone") :
                    "No microphone available"}
                >
                  {isAudioEnabled ? (
                    <Mic className="text-white" size={20} />
                  ) : (
                    <MicOff className="text-white" size={20} />
                  )}
                </button>

                <button
                  onClick={toggleVideo}
                  className={`p-3 rounded-full ${isVideoEnabled ? 'bg-neutral-700' : 'bg-red-600'}`}
                  disabled={!localStream || localStream.getVideoTracks().length === 0}
                  title={localStream && localStream.getVideoTracks().length > 0 ?
                    (isVideoEnabled ? "Turn off camera" : "Turn on camera") :
                    "No camera available"}
                >
                  {isVideoEnabled ? (
                    <Camera className="text-white" size={20} />
                  ) : (
                    <CameraOff className="text-white" size={20} />
                  )}
                </button>

                <button
                  onClick={handleClose}
                  className="p-3 rounded-full bg-red-600"
                  title="End call"
                >
                  <PhoneOff className="text-white" size={20} />
                </button>
              </div>

              {/* Debug button */}
              <button
                onClick={() => {
                  const debugInfo = {
                    localStream: localStream ? {
                      videoTracks: localStream.getVideoTracks().length,
                      audioTracks: localStream.getAudioTracks().length,
                      videoEnabled: isVideoEnabled,
                      audioEnabled: isAudioEnabled,
                      videoTrackSettings: localStream.getVideoTracks().length > 0 ?
                        localStream.getVideoTracks()[0].getSettings() : null
                    } : null,
                    peerConnections: Array.from(peerConnections.entries()).map(([id, pc]) => ({
                      id,
                      connectionState: pc.connectionState,
                      iceConnectionState: pc.iceConnectionState,
                      signalingState: pc.signalingState
                    })),
                    peers: Array.from(peers.entries()).map(([id, peer]) => ({
                      id,
                      name: peer.name,
                      hasStream: !!peer.stream,
                      videoTracks: peer.stream ? peer.stream.getVideoTracks().length : 0,
                      audioTracks: peer.stream ? peer.stream.getAudioTracks().length : 0
                    }))
                  };
                  console.log('Video call debug info:', debugInfo);
                  alert('Debug info logged to console. Press F12 to view.');
                }}
                className="text-xs text-neutral-500 underline mt-2"
              >
                Debug Info
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
