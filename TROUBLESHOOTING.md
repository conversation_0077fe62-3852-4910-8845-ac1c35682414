# Thunee ASP.NET Core API Troubleshooting Guide

## ✅ **SOLUTION FOUND!**

The issue was that the API was trying to bind to port 3001 (production configuration) instead of port 5000 (development configuration).

### 🚀 **How to Run the API Successfully:**

**Option 1: Use the provided script (Recommended)**
```bash
start-dev-api.bat
```

**Option 2: Manual PowerShell commands**
```powershell
cd "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
$env:ASPNETCORE_ENVIRONMENT = "Development"
dotnet run --urls "http://localhost:5000"
```

**Option 3: Manual Command Prompt**
```cmd
cd "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
set ASPNETCORE_ENVIRONMENT=Development
dotnet run --urls "http://localhost:5000"
```

### 🎯 **Verification:**

After running the API, you should be able to access:
- **Main API:** http://localhost:5000
- **Test Client:** http://localhost:5000/test-client.html
- **Config File:** http://localhost:5000/config.js

### 🔧 **What Was Wrong:**

1. **Port Conflict:** The API was trying to use port 3001 (production) instead of 5000 (development)
2. **Environment Issue:** The production configuration was being loaded instead of development
3. **Node.js Conflict:** Your existing Node.js server might have been using port 3001

### 🛠️ **The Fix:**

1. **Explicit Environment Setting:** `ASPNETCORE_ENVIRONMENT=Development`
2. **Explicit URL Binding:** `--urls "http://localhost:5000"`
3. **Proper Directory Navigation:** Ensure you're in the ThuneeAPI directory

### 📋 **Common Issues and Solutions:**

#### Issue: "Failed to bind to address http://[::]:3001: address already in use"
**Solution:** Use the development script or set environment explicitly:
```bash
start-dev-api.bat
```

#### Issue: "localhost refused to connect" or "ERR_CONNECTION_REFUSED"
**Solutions:**
1. Make sure the API is actually running (check terminal output)
2. Verify you're using the correct URL: `http://localhost:5000`
3. Check Windows Firewall isn't blocking the port
4. Try running as Administrator if needed

#### Issue: API starts but shows no output
**This is normal!** ASP.NET Core apps often don't show startup messages. Just check if the browser can access the URLs.

#### Issue: Build errors
**Solution:** Make sure you're in the correct directory and run:
```bash
dotnet restore
dotnet build
```

### 🌐 **Environment Configuration:**

**Development (localhost):**
- Port: 5000
- Environment: Development
- CORS: Allows localhost:3000 (React app)

**Production (IIS):**
- Port: 3001
- Environment: Production
- CORS: Allows **************:96

### 🔄 **Next Steps:**

1. **Keep the API running** using `start-dev-api.bat`
2. **Test SignalR connection** at http://localhost:5000/test-client.html
3. **Update your React frontend** to use the SignalR service
4. **For production deployment** use `deploy-to-iis.ps1`

### 📞 **Still Having Issues?**

If you're still having problems:

1. **Check if Node.js server is running:** Stop any Node.js servers on port 3001
2. **Restart your computer:** Sometimes port conflicts persist
3. **Run as Administrator:** Some port binding issues require admin privileges
4. **Check antivirus/firewall:** Make sure they're not blocking the application

### 🎉 **Success Indicators:**

You'll know it's working when:
- ✅ Browser loads http://localhost:5000 without errors
- ✅ Test client page loads at http://localhost:5000/test-client.html
- ✅ You can click "Connect" in the test client and see "Connected to SignalR hub"
- ✅ Terminal shows the API is running (even if no output is visible)

The API is now ready to replace your Node.js servers!
