using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using ThuneeAPI.Data;
using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class CompetitionService : ICompetitionService
    {
        private readonly ThuneeDbContext _context;
        private readonly ILogger<CompetitionService> _logger;

        public CompetitionService(ThuneeDbContext context, ILogger<CompetitionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<CompetitionDto>> GetCompetitionsAsync()
        {
            var competitions = await _context.Competitions
                .Select(c => new CompetitionDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    StartDate = c.StartDate,
                    EndDate = c.EndDate,
                    Prizes = new PrizesDto
                    {
                        First = c.FirstPrize,
                        Second = c.SecondPrize,
                        Third = c.ThirdPrize
                    },
                    Status = c.Status,
                    TotalPlayers = c.PlayerStats.Count(),
                    TotalGames = c.Games.Count()
                })
                .ToListAsync();

            return competitions;
        }

        public async Task<CompetitionDto?> GetCompetitionAsync(int competitionId)
        {
            var competition = await _context.Competitions
                .Where(c => c.Id == competitionId)
                .Select(c => new CompetitionDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    StartDate = c.StartDate,
                    EndDate = c.EndDate,
                    Prizes = new PrizesDto
                    {
                        First = c.FirstPrize,
                        Second = c.SecondPrize,
                        Third = c.ThirdPrize
                    },
                    Status = c.Status,
                    TotalPlayers = c.PlayerStats.Count(),
                    TotalGames = c.Games.Count()
                })
                .FirstOrDefaultAsync();

            return competition;
        }

        public async Task<LeaderboardDto> GetCompetitionLeaderboardAsync(int competitionId, int page = 1, int pageSize = 10, string? sortBy = "score", string? sortDirection = "desc", int? minGames = null)
        {
            var query = _context.PlayerCompetitionStats
                .Where(pcs => pcs.CompetitionId == competitionId);

            // Apply filters
            if (minGames.HasValue)
            {
                query = query.Where(pcs => pcs.GamesPlayed >= minGames.Value);
            }

            // Apply sorting
            query = sortBy?.ToLower() switch
            {
                "winrate" => sortDirection?.ToLower() == "asc" 
                    ? query.OrderBy(pcs => pcs.WinRate) 
                    : query.OrderByDescending(pcs => pcs.WinRate),
                "gamesplayed" => sortDirection?.ToLower() == "asc" 
                    ? query.OrderBy(pcs => pcs.GamesPlayed) 
                    : query.OrderByDescending(pcs => pcs.GamesPlayed),
                _ => sortDirection?.ToLower() == "asc" 
                    ? query.OrderBy(pcs => pcs.TotalScore) 
                    : query.OrderByDescending(pcs => pcs.TotalScore)
            };

            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

            var entries = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select((pcs, index) => new LeaderboardEntryDto
                {
                    Id = pcs.Id,
                    UserId = pcs.UserId,
                    PlayerName = pcs.PlayerName,
                    Rank = ((page - 1) * pageSize) + index + 1,
                    TotalScore = pcs.TotalScore,
                    GamesPlayed = pcs.GamesPlayed,
                    GamesWon = pcs.GamesWon,
                    WinRate = pcs.WinRate,
                    TotalBallsWon = pcs.TotalBallsWon,
                    TotalPoints = pcs.TotalPoints
                })
                .ToListAsync();

            return new LeaderboardDto
            {
                Entries = entries,
                TotalItems = totalItems,
                CurrentPage = page,
                ItemsPerPage = pageSize,
                TotalPages = totalPages
            };
        }

        public async Task<bool> SaveGameResultAsync(GameResultRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Create game record
                var game = new Game
                {
                    CompetitionId = request.CompetitionId,
                    LobbyCode = request.LobbyCode,
                    StartedAt = DateTime.UtcNow.AddMinutes(-30), // Assume game took 30 minutes
                    CompletedAt = DateTime.UtcNow,
                    WinningTeam = request.WinningTeam,
                    Team1Balls = request.Team1Balls,
                    Team2Balls = request.Team2Balls,
                    TotalBalls = request.TotalBalls,
                    GameSettings = request.GameSettings
                };

                _context.Games.Add(game);
                await _context.SaveChangesAsync();

                // Create player game stats
                foreach (var playerResult in request.PlayerResults)
                {
                    // Try to find user by name (for now, since we don't have user mapping)
                    var user = await _context.Users
                        .FirstOrDefaultAsync(u => u.Username == playerResult.PlayerName);

                    var playerGameStat = new PlayerGameStat
                    {
                        GameId = game.Id,
                        UserId = user?.Id ?? 0, // 0 for guest players
                        PlayerName = playerResult.PlayerName,
                        Team = playerResult.Team,
                        Position = playerResult.Position,
                        IsWinner = playerResult.IsWinner,
                        BallsWon = playerResult.BallsWon,
                        TotalPoints = playerResult.TotalPoints,
                        JordhiCalls = playerResult.JordhiCalls,
                        SuccessfulJordhiCalls = playerResult.SuccessfulJordhiCalls,
                        KhanakCalls = playerResult.KhanakCalls,
                        SuccessfulKhanakCalls = playerResult.SuccessfulKhanakCalls,
                        ThuneeCalls = playerResult.ThuneeCalls,
                        SuccessfulThuneeCalls = playerResult.SuccessfulThuneeCalls,
                        DoubleCalls = playerResult.DoubleCalls,
                        SuccessfulDoubleCalls = playerResult.SuccessfulDoubleCalls,
                        FourBallPenalties = playerResult.FourBallPenalties,
                        HandsWon = playerResult.HandsWon,
                        TotalHands = playerResult.TotalHands,
                        BallResults = playerResult.BallResults != null ? JsonSerializer.Serialize(playerResult.BallResults) : null
                    };

                    _context.PlayerGameStats.Add(playerGameStat);
                }

                await _context.SaveChangesAsync();

                // Update competition stats for registered players
                await UpdatePlayerCompetitionStatsAsync(game.Id);

                await transaction.CommitAsync();

                _logger.LogInformation($"Game result saved successfully for lobby: {request.LobbyCode}");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error saving game result for lobby: {request.LobbyCode}");
                return false;
            }
        }

        public async Task<PlayerDetailsDto?> GetPlayerDetailsAsync(int userId)
        {
            var user = await _context.Users
                .Include(u => u.GameStats)
                .Include(u => u.CompetitionStats)
                .ThenInclude(cs => cs.Competition)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null) return null;

            var totalGames = user.GameStats.Count;
            var totalWins = user.GameStats.Count(gs => gs.IsWinner);
            var overallWinRate = totalGames > 0 ? (decimal)totalWins / totalGames * 100 : 0;

            var competitionStats = user.CompetitionStats.Select(cs => new CompetitionStatsDto
            {
                CompetitionId = cs.CompetitionId,
                CompetitionName = cs.Competition.Name,
                Rank = cs.Rank,
                GamesPlayed = cs.GamesPlayed,
                GamesWon = cs.GamesWon,
                WinRate = cs.WinRate,
                TotalScore = cs.TotalScore
            }).ToList();

            return new PlayerDetailsDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt,
                Stats = new PlayerStatsDto
                {
                    TotalGames = totalGames,
                    TotalWins = totalWins,
                    OverallWinRate = overallWinRate,
                    TotalBallsWon = user.GameStats.Sum(gs => gs.BallsWon),
                    TotalPoints = user.GameStats.Sum(gs => gs.TotalPoints),
                    TotalJordhiCalls = user.GameStats.Sum(gs => gs.JordhiCalls),
                    SuccessfulJordhiCalls = user.GameStats.Sum(gs => gs.SuccessfulJordhiCalls),
                    TotalKhanakCalls = user.GameStats.Sum(gs => gs.KhanakCalls),
                    SuccessfulKhanakCalls = user.GameStats.Sum(gs => gs.SuccessfulKhanakCalls),
                    TotalThuneeCalls = user.GameStats.Sum(gs => gs.ThuneeCalls),
                    SuccessfulThuneeCalls = user.GameStats.Sum(gs => gs.SuccessfulThuneeCalls),
                    CompetitionStats = competitionStats
                }
            };
        }

        public async Task<GameHistoryDto> GetPlayerGameHistoryAsync(int userId, int page = 1, int pageSize = 10)
        {
            var query = _context.PlayerGameStats
                .Where(pgs => pgs.UserId == userId)
                .Include(pgs => pgs.Game)
                .ThenInclude(g => g.Competition)
                .OrderByDescending(pgs => pgs.Game.CompletedAt);

            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

            var games = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(pgs => new GameHistoryEntryDto
                {
                    GameId = pgs.GameId,
                    CompetitionName = pgs.Game.Competition.Name,
                    PlayedAt = pgs.Game.CompletedAt ?? pgs.Game.StartedAt,
                    IsWinner = pgs.IsWinner,
                    Team = pgs.Team,
                    BallsWon = pgs.BallsWon,
                    TotalBalls = pgs.Game.TotalBalls,
                    TotalPoints = pgs.TotalPoints,
                    SpecialCalls = $"J:{pgs.SuccessfulJordhiCalls}/{pgs.JordhiCalls} K:{pgs.SuccessfulKhanakCalls}/{pgs.KhanakCalls} T:{pgs.SuccessfulThuneeCalls}/{pgs.ThuneeCalls}"
                })
                .ToListAsync();

            return new GameHistoryDto
            {
                Games = games,
                TotalItems = totalItems,
                CurrentPage = page,
                ItemsPerPage = pageSize,
                TotalPages = totalPages
            };
        }

        public async Task<List<UserDto>> GetPlayersAsync()
        {
            var users = await _context.Users
                .Where(u => u.IsVerified)
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    IsVerified = u.IsVerified,
                    CreatedAt = u.CreatedAt,
                    LastLoginAt = u.LastLoginAt
                })
                .ToListAsync();

            return users;
        }

        public async Task UpdatePlayerCompetitionStatsAsync(int gameId)
        {
            var game = await _context.Games
                .Include(g => g.PlayerStats)
                .FirstOrDefaultAsync(g => g.Id == gameId);

            if (game == null) return;

            foreach (var playerStat in game.PlayerStats.Where(ps => ps.UserId > 0))
            {
                var competitionStat = await _context.PlayerCompetitionStats
                    .FirstOrDefaultAsync(pcs => pcs.CompetitionId == game.CompetitionId && pcs.UserId == playerStat.UserId);

                if (competitionStat == null)
                {
                    competitionStat = new PlayerCompetitionStat
                    {
                        CompetitionId = game.CompetitionId,
                        UserId = playerStat.UserId,
                        PlayerName = playerStat.PlayerName
                    };
                    _context.PlayerCompetitionStats.Add(competitionStat);
                }

                // Update stats
                competitionStat.GamesPlayed++;
                if (playerStat.IsWinner) competitionStat.GamesWon++;
                
                competitionStat.TotalBallsWon += playerStat.BallsWon;
                competitionStat.TotalPoints += playerStat.TotalPoints;
                competitionStat.WinRate = competitionStat.GamesPlayed > 0 ? (decimal)competitionStat.GamesWon / competitionStat.GamesPlayed * 100 : 0;
                
                // Calculate score (wins * 100 + balls won * 10 + points)
                competitionStat.TotalScore = competitionStat.GamesWon * 100 + competitionStat.TotalBallsWon * 10 + competitionStat.TotalPoints;
                
                competitionStat.TotalJordhiCalls += playerStat.JordhiCalls;
                competitionStat.SuccessfulJordhiCalls += playerStat.SuccessfulJordhiCalls;
                competitionStat.TotalKhanakCalls += playerStat.KhanakCalls;
                competitionStat.SuccessfulKhanakCalls += playerStat.SuccessfulKhanakCalls;
                competitionStat.TotalThuneeCalls += playerStat.ThuneeCalls;
                competitionStat.SuccessfulThuneeCalls += playerStat.SuccessfulThuneeCalls;
                competitionStat.TotalDoubleCalls += playerStat.DoubleCalls;
                competitionStat.SuccessfulDoubleCalls += playerStat.SuccessfulDoubleCalls;
                competitionStat.TotalFourBallPenalties += playerStat.FourBallPenalties;
                competitionStat.TotalHandsWon += playerStat.HandsWon;
                competitionStat.TotalHands += playerStat.TotalHands;
                
                competitionStat.LastUpdated = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            // Update ranks
            await UpdateCompetitionRanksAsync(game.CompetitionId);
        }

        private async Task UpdateCompetitionRanksAsync(int competitionId)
        {
            var stats = await _context.PlayerCompetitionStats
                .Where(pcs => pcs.CompetitionId == competitionId)
                .OrderByDescending(pcs => pcs.TotalScore)
                .ToListAsync();

            for (int i = 0; i < stats.Count; i++)
            {
                stats[i].Rank = i + 1;
            }

            await _context.SaveChangesAsync();
        }
    }
}
