"use client";
import { useGameStore } from "@/store/gameStore";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { Clock } from "lucide-react";

export default function TurnIndicator() {
  const { players, currentTurn, isCurrentTurn, playTimeframe, turnTimeRemaining } = useGameStore();
  const [localTimeRemaining, setLocalTimeRemaining] = useState<number | null>(null);

  // Debug logging
  console.log('TurnIndicator rendering with:', {
    currentTurn,
    isCurrentTurn,
    playersCount: players.length,
    playTimeframe,
    turnTimeRemaining
  });

  // Find the current player
  const currentPlayer = players.find(player => player.id === currentTurn);

  // Set up local timer that syncs with server updates
  useEffect(() => {
    if (turnTimeRemaining !== null) {
      setLocalTimeRemaining(turnTimeRemaining);

      // Set up interval to count down locally between server updates
      const timer = setInterval(() => {
        setLocalTimeRemaining(prev => {
          if (prev === null || prev <= 0) {
            clearInterval(timer);
            return 0;
          }
          return prev - 0.1; // Update every 100ms for smoother countdown
        });
      }, 100);

      return () => clearInterval(timer);
    }
  }, [turnTimeRemaining]);

  if (!currentTurn || !currentPlayer) {
    console.log('No current turn or current player not found');
    return null;
  }

  // Calculate timer color based on remaining time
  const getTimerColor = () => {
    if (localTimeRemaining === null) return "text-[#E1C760]";
    if (localTimeRemaining <= 1) return "text-red-500";
    if (localTimeRemaining <= 2) return "text-orange-500";
    return "text-[#E1C760]";
  };

  return (
    <div className="fixed top-4 left-0 right-0 flex justify-center items-center z-40 pointer-events-none">
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-black/80 border-2 border-[#E1C760] rounded-lg px-4 py-2 text-center"
      >
        {isCurrentTurn ? (
          <div className="flex flex-col items-center">
            <div className="flex items-center gap-2">
              <span className="text-[#E1C760] text-lg font-bold">Your Turn</span>
              {localTimeRemaining !== null && (
                <span className={`${getTimerColor()} font-bold`}>
                  {localTimeRemaining.toFixed(1)}s
                </span>
              )}
            </div>
            <span className="text-white text-sm">Play a card</span>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="flex items-center gap-2">
              <span className="text-white text-lg">
                <span className="text-[#E1C760] font-bold">{currentPlayer.name}'s</span> Turn
              </span>
              {localTimeRemaining !== null && (
                <span className={`${getTimerColor()} font-bold`}>
                  {localTimeRemaining.toFixed(1)}s
                </span>
              )}
            </div>
            <span className="text-white/70 text-sm">Waiting for player to make a move</span>
          </div>
        )}
      </motion.div>
    </div>
  );
}
