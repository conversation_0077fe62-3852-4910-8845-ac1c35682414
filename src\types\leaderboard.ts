// Types for the leaderboard feature

export interface Competition {
  id: string;
  name: string;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  prizes: {
    first: string;
    second: string;
    third: string;
  };
  status: 'upcoming' | 'active' | 'completed';
  description?: string;
}

export interface LeaderboardEntry {
  id: string;
  playerId: string;
  playerName: string;
  score: number;
  rank: number;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
}

export interface LeaderboardFilter {
  timeFrame?: 'all' | 'week' | 'month';
  minGames?: number;
  sortBy?: 'score' | 'winRate' | 'gamesPlayed';
  sortDirection?: 'asc' | 'desc';
}

export interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
}
