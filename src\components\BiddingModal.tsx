"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import PlayerHand from "./PlayerHand";
import BidOptions from "./BidOptions";
import DirectBidding from "./DirectBidding";
import gameService from '../services/gameService';

interface BiddingModalProps {s
  isVisible: boolean;
  onBiddingComplete: () => void; 
}

export default function BiddingModal({
  isVisible,
  onBiddingComplete,
}: BiddingModalProps) {
  const { players, initialHand, canBid: storeCanBid } = useGameStore();
  const [currentBid, setCurrentBid] = useState<number>(0);
  const [highestBidder, setHighestBidder] = useState<string | null>(null);
  const [isPlacingBid, setIsPlacingBid] = useState(false);
  const [canBid, setCanBid] = useState(storeCanBid);
  const [timeLeft, setTimeLeft] = useState<number>(3);

  type BidHistoryEntry = {
    playerId: string;
    bid: number;
    team?: number;
    autoPass?: boolean;
    reason?: string;
  };

  const [bidHistory, setBidHistory] = useState<BidHistoryEntry[]>([]);
  const [currentBidderName, setCurrentBidderName] = useState<string | null>(null);
  const [currentBidderTeam, setCurrentBidderTeam] = useState<number | null>(null);

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      setCurrentBid(0);
      setHighestBidder(null);
      setIsPlacingBid(false);
      setTimeLeft(3);
      setBidHistory([]);
      setCanBid(storeCanBid);
    }
  }, [isVisible, storeCanBid]);

  // Keep canBid in sync with the store
  useEffect(() => {
    if (storeCanBid !== canBid) {
      setCanBid(storeCanBid);
      if (storeCanBid) {
        setIsPlacingBid(false);
      }
    }
  }, [storeCanBid, canBid]);

  // Countdown timer for bidding
  useEffect(() => {
    if (!isVisible || !canBid || timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          handlePass();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, canBid, timeLeft]);

  // Listen for bidding events from the server
  useEffect(() => {
    const handleBidPlaced = (data: {
      playerId: string;
      playerName: string;
      playerTeam?: number;
      bid: number;
    }) => {
      setCurrentBid(data.bid);
      setHighestBidder(data.playerId);

      setBidHistory(prev => [...prev, {
        playerId: data.playerId,
        bid: data.bid,
        team: data.playerTeam
      }]);

      setTimeLeft(3);
    };

    const handleBidPassed = (data: {
      playerId: string;
      playerName: string;
      autoPass?: boolean;
      reason?: string;
    }) => {
      setBidHistory(prev => [...prev, {
        playerId: data.playerId,
        bid: 0,
        autoPass: data.autoPass,
        reason: data.reason
      }]);
    };

    const handleBiddingStateUpdate = (data: {
      currentBidder: string;
      currentBidderName: string;
      currentBidderTeam: number;
      highestBid: number;
      highestBidder: string | null;
    }) => {
      setCurrentBidderName(data.currentBidderName);
      setCurrentBidderTeam(data.currentBidderTeam);
      setCurrentBid(data.highestBid);

      const currentPlayerId = gameService.getSocketId();
      if (data.currentBidder === currentPlayerId) {
        setCanBid(true);
        setTimeLeft(3);
        setIsPlacingBid(false);
      }
    };

    const handleBiddingComplete = () => {
      setTimeout(() => {
        onBiddingComplete();
      }, 2000);
    };

    const handleYourTurnToBid = () => {
      setCanBid(true);
      setTimeLeft(3);
      setIsPlacingBid(false);
    };

    gameService.on("bid_placed", handleBidPlaced);
    gameService.on("bid_passed", handleBidPassed);
    gameService.on("bidding_complete", handleBiddingComplete);
    gameService.on("your_turn_to_bid", handleYourTurnToBid);
    gameService.on("bidding_state_update", handleBiddingStateUpdate);

    return () => {
      gameService.off("bid_placed", handleBidPlaced);
      gameService.off("bid_passed", handleBidPassed);
      gameService.off("bidding_complete", handleBiddingComplete);
      gameService.off("your_turn_to_bid", handleYourTurnToBid);
      gameService.off("bidding_state_update", handleBiddingStateUpdate);
    };
  }, [onBiddingComplete, currentBid]);

  // Handle placing a bid
  const handlePlaceBid = async (bid: number) => {
    if (isPlacingBid || !canBid) {
      return;
    }

    try {
      setIsPlacingBid(true);

      await gameService.sendGameAction("place_bid", {
        bid: bid,
      });

      setCanBid(false);
      setIsPlacingBid(false);
    } catch (error) {
      console.error("Error placing bid:", error);
      setIsPlacingBid(false);
    }
  };

  // Handle passing on bidding
  const handlePass = async () => {
    if (isPlacingBid || !canBid) return;

    try {
      setIsPlacingBid(true);

      await gameService.sendGameAction("pass_bid", {});

      setCanBid(false);
      setIsPlacingBid(false);
    } catch (error) {
      console.error("Error passing bid:", error);
      setIsPlacingBid(false);
    }
  };

  // Find player name by ID
  const getPlayerName = (playerId: string | null) => {
    if (!playerId) return "No one";
    const player = players.find((p) => p.id === playerId);
    return player ? player.name : "Unknown player";
  };

  if (!isVisible) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/80"
      >
        <div className="flex w-full h-full max-h-screen overflow-hidden">
          {/* Left side - Cards and player info */}
          <div className="w-1/2 h-full flex flex-col">
            {/* Player's hand */}
            <div className="p-2 h-1/2">
              <PlayerHand cards={initialHand} />
            </div>

            {/* Current bid info and history */}
            <div className="bg-gray-900 border border-[#E1C760]/50 rounded-lg p-2 m-2 flex-1 overflow-auto">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-[#E1C760] text-sm font-bold">Current Bid</h3>
                {canBid && (
                  <span className="bg-black/70 text-[#E1C760] px-2 py-1 rounded-md text-xs">
                    {timeLeft}s
                  </span>
                )}
              </div>

              <div className="mb-2">
                <p className="text-white text-sm">
                  Highest: <span className="text-[#E1C760] font-bold">{currentBid > 0 ? currentBid : "No bids"}</span>
                  {highestBidder && (
                    <span className="text-xs ml-1">by {getPlayerName(highestBidder)}</span>
                  )}
                </p>

                {currentBidderName && (
                  <p className="text-white text-xs">
                    Current bidder: <span className="text-[#E1C760]">{currentBidderName}</span>
                    {currentBidderTeam && <span> (Team {currentBidderTeam})</span>}
                  </p>
                )}
              </div>

              {/* Bid history */}
              {bidHistory.length > 0 && (
                <div className="mt-2">
                  <h4 className="text-[#E1C760] text-xs font-semibold">History</h4>
                  <div className="bg-black/30 rounded p-1 max-h-16 overflow-y-auto">
                    {bidHistory.map((entry, index) => (
                      <div key={index} className="text-xs mb-0.5 text-white">
                        <span className="text-[#E1C760]">{getPlayerName(entry.playerId).split(' ')[0]}</span>
                        {entry.team && <span className="text-gray-400 text-xs"> (T{entry.team})</span>}:{" "}
                        {entry.bid === 0 ? (
                          <span>Pass{entry.autoPass ? " (auto)" : ""}</span>
                        ) : (
                          <span className="font-bold">{entry.bid}</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right side - Bidding UI */}
          <div className="w-1/2 h-full flex flex-col">
            <div className="bg-gray-900 border-2 border-[#E1C760] rounded-lg h-full m-2 flex flex-col overflow-hidden">
              {/* Header */}
              <div className="bg-[#E1C760] px-3 py-2 flex justify-between items-center">
                <h2 className="text-black font-bold text-base">Bidding Phase</h2>
                {canBid && (
                  <span className="text-black text-xs font-medium bg-black/20 px-2 py-1 rounded">
                    Your Turn
                  </span>
                )}
              </div>

              {/* Content */}
              <div className="p-2 text-white flex-1 overflow-y-auto">
                {canBid ? (
                  <div className="flex flex-col h-full">
                    <h3 className="text-[#E1C760] text-sm font-bold mb-2 text-center">YOUR TURN</h3>

                    {/* Direct bidding interface */}
                    <div className="flex-grow">
                      <DirectBidding
                        currentBid={currentBid}
                        onPlaceBid={handlePlaceBid}
                        isOpposingTeam={players.find(p => p.id === gameService.getSocketId())?.team !==
                          players.find(p => p.isDealer)?.team}
                        isPlacingBid={isPlacingBid}
                      />
                    </div>

                    {/* Action buttons */}
                    <div className="flex justify-center mt-2">
                      <motion.button
                        whileTap={{ scale: 0.95 }}
                        onClick={handlePass}
                        disabled={isPlacingBid}
                        className="w-full py-2 rounded text-sm font-bold bg-red-700 text-white disabled:opacity-50"
                      >
                        Pass
                      </motion.button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col h-full justify-between">
                    <div className="text-center py-2 flex-grow flex flex-col justify-center">
                      <p className="text-sm mb-2">Waiting for players...</p>
                      <div className="w-8 h-8 border-3 border-[#E1C760] border-t-transparent rounded-full animate-spin mx-auto"></div>
                    </div>

                    {/* Mini bidding guide */}
                    <div className="bg-black/30 p-2 rounded text-xs mt-2">
                      <p className="text-[#E1C760] font-semibold mb-1">Quick Guide:</p>
                      <p className="mb-0.5">• Click +10 to increase bid by 10</p>
                      <p className="mb-0.5">• At 100, opposing team can bid +4</p>
                      <p className="mb-0.5">• Click Pass to skip bidding</p>
                      <p>• Winners set trump, losers need 105-bid points</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}