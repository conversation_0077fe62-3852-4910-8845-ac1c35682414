"use client";

import type React from "react";
import { useState } from "react";
import { Home, MessageCircle, Video, MoreHorizontal } from "lucide-react";
import { useChatStore } from "@/store/chatStore";
import { useVideoCallStore } from "@/store/videoCallStore";

export function BottomNavigation() {
  const [activeTab, setActiveTab] = useState("home");
  const { toggleChat, isOpen: isChatOpen } = useChatStore();
  const { startCall, isCallActive } = useVideoCallStore();

  const NavButton = ({
    icon,
    label,
    value,
  }: {
    icon: React.ReactNode;
    label: string;
    value: string;
  }) => (
    <button
      onClick={() => setActiveTab(value)}
      className={`flex flex-col items-center justify-center flex-1 py-1 sm:py-2 ${
        activeTab === value ? "text-[#edcf5d]" : "text-gray-400"
      }`}
    >
      {icon}
      <span className="text-[10px] sm:text-xs mt-0.5 sm:mt-1">{label}</span>
    </button>
  );

  const ChatButton = () => (
    <button
      onClick={() => {
        setActiveTab("chat");
        toggleChat();
      }}
      className={`flex flex-col items-center justify-center flex-1 py-1 sm:py-2 ${
        activeTab === "chat" || isChatOpen ? "text-[#E1C760]" : "text-gray-400"
      }`}
    >
      <MessageCircle size={16} className="sm:w-5 sm:h-5" />
      <span className="text-[10px] sm:text-xs mt-0.5 sm:mt-1"></span>
    </button>
  );

  const VideoCallButton = () => (
    <button
      onClick={() => {
        setActiveTab("video");
        startCall();
      }}
      className={`flex flex-col items-center justify-center flex-1 py-1 sm:py-2 ${
        activeTab === "video" || isCallActive ? "text-[#E1C760]" : "text-gray-400"
      }`}
    >
      <Video size={16} className="sm:w-5 sm:h-5" />
      <span className="text-[10px] sm:text-xs mt-0.5 sm:mt-1"></span>
    </button>
  );

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-[#2a2a2a] shadow-lg bottom-nav">
      <div className="max-w-xl mx-auto flex items-end justify-between px-2 sm:px-4 border-t-2 border-[#E1C760]">
        <NavButton icon={<Home size={16} className="sm:w-5 sm:h-5" />} label="" value="home" />
        <ChatButton />
        <VideoCallButton />
        <NavButton icon={<MoreHorizontal size={16} className="sm:w-5 sm:h-5" />} label="" value="more" />
      </div>
    </div>
  );
}

export default BottomNavigation;
