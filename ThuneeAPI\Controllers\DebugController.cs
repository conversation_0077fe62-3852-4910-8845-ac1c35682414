using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Services;

namespace ThuneeAPI.Controllers
{
    [ApiController]
    [Route("debug")]
    public class DebugController : ControllerBase
    {
        private readonly ILobbyService _lobbyService;

        public DebugController(ILobbyService lobbyService)
        {
            _lobbyService = lobbyService;
        }

        [HttpGet("match-queue")]
        public IActionResult GetMatchQueueStatus()
        {
            // This would need to be implemented in the lobby service
            // to provide debug information about the match queue
            
            var debugInfo = new
            {
                QueueSize = 0, // Placeholder
                Queue = new object[0], // Placeholder
                TotalLobbies = 0 // Placeholder
            };

            return Ok(debugInfo);
        }

        [HttpGet("force-match")]
        public IActionResult ForceMatch([FromQuery] string lobby1, [FromQuery] string lobby2)
        {
            if (string.IsNullOrEmpty(lobby1) || string.IsNullOrEmpty(lobby2))
            {
                return BadRequest(new { success = false, error = "Both lobby1 and lobby2 parameters are required" });
            }

            var lobby1Obj = _lobbyService.GetLobby(lobby1);
            var lobby2Obj = _lobbyService.GetLobby(lobby2);

            if (lobby1Obj == null || lobby2Obj == null)
            {
                return BadRequest(new { success = false, error = "One or both lobbies not found" });
            }

            // Force match logic would be implemented here
            // This is a placeholder implementation

            return Ok(new { success = true, message = "Match forced successfully" });
        }
    }
}
