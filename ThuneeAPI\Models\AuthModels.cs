using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Models
{
    // Authentication Request Models
    public class RegisterRequest
    {
        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; } = string.Empty;
    }

    public class LoginRequest
    {
        [Required]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        public string Password { get; set; } = string.Empty;
    }

    public class VerifyOtpRequest
    {
        [Required]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [StringLength(4, MinimumLength = 4)]
        public string OtpCode { get; set; } = string.Empty;
    }

    public class ResendOtpRequest
    {
        [Required]
        public string Username { get; set; } = string.Empty;
    }

    // Authentication Response Models
    public class AuthResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserDto? User { get; set; }
        public string? Token { get; set; }
    }

    public class UserDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsVerified { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    // Game Result Models
    public class GameResultRequest
    {
        [Required]
        public string LobbyCode { get; set; } = string.Empty;
        
        public int CompetitionId { get; set; } = 1; // Default to first competition
        
        public int WinningTeam { get; set; }
        
        public int Team1Balls { get; set; }
        
        public int Team2Balls { get; set; }
        
        public int TotalBalls { get; set; }
        
        public List<PlayerGameResultDto> PlayerResults { get; set; } = new();
        
        public string? GameSettings { get; set; }
    }

    public class PlayerGameResultDto
    {
        [Required]
        public string PlayerName { get; set; } = string.Empty;
        
        public int Team { get; set; }
        
        public int Position { get; set; }
        
        public bool IsWinner { get; set; }
        
        public int BallsWon { get; set; }
        
        public int TotalPoints { get; set; }
        
        public int JordhiCalls { get; set; }
        
        public int SuccessfulJordhiCalls { get; set; }
        
        public int KhanakCalls { get; set; }
        
        public int SuccessfulKhanakCalls { get; set; }
        
        public int ThuneeCalls { get; set; }
        
        public int SuccessfulThuneeCalls { get; set; }
        
        public int DoubleCalls { get; set; }
        
        public int SuccessfulDoubleCalls { get; set; }
        
        public int FourBallPenalties { get; set; }
        
        public int HandsWon { get; set; }
        
        public int TotalHands { get; set; }
        
        public List<BallResultDto>? BallResults { get; set; }
    }

    public class BallResultDto
    {
        public int BallNumber { get; set; }
        public int TeamScore { get; set; }
        public int OpponentScore { get; set; }
        public bool Won { get; set; }
        public string? SpecialCalls { get; set; }
    }

    // Competition and Leaderboard Models
    public class CompetitionDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public PrizesDto Prizes { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public int TotalPlayers { get; set; }
        public int TotalGames { get; set; }
    }

    public class PrizesDto
    {
        public string First { get; set; } = string.Empty;
        public string Second { get; set; } = string.Empty;
        public string Third { get; set; } = string.Empty;
    }

    public class LeaderboardDto
    {
        public List<LeaderboardEntryDto> Entries { get; set; } = new();
        public int TotalItems { get; set; }
        public int CurrentPage { get; set; }
        public int ItemsPerPage { get; set; }
        public int TotalPages { get; set; }
    }

    public class LeaderboardEntryDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string PlayerName { get; set; } = string.Empty;
        public int Rank { get; set; }
        public int TotalScore { get; set; }
        public int GamesPlayed { get; set; }
        public int GamesWon { get; set; }
        public decimal WinRate { get; set; }
        public int TotalBallsWon { get; set; }
        public int TotalPoints { get; set; }
    }

    public class PlayerDetailsDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public PlayerStatsDto Stats { get; set; } = new();
    }

    public class PlayerStatsDto
    {
        public int TotalGames { get; set; }
        public int TotalWins { get; set; }
        public decimal OverallWinRate { get; set; }
        public int TotalBallsWon { get; set; }
        public int TotalPoints { get; set; }
        public int TotalJordhiCalls { get; set; }
        public int SuccessfulJordhiCalls { get; set; }
        public int TotalKhanakCalls { get; set; }
        public int SuccessfulKhanakCalls { get; set; }
        public int TotalThuneeCalls { get; set; }
        public int SuccessfulThuneeCalls { get; set; }
        public List<CompetitionStatsDto> CompetitionStats { get; set; } = new();
    }

    public class CompetitionStatsDto
    {
        public int CompetitionId { get; set; }
        public string CompetitionName { get; set; } = string.Empty;
        public int Rank { get; set; }
        public int GamesPlayed { get; set; }
        public int GamesWon { get; set; }
        public decimal WinRate { get; set; }
        public int TotalScore { get; set; }
    }

    public class GameHistoryDto
    {
        public List<GameHistoryEntryDto> Games { get; set; } = new();
        public int TotalItems { get; set; }
        public int CurrentPage { get; set; }
        public int ItemsPerPage { get; set; }
        public int TotalPages { get; set; }
    }

    public class GameHistoryEntryDto
    {
        public int GameId { get; set; }
        public string CompetitionName { get; set; } = string.Empty;
        public DateTime PlayedAt { get; set; }
        public bool IsWinner { get; set; }
        public int Team { get; set; }
        public int BallsWon { get; set; }
        public int TotalBalls { get; set; }
        public int TotalPoints { get; set; }
        public string? SpecialCalls { get; set; }
    }
}
