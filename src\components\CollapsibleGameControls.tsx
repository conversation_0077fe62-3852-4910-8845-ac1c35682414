import { useState } from "react";
import { ArrowLeft } from "lucide-react";
import gameService from "@/services/gameService";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface GameControlsProps {
  onShowRules?: () => void;
}

const GameControls = ({ onShowRules }: GameControlsProps) => {
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);

  // Options for different menus
  const callOptions = ["Doubles", "Khanak", "Thunee"];
  const ballOptions = ["1 ball", "2 ball", "3 ball", "4 ball", "8 ball"];
  const fourEightBallOptions = ["4 ball"];
  const jordhiOptions = ["20", "20","30", "40", "50"];

  // Nested options for 4/8 ball
  const nestedOptions: Record<string, string[]> = {
    "4 ball": ["Under chopped", "Never follow suit", "Called incorrect Jodhi", "Partner caught you"],
    "8 ball": ["Partner caught you"],
  };

  // Handle socket actions
  const handleCallAction = (option: string) => {
    if (option === "Thunee") {
      console.log("Thunee called");
      gameService.sendGameAction("call_thunee", {});
    } else if (option === "Doubles") {
      console.log("Double called");
      gameService.sendGameAction("call_double", {});
    } else if (option === "Khanak") {
      console.log("Khanuck called");
      gameService.sendGameAction("call_khanuck", {});
    }
    setActiveSubmenu(null);
  };

  const handleBallAction = (option: string) => {
    console.log(`Ball option selected: ${option}`);
    gameService.sendGameAction("ball_selection", { ballOption: option });
    setActiveSubmenu(null);
  };

  const handleJordhiAction = (option: string) => {
    console.log(`Jordhi option selected: ${option}`);
    gameService.sendGameAction("jordhi_selection", { jordhiOption: option })
      .then(() => {
        console.log("Jordhi selection sent successfully");
      })
      .catch(err => {
        console.error("Error sending Jordhi selection:", err);
      });
    setActiveSubmenu(null);
  };

  const handleFourEightBallAction = (ballType: string, option: string) => {
    console.log(`Selected ${ballType} with option: ${option}`);

    // Special handling for "Called incorrect Jodhi" option
    if (option === "Called incorrect Jodhi") {
      // Open the incorrect Jordhi modal instead of sending action directly
      import("@/store/gameStore").then(({ useGameStore }) => {
        useGameStore.getState().updateGameState({ incorrectJordhiModalOpen: true });
      });
    }
    // Special handling for "Never follow suit" option
    else if (option === "Never follow suit") {
      // Open the Never Follow Suit modal to select a player and hand number
      import("@/store/gameStore").then(({ useGameStore }) => {
        // Make sure currentPlayerId is set before opening the modal
        const currentState = useGameStore.getState();
        const socketId = gameService.getSocketId();

        if (!currentState.currentPlayerId && socketId) {
          console.log("Setting currentPlayerId before opening modal:", socketId);
          useGameStore.getState().updateGameState({
            currentPlayerId: socketId,
            neverFollowSuitModalOpen: true
          });
        } else {
          useGameStore.getState().updateGameState({ neverFollowSuitModalOpen: true });
        }
      });
    }
    // Special handling for "Under chopped" option
    else if (option === "Under chopped") {
      // Open the Under Chopped modal to select a player and hand number
      import("@/store/gameStore").then(({ useGameStore }) => {
        // Make sure currentPlayerId is set before opening the modal
        const currentState = useGameStore.getState();
        const socketId = gameService.getSocketId();

        if (!currentState.currentPlayerId && socketId) {
          console.log("Setting currentPlayerId before opening modal:", socketId);
          useGameStore.getState().updateGameState({
            currentPlayerId: socketId,
            underChoppedModalOpen: true
          });
        } else {
          useGameStore.getState().updateGameState({ underChoppedModalOpen: true });
        }
      });
    }
    else {
      // For other options, send action directly
      gameService.sendGameAction("four_eight_ball_selection", {
        ballType,
        option
      });
    }

    setActiveSubmenu(null);
  };

  // Render submenu content based on active submenu
  const renderSubmenuContent = () => {
    const buttonClass = "h-8 sm:h-10 w-full bg-black text-[#E1C760] text-xs sm:text-sm font-medium border border-[#E1C760] rounded-lg hover:bg-[#E1C760]/10 mb-1";
    const backButtonClass = "h-8 sm:h-10 w-full bg-black text-[#E1C760] text-xs sm:text-sm font-medium border border-[#E1C760] rounded-lg hover:bg-[#E1C760]/10 flex items-center justify-center mb-1";

    switch (activeSubmenu) {
      case "calls":
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu(null)}
              className={backButtonClass}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </button>
            {callOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => handleCallAction(option)}
                className={buttonClass}
              >
                {option}
              </button>
            ))}
          </div>
        );
      case "ball":
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu(null)}
              className={backButtonClass}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </button>
            {ballOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => handleBallAction(option)}
                className={buttonClass}
              >
                {option}
              </button>
            ))}
          </div>
        );
      case "4/8ball":
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu(null)}
              className={backButtonClass}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </button>
            {fourEightBallOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => setActiveSubmenu(`${option}-submenu`)}
                className={buttonClass}
              >
                {option}
              </button>
            ))}
          </div>
        );
      case "4 ball-submenu":
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu("4/8ball")}
              className={backButtonClass}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </button>
            {nestedOptions["4 ball"].map((option, index) => (
              <button
                key={index}
                onClick={() => handleFourEightBallAction("4 ball", option)}
                className={buttonClass}
              >
                {option}
              </button>
            ))}
          </div>
        );
      case "8 ball-submenu":
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu("4/8ball")}
              className={backButtonClass}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </button>
            {nestedOptions["8 ball"].map((option, index) => (
              <button
                key={index}
                onClick={() => handleFourEightBallAction("8 ball", option)}
                className={buttonClass}
              >
                {option}
              </button>
            ))}
          </div>
        );
      case "jordhi":
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu(null)}
              className={backButtonClass}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </button>
            {jordhiOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => handleJordhiAction(option)}
                className={buttonClass}
              >
                {option}
              </button>
            ))}
          </div>
        );
      default:
        return (
          <div className="flex flex-col gap-1 mb-2">
            <button
              onClick={() => setActiveSubmenu("calls")}
              className={buttonClass}
            >
              Calls
            </button>
            {/* <button
              onClick={() => setActiveSubmenu("ball")}
              className={buttonClass}
            >
              Ball
            </button> */}
            <button
              onClick={() => setActiveSubmenu("4/8ball")}
              className={buttonClass}
            >
              4 Ball
            </button>
            <button
              onClick={() => setActiveSubmenu("jordhi")}
              className={buttonClass}
            >
              Jordhi
            </button>
          </div>
        );
    }
  };

  return (
    // <div className="fixed bottom-12 sm:bottom-14 transform -translate-x-1/2 z-10 w-56 sm:w-48 -right-20 game-controls">
    <div className="fixed top-40 lg:top-[35rem] transform -translate-x-1/2 z-10 w-56 sm:w-48 -right-20 game-controls">

    <Accordion
        type="single"
        collapsible
        className="w-full"
        defaultValue=""
      >
        <AccordionItem
          value="game-controls"
          className="border-0"
        >
          <AccordionTrigger
            className="h-8 sm:h-10 bg-black text-[#E1C760] text-xs sm:text-sm font-medium rounded-lg border border-[#E1C760] flex items-center justify-center shadow-lg shadow-black/50 px-2 sm:px-4 py-0"
          >
            Calls
          </AccordionTrigger>
          <AccordionContent
            className="pt-1 sm:pt-2 data-[state=open]:animate-accordion-up data-[state=closed]:animate-accordion-down"
            forceMount
          >
            {renderSubmenuContent()}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );

};

export default GameControls;