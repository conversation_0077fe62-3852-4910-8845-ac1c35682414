"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";

export type ShuffleType = "cascade" | "riffle" | "overhand";

interface ShuffleOptionsProps {
  onSelectShuffle: (type: ShuffleType) => void;
  onCancel: () => void;
  isOpen: boolean;
}

export default function ShuffleOptions({
  onSelectShuffle,
  onCancel,
  isOpen
}: ShuffleOptionsProps) {
  const [selectedType, setSelectedType] = useState<ShuffleType>("cascade");

  const handleSelect = (type: ShuffleType) => {
    setSelectedType(type);
  };

  const handleConfirm = () => {
    onSelectShuffle(selectedType);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 flex items-center justify-center z-50"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          // className="bg-black border-4 border-[#E1C760] rounded-lg p-6 max-w-md w-full relative height: 100vh;"
          className="bg-black border-4 border-[#E1C760] rounded-lg p-2  w-full relative height: 100vh;"

        >
          <button
            onClick={onCancel}
            className="absolute top-2 right-2 text-[#E1C760] hover:text-white"
          >
            <X size={24} />
          </button>
          
          <h2 className="text-[#E1C760] text-2xl font-bold mb-6 text-center">
            Select Shuffle Type
          </h2>
          
          <div className="grid grid-cols-1 gap-4 mb-6">
            <ShuffleOption
              type="cascade"
              selected={selectedType === "cascade"}
              onSelect={handleSelect}
              title="Cascade Shuffle"
              description="Cards cascade from one hand to another"
            />
            
            <ShuffleOption
              type="riffle"
              selected={selectedType === "riffle"}
              onSelect={handleSelect}
              title="Riffle Shuffle"
              description="Cards are riffled together from both hands"
            />
            
            <ShuffleOption
              type="overhand"
              selected={selectedType === "overhand"}
              onSelect={handleSelect}
              title="Overhand Shuffle"
              description="Cards are transferred in small groups from one hand to another"
            />
          </div>
          
          <div className="flex justify-center">
            <Button
              onClick={handleConfirm}
              className="bg-[#E1C760] text-black px-8 py-3 rounded-md text-xl font-bold shadow-lg border-2 border-[#E1C760]/80 hover:bg-[#E1C760]/90 transition-colors"
            >
              SHUFFLE
            </Button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

interface ShuffleOptionProps {
  type: ShuffleType;
  selected: boolean;
  onSelect: (type: ShuffleType) => void;
  title: string;
  description: string;
}

function ShuffleOption({
  type,
  selected,
  onSelect,
  title,
  description
}: ShuffleOptionProps) {
  return (
    <div
      className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
        selected
          ? "border-[#E1C760] bg-[#E1C760]/20"
          : "border-gray-700 hover:border-[#E1C760]/50"
      }`}
      onClick={() => onSelect(type)}
    >
      <div className="flex items-center">
        <div
          className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
            selected ? "border-[#E1C760]" : "border-gray-500"
          }`}
        >
          {selected && (
            <div className="w-3 h-3 rounded-full bg-[#E1C760]"></div>
          )}
        </div>
        <div>
          <h3 className="text-[#E1C760] font-bold">{title}</h3>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
      </div>
    </div>
  );
}
