using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface IAuthService
    {
        Task<AuthResponse> RegisterAsync(RegisterRequest request);
        Task<AuthResponse> LoginAsync(LoginRequest request);
        Task<AuthResponse> VerifyOtpAsync(VerifyOtpRequest request);
        Task<AuthResponse> ResendOtpAsync(ResendOtpRequest request);
        Task<UserDto?> GetUserByIdAsync(int userId);
        Task<UserDto?> GetUserByUsernameAsync(string username);
        string GenerateJwtToken(User user);
        bool ValidateJwtToken(string token);
    }
}
