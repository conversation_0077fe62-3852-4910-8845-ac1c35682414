# PowerShell script to fix and run Thunee ASP.NET Core API
Write-Host "Fixing and starting Thunee ASP.NET Core API..." -ForegroundColor Green

# Navigate to the correct directory
$projectPath = "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
if (Test-Path $projectPath) {
    Set-Location $projectPath
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Green
} else {
    Write-Host "Error: ThuneeAPI directory not found at $projectPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`nChecking for processes using ports 3001, 5000, and 5001..." -ForegroundColor Yellow

# Function to kill processes on a specific port
function Kill-ProcessOnPort {
    param([int]$Port)
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
        if ($processes) {
            foreach ($processId in $processes) {
                Write-Host "Killing process $processId on port $Port" -ForegroundColor Yellow
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
            }
        } else {
            Write-Host "No processes found on port $Port" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Could not check port $Port (this is normal if nothing is running)" -ForegroundColor Gray
    }
}

# Kill processes on the ports we need
Kill-ProcessOnPort -Port 3001
Kill-ProcessOnPort -Port 5000
Kill-ProcessOnPort -Port 5001

Write-Host "`nPorts cleared. Starting ASP.NET Core API..." -ForegroundColor Green

# Set environment to Development explicitly
$env:ASPNETCORE_ENVIRONMENT = "Development"
Write-Host "Environment: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor Cyan

Write-Host "`nBuilding project..." -ForegroundColor Yellow
$buildResult = & dotnet build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`nBuild successful!" -ForegroundColor Green
Write-Host "`nStarting the application on development ports..." -ForegroundColor Yellow
Write-Host "`nThe API will be available at:" -ForegroundColor Cyan
Write-Host "  http://localhost:5000" -ForegroundColor White
Write-Host "  https://localhost:5001" -ForegroundColor White
Write-Host "`nTest client available at:" -ForegroundColor Cyan
Write-Host "  http://localhost:5000/test-client.html" -ForegroundColor White
Write-Host "`nPress Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Run with explicit URLs to override any configuration issues
& dotnet run --urls "http://localhost:5000;https://localhost:5001"
