<!DOCTYPE html>
<html>
<head>
    <title>Thunee SignalR Test Client</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #222; color: #E1C760; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; background: #333; border-radius: 5px; }
        button { background: #E1C760; color: #222; border: none; padding: 10px 15px; margin: 5px; border-radius: 3px; cursor: pointer; }
        button:hover { background: #d4b84a; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #555; background: #444; color: #E1C760; border-radius: 3px; }
        #messages { height: 300px; overflow-y: auto; background: #111; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .message { margin: 2px 0; }
        .sent { color: #4CAF50; }
        .received { color: #2196F3; }
        .error { color: #f44336; }
        .info { color: #E1C760; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Thunee SignalR Test Client</h1>
        
        <div class="section">
            <h3>Connection</h3>
            <input type="text" id="serverUrl" value="http://localhost:5000/gameHub" placeholder="Server URL">
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
            <span id="connectionStatus">Disconnected</span>
        </div>

        <div class="section">
            <h3>Lobby Actions</h3>
            <input type="text" id="playerName" value="TestPlayer" placeholder="Player Name">
            <input type="text" id="teamName" value="Test Team" placeholder="Team Name">
            <button onclick="createLobby()">Create Lobby</button>
            <br>
            <input type="text" id="lobbyCode" placeholder="Lobby Code">
            <button onclick="joinLobby()">Join Lobby</button>
            <button onclick="setReady()">Set Ready</button>
        </div>

        <div class="section">
            <h3>Game Actions</h3>
            <select id="timeframe">
                <option value="3">3 seconds</option>
                <option value="4">4 seconds</option>
                <option value="5">5 seconds</option>
                <option value="6">6 seconds</option>
                <option value="60">60 seconds</option>
            </select>
            <button onclick="voteTimeframe()">Vote Timeframe</button>
            <br>
            <select id="trumpSuit">
                <option value="hearts">Hearts</option>
                <option value="diamonds">Diamonds</option>
                <option value="clubs">Clubs</option>
                <option value="spades">Spades</option>
            </select>
            <button onclick="selectTrump()">Select Trump</button>
        </div>

        <div class="section">
            <h3>Messages</h3>
            <div id="messages"></div>
            <button onclick="clearMessages()">Clear</button>
        </div>
    </div>

    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>
    <script>
        let connection = null;
        let currentLobbyCode = null;

        function addMessage(message, type = 'info') {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = `message ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateConnectionStatus(status) {
            document.getElementById('connectionStatus').textContent = status;
        }

        async function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            connection = new signalR.HubConnectionBuilder()
                .withUrl(serverUrl)
                .withAutomaticReconnect()
                .build();

            // Set up event listeners
            connection.on('players_updated', (data) => {
                addMessage(`Players updated: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('team_names_updated', (data) => {
                addMessage(`Team names updated: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('team_ready_updated', (data) => {
                addMessage(`Team ready updated: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('game_started', (data) => {
                addMessage(`Game started: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('game_phase_updated', (data) => {
                addMessage(`Game phase updated: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('timeframe_options', (data) => {
                addMessage(`Timeframe options: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('timeframe_vote_update', (data) => {
                addMessage(`Timeframe vote update: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('trump_selected', (data) => {
                addMessage(`Trump selected: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('card_played', (data) => {
                addMessage(`Card played: ${JSON.stringify(data)}`, 'received');
            });

            connection.on('chat_message', (data) => {
                addMessage(`Chat: ${data.playerName}: ${data.message}`, 'received');
            });

            try {
                await connection.start();
                updateConnectionStatus('Connected');
                addMessage('Connected to SignalR hub', 'info');
            } catch (err) {
                updateConnectionStatus('Connection Failed');
                addMessage(`Connection failed: ${err}`, 'error');
            }
        }

        async function disconnect() {
            if (connection) {
                await connection.stop();
                updateConnectionStatus('Disconnected');
                addMessage('Disconnected from SignalR hub', 'info');
            }
        }

        async function createLobby() {
            if (!connection) {
                addMessage('Not connected', 'error');
                return;
            }

            const playerName = document.getElementById('playerName').value;
            const teamName = document.getElementById('teamName').value;

            try {
                const response = await connection.invoke('CreateLobby', {
                    playerName: playerName,
                    teamName: teamName
                });

                addMessage(`Create lobby response: ${JSON.stringify(response)}`, 'sent');
                
                if (response.success) {
                    currentLobbyCode = response.data.lobbyCode;
                    document.getElementById('lobbyCode').value = currentLobbyCode;
                    addMessage(`Lobby created: ${currentLobbyCode}`, 'info');
                }
            } catch (err) {
                addMessage(`Create lobby error: ${err}`, 'error');
            }
        }

        async function joinLobby() {
            if (!connection) {
                addMessage('Not connected', 'error');
                return;
            }

            const lobbyCode = document.getElementById('lobbyCode').value;
            const playerName = document.getElementById('playerName').value;

            try {
                const response = await connection.invoke('JoinLobby', {
                    lobbyCode: lobbyCode,
                    playerName: playerName
                });

                addMessage(`Join lobby response: ${JSON.stringify(response)}`, 'sent');
                
                if (response.success) {
                    currentLobbyCode = lobbyCode;
                    addMessage(`Joined lobby: ${lobbyCode}`, 'info');
                }
            } catch (err) {
                addMessage(`Join lobby error: ${err}`, 'error');
            }
        }

        async function setReady() {
            if (!connection) {
                addMessage('Not connected', 'error');
                return;
            }

            try {
                const response = await connection.invoke('SetTeamReady', {
                    lobbyCode: currentLobbyCode,
                    ready: true
                });

                addMessage(`Set ready response: ${JSON.stringify(response)}`, 'sent');
            } catch (err) {
                addMessage(`Set ready error: ${err}`, 'error');
            }
        }

        async function voteTimeframe() {
            if (!connection) {
                addMessage('Not connected', 'error');
                return;
            }

            const timeframe = parseInt(document.getElementById('timeframe').value);

            try {
                const response = await connection.invoke('VoteTimeframe', {
                    timeframe: timeframe
                });

                addMessage(`Vote timeframe response: ${JSON.stringify(response)}`, 'sent');
            } catch (err) {
                addMessage(`Vote timeframe error: ${err}`, 'error');
            }
        }

        async function selectTrump() {
            if (!connection) {
                addMessage('Not connected', 'error');
                return;
            }

            const trumpSuit = document.getElementById('trumpSuit').value;

            try {
                const response = await connection.invoke('SelectTrump', {
                    trumpSuit: trumpSuit
                });

                addMessage(`Select trump response: ${JSON.stringify(response)}`, 'sent');
            } catch (err) {
                addMessage(`Select trump error: ${err}`, 'error');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
    </script>
</body>
</html>
