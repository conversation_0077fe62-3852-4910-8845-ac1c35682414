# React Frontend Integration with ASP.NET Core SignalR

This guide explains how to update your React frontend to work with the new ASP.NET Core SignalR API.

## Prerequisites

Install the Microsoft SignalR client library:

```bash
npm install @microsoft/signalr
```

## Migration Steps

### 1. Replace Socket.IO with SignalR

**Remove Socket.IO:**
```bash
npm uninstall socket.io-client
```

**Install SignalR:**
```bash
npm install @microsoft/signalr
```

### 2. Update Your Socket Service

Replace your existing `src/services/gameService.ts` with the provided `signalRService.ts`:

```typescript
// Copy the content from client-integration/signalRService.ts
// to your src/services/gameService.ts (or create a new signalRService.ts)
```

### 3. Update Component Imports

Change your imports from:
```typescript
import gameService from '../services/gameService';
```

To:
```typescript
import signalRService from '../services/signalRService';
// or if you replaced gameService.ts:
import gameService from '../services/gameService';
```

### 4. Update Method Calls

**Old Socket.IO syntax:**
```typescript
// Emit events
socket.emit('createLobby', { playerName, teamName }, (response) => {
  // handle response
});

// Listen for events
socket.on('playersUpdated', (data) => {
  // handle data
});
```

**New SignalR syntax:**
```typescript
// Invoke hub methods (returns Promise)
const response = await signalRService.createLobby(playerName, teamName);

// Listen for events (same pattern)
signalRService.onPlayersUpdated((data) => {
  // handle data
});
```

### 5. Update Event Names

The ASP.NET Core API uses snake_case for event names to match the Node.js implementation:

| Frontend Event | SignalR Event |
|----------------|---------------|
| `playersUpdated` | `players_updated` |
| `gameStarted` | `game_started` |
| `cardPlayed` | `card_played` |
| `chatMessage` | `chat_message` |
| `trumpSelected` | `trump_selected` |

### 6. Environment Configuration

The service automatically detects the environment:

- **Development**: Connects to `http://localhost:5000`
- **Production**: Connects to `http://[current-host]:3001`

You can verify the configuration:
```typescript
console.log(signalRService.getConfig());
```

### 7. Connection Management

**Initialize connections:**
```typescript
// In your main component or App.tsx
useEffect(() => {
  const initializeConnections = async () => {
    try {
      await signalRService.connectToGameHub();
      // Only connect to video hub when needed
      // await signalRService.connectToVideoHub();
    } catch (error) {
      console.error('Failed to connect:', error);
    }
  };

  initializeConnections();

  // Cleanup on unmount
  return () => {
    signalRService.disconnect();
  };
}, []);
```

### 8. Error Handling

SignalR methods return Promises, so use try-catch:

```typescript
try {
  const response = await signalRService.createLobby(playerName, teamName);
  if (response.success) {
    // Handle success
    setLobbyCode(response.data.lobbyCode);
  } else {
    // Handle error
    console.error(response.error);
  }
} catch (error) {
  console.error('Network error:', error);
}
```

### 9. Event Listener Setup

Set up event listeners in useEffect:

```typescript
useEffect(() => {
  // Set up event listeners
  signalRService.onPlayersUpdated((data) => {
    setPlayers(data.players);
    setTeams(data.teams);
  });

  signalRService.onGameStarted((data) => {
    setGameStarted(true);
    setPlayers(data.players);
  });

  signalRService.onCardPlayed((data) => {
    // Handle card played
  });

  signalRService.onChatMessage((data) => {
    setChatMessages(prev => [...prev, data]);
  });

  // Cleanup function
  return () => {
    // SignalR automatically handles cleanup when connection is closed
  };
}, []);
```

### 10. Video Chat Integration

For video functionality:

```typescript
// Connect to video hub when entering a game
const initializeVideo = async () => {
  await signalRService.connectToVideoHub();
  
  // Set up video event listeners
  signalRService.onVideoUserJoined((data) => {
    console.log('User joined video:', data);
  });

  signalRService.onVideoSignal((data) => {
    // Handle WebRTC signaling
  });

  // Join video room
  await signalRService.joinVideoRoom(lobbyCode, playerName);
};
```

## Testing the Integration

1. **Start the ASP.NET Core API:**
   ```bash
   cd ThuneeAPI
   dotnet run
   ```

2. **Start your React app:**
   ```bash
   npm start
   ```

3. **Test the connection:**
   - Open browser console
   - Look for "Connected to Game Hub" message
   - Check `signalRService.getConfig()` output

4. **Use the test client:**
   - Navigate to `http://localhost:5000/test-client.html`
   - Test SignalR functionality

## Common Issues and Solutions

### CORS Errors
- Ensure your React app URL is in the CORS configuration
- Check `appsettings.Development.json` for allowed origins

### Connection Failures
- Verify the ASP.NET Core API is running
- Check browser console for detailed error messages
- Ensure firewall allows the ports (5000, 3001)

### Event Not Firing
- Verify event names match (snake_case)
- Check that event listeners are set up before the events are triggered
- Use browser dev tools to monitor SignalR traffic

### Authentication Issues
- If you add authentication later, update the `accessTokenFactory` in the connection config

## Performance Considerations

- Only connect to video hub when needed (during games)
- Use connection state checks before invoking methods
- Implement proper error handling and retry logic
- Consider connection pooling for high-traffic scenarios

## Migration Checklist

- [ ] Install @microsoft/signalr package
- [ ] Remove socket.io-client package
- [ ] Replace gameService with signalRService
- [ ] Update all method calls to use async/await
- [ ] Update event names to snake_case
- [ ] Test lobby creation and joining
- [ ] Test game functionality
- [ ] Test video chat (if used)
- [ ] Test production deployment
- [ ] Update error handling
- [ ] Verify CORS configuration
