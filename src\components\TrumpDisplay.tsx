"use client";
import { useGameStore } from "@/store/gameStore";
import { motion } from "framer-motion";
import { useEffect, useState, useRef } from "react";

export default function TrumpDisplay() {
  const { trumpSuit, playedCards, currentHand, currentBall } = useGameStore();
  const [showTrump, setShowTrump] = useState(false);
  const [temporaryReveal, setTemporaryReveal] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastHandRef = useRef<number>(0);
  const lastBallRef = useRef<number>(0);
  const firstCardPlayedRef = useRef<boolean>(false);

  // Track when the first card of a ball is played
  useEffect(() => {
    // Reset first card played flag when a new ball starts
    if (currentBall !== lastBallRef.current) {
      console.log("New ball started - resetting first card played flag");
      firstCardPlayedRef.current = false;
      lastBallRef.current = currentBall;
      lastHandRef.current = currentHand;
    }

    // Track the current hand without resetting the flag
    if (currentHand !== lastHandRef.current) {
      lastHandRef.current = currentHand;
    }

    // Check if this is the first card of the ball
    if (trumpSuit && playedCards.length === 1 && !firstCardPlayedRef.current) {
      console.log("First card of the ball played - revealing trump for 10 seconds");
      firstCardPlayedRef.current = true;
      setTemporaryReveal(true);

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // Set a timer to hide the trump after 10 seconds
      timerRef.current = setTimeout(() => {
        console.log("10 seconds passed - hiding trump");
        setTemporaryReveal(false);
      }, 10000);
    }
  }, [trumpSuit, playedCards, currentHand, currentBall]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Determine if trump should be shown
  useEffect(() => {
    // Show trump if it's temporarily revealed or if the user has chosen to always show it
    if (temporaryReveal || (trumpSuit && playedCards.length > 0)) {
      setShowTrump(true);
    } else {
      setShowTrump(false);
    }
  }, [trumpSuit, playedCards, temporaryReveal]);

  if (!trumpSuit || !showTrump) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-40 pointer-events-none">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-black/90 border-3 border-[#E1C760] rounded-lg p-4 flex flex-col items-center shadow-[0_0_15px_rgba(225,199,96,0.5)]"
      >
        <span className="text-[#E1C760] text-lg font-bold mb-2">TRUMP</span>
        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-700 to-green-900 flex items-center justify-center p-1.5 border border-[#E1C760]">
          <img
            src={`/SuitFaces/${trumpSuit.charAt(0).toUpperCase()}.svg`}
            alt={trumpSuit}
            className="w-full h-full"
          />
        </div>
        <span className="text-white text-sm mt-2 capitalize font-medium">{trumpSuit}</span>
        {temporaryReveal && (
          <span className="text-yellow-300 text-xs mt-1">
            (Visible for 10 seconds)
          </span>
        )}
      </motion.div>
    </div>
  );
}
