import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";

export default function DealerIndicator() {
  const { players } = useGameStore();
  const [showIndicator, setShowIndicator] = useState(true);
  const [dealer, setDealer] = useState<{id: string, name: string, avatar: string} | null>(null);

  // Find the dealer among players
  useEffect(() => {
    const dealerPlayer = players.find(player => player.isDealer);
    if (dealerPlayer) {
      setDealer({
        id: dealerPlayer.id,
        name: dealerPlayer.name,
        avatar: dealerPlayer.avatar
      });
    } else {
      setDealer(null);
    }
  }, [players]);

  // Auto-hide the indicator after 15 seconds
  useEffect(() => {
    if (dealer) {
      // Reset the indicator to visible whenever dealer changes
      setShowIndicator(true);

      const timer = setTimeout(() => {
        setShowIndicator(false);
      }, 15000);

      return () => clearTimeout(timer);
    }
  }, [dealer]);

  // If no dealer is found, don't render anything
  if (!dealer) return null;

  return (
    <AnimatePresence>
      {showIndicator && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.5 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
        >
          <div className="bg-black border-4 border-[#E1C760] rounded-lg p-6 shadow-lg flex flex-col items-center max-w-md mx-4">
            <h2 className="text-[#E1C760] text-2xl font-bold mb-4">Dealer Selected!</h2>

            <div className="flex items-center mb-4">
              <div className="relative w-16 h-16 mr-5">
                <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-30"></div>
                <div className="absolute inset-0 bg-green-500/30 rounded-full animate-pulse"></div>
                <div className="relative w-16 h-16 rounded-full overflow-hidden border-4 border-green-500">
                  <img src={dealer.avatar} alt={dealer.name} className="w-full h-full object-cover" />
                </div>
              </div>
              <div className="flex flex-col">
                <span className="text-white font-bold text-xl">{dealer.name}</span>
                <div className="flex items-center">
                  <span className="text-green-500 font-bold">is the </span>
                  <span className="text-[#E1C760] font-bold ml-1 text-2xl">DEALER</span>
                </div>
              </div>
            </div>

            <p className="text-white text-center mb-4">The dealer will now deal the cards for the game.</p>

            <button
              onClick={() => setShowIndicator(false)}
              className="bg-[#E1C760] text-black px-4 py-2 rounded-md font-bold hover:bg-[#E1C760]/80 transition-colors"
            >
              Continue to Game
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
