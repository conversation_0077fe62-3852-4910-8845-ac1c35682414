using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface ICompetitionService
    {
        Task<List<CompetitionDto>> GetCompetitionsAsync();
        Task<CompetitionDto?> GetCompetitionAsync(int competitionId);
        Task<LeaderboardDto> GetCompetitionLeaderboardAsync(int competitionId, int page = 1, int pageSize = 10, string? sortBy = "score", string? sortDirection = "desc", int? minGames = null);
        Task<bool> SaveGameResultAsync(GameResultRequest request);
        Task<PlayerDetailsDto?> GetPlayerDetailsAsync(int userId);
        Task<GameHistoryDto> GetPlayerGameHistoryAsync(int userId, int page = 1, int pageSize = 10);
        Task<List<UserDto>> GetPlayersAsync();
        Task UpdatePlayerCompetitionStatsAsync(int gameId);
    }
}
