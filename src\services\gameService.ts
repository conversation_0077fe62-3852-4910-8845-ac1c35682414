// Unified Game Service - Adapter for SignalR and Socket.IO
// This service provides a unified interface and handles the migration from Socket.IO to SignalR

import signalRService from './signalRService';

// Feature flag to control which service to use
const USE_SIGNALR = import.meta.env.VITE_USE_SIGNALR !== 'false'; // Default to true

class GameService {
  private useSignalR: boolean = USE_SIGNALR;

  constructor() {
    console.log(`Game Service initialized with ${this.useSignalR ? 'SignalR' : 'Socket.IO'}`);
  }

  // Check if connected
  isConnected(): boolean {
    return signalRService.isConnected();
  }

  // Connect to the game service
  async connect(playerName: string): Promise<void> {
    return signalRService.connect(playerName);
  }

  // Disconnect from the game service
  async disconnect(): Promise<void> {
    return signalRService.disconnect();
  }

  // Create a new lobby
  async createLobby(playerName: string, teamName?: string, timeSettings?: any): Promise<{
    lobbyCode: string;
    partnerInviteCode: string;
    opponentInviteCode: string;
  }> {
    return signalRService.createLobby(playerName, teamName, timeSettings);
  }

  // Join an existing lobby
  async joinLobby(lobbyCode: string, playerName: string): Promise<{ actualLobbyCode?: string, isInviteCode?: boolean }> {
    return signalRService.joinLobby(lobbyCode, playerName);
  }

  // Update team name
  async updateTeamName(lobbyCode: string, teamNumber: 1 | 2, teamName: string): Promise<void> {
    return signalRService.updateTeamName(lobbyCode, teamNumber, teamName);
  }

  // Set team ready status
  async setTeamReady(lobbyCode: string, ready: boolean): Promise<void> {
    return signalRService.setTeamReady(lobbyCode, ready);
  }

  // Switch team
  async switchTeam(lobbyCode: string): Promise<void> {
    // This method needs to be implemented in SignalR service
    return signalRService.sendCustomEvent('SwitchTeam', { lobbyCode });
  }

  // Start the game
  async startGame(lobbyCode: string): Promise<void> {
    return signalRService.startGame(lobbyCode);
  }

  // Find a match for the team
  async findMatch(lobbyCode: string): Promise<void> {
    return signalRService.findMatch(lobbyCode);
  }

  // Cancel finding a match
  async cancelFindMatch(lobbyCode: string): Promise<void> {
    return signalRService.cancelFindMatch(lobbyCode);
  }

  // Send a game action
  async sendGameAction(action: string, data: any): Promise<void> {
    return signalRService.sendGameAction(action, data);
  }

  // Vote for timeframe
  async voteTimeframe(timeframe: number): Promise<void> {
    return signalRService.voteTimeframe(timeframe);
  }

  // Play a card
  async playCard(card: any): Promise<void> {
    return signalRService.playCard(card);
  }

  // Select trump
  async selectTrump(trumpSuit: string): Promise<void> {
    return signalRService.selectTrump(trumpSuit);
  }

  // Send chat message
  async sendChatMessage(message: string, lobbyCode?: string): Promise<void> {
    return signalRService.sendChatMessage(message, lobbyCode);
  }

  // Add an event listener
  on(event: string, callback: (data: any) => void): void {
    signalRService.on(event, callback);
  }

  // Remove an event listener
  off(event: string, callback?: (data: any) => void): void {
    signalRService.off(event, callback);
  }

  // Get the connection ID
  getSocketId(): string | null {
    return signalRService.getSocketId();
  }

  // Update the server URL (deprecated)
  updateServerUrl(port: string): void {
    console.warn('updateServerUrl is deprecated. Use environment variables instead.');
    signalRService.updateServerUrl(port);
  }

  // Request timeframe options from the server
  async requestTimeframeOptions(): Promise<void> {
    return signalRService.requestTimeframeOptions();
  }

  // Send a custom event to the server
  async sendCustomEvent(event: string, data: any): Promise<any> {
    return signalRService.sendCustomEvent(event, data);
  }

  // Get service configuration
  getConfig() {
    return {
      ...signalRService.getConfig(),
      useSignalR: this.useSignalR
    };
  }

  // Get the underlying service (for debugging)
  getUnderlyingService() {
    return signalRService;
  }

  // Force switch to SignalR (for testing)
  forceSignalR(): void {
    this.useSignalR = true;
    console.log('Forced switch to SignalR');
  }

  // Check which service is being used
  getServiceType(): 'SignalR' | 'Socket.IO' {
    return this.useSignalR ? 'SignalR' : 'Socket.IO';
  }
}

// Create a singleton instance
const gameService = new GameService();

export default gameService;
