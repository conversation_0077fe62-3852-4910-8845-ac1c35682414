"use client";
import { useGameStore } from "@/store/gameStore";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "./ui/card";
import { getCardImagePath } from "@/utils/cardUtils";
import { useEffect, useState } from "react";
import gameService from "@/services/gameService";
import { PlayerPosition } from "@/utils/playerPositionUtils";

interface PlayedCardInfo {
  card: {
    id: string;
    value: string;
    suit: string;
  };
  playerId: string;
  position: PlayerPosition;
  index: number; // Add index to track order of play
}

export default function PlayedCardsArrangement() {
  const { playedCards, players, currentTurn } = useGameStore();
  const [arrangedCards, setArrangedCards] = useState<PlayedCardInfo[]>([]);

  // Map player IDs to positions (1-4) based on fixed positions
  useEffect(() => {
    // Clear arranged cards if there are no played cards
    if (playedCards.length === 0) {
      if (arrangedCards.length > 0) {
        console.log('Clearing arranged cards because playedCards is empty');
        setArrangedCards([]);
      }
      return;
    }

    // Skip if no players
    if (players.length === 0) {
      return;
    }

    // Check for invalid played cards (missing required properties)
    const invalidCards = playedCards.filter(card => !card.id || !card.value || !card.suit);
    if (invalidCards.length > 0) {
      console.error('Found invalid cards in playedCards:', invalidCards);
    }

    console.log('PlayedCardsArrangement - Current state:', {
      playedCards: playedCards.length,
      players: players.map(p => `${p.name} (${p.id}) - Position: ${p.position || 'unknown'}`),
    });

    // Map each played card to its position based on who played it
    const newArrangedCards: PlayedCardInfo[] = [];

    playedCards.forEach((card, index) => {
      // Get the player ID from the card's playedBy property
      const playerId = (card as any).playedBy;

      if (!playerId) {
        console.error('Card missing playedBy property:', card);
        return; // Skip this card if it doesn't have a playerId
      }

      // Find the player who played this card
      const player = players.find(p => p.id === playerId);

      if (!player) {
        console.error('Player not found for ID:', playerId);
        // Instead of skipping, assign a default position based on index
        // This ensures cards are still displayed even if player info is missing
        newArrangedCards.push({
          card,
          playerId,
          position: ((index % 4) + 1) as PlayerPosition,
          index
        });
        return;
      }

      // Get the player's fixed position
      const playerPosition = player.position;

      if (!playerPosition) {
        console.error('Player has no position assigned:', player.name);
        // Instead of skipping, assign a default position based on index
        newArrangedCards.push({
          card,
          playerId,
          position: ((index % 4) + 1) as PlayerPosition,
          index
        });
        return;
      }

      // Get the current player's ID
      const currentPlayerId = gameService.getSocketId();
      if (!currentPlayerId) {
        console.error('No current player ID available');
        // Use a default arrangement if we can't get the current player ID
        newArrangedCards.push({
          card,
          playerId,
          position: playerPosition,
          index
        });
        return;
      }

      // Find the current player
      const currentPlayer = players.find(p => p.id === currentPlayerId);
      if (!currentPlayer) {
        console.error('Current player not found in players list');
        // Use a default arrangement if we can't find the current player
        newArrangedCards.push({
          card,
          playerId,
          position: playerPosition,
          index
        });
        return;
      }

      // Get the current player's position
      const currentPlayerPosition = currentPlayer.position;
      if (!currentPlayerPosition) {
        console.error('Current player has no position assigned:', currentPlayer.name);
        // Use a default arrangement if the current player has no position
        newArrangedCards.push({
          card,
          playerId,
          position: playerPosition,
          index
        });
        return;
      }

      // Calculate relative position (1-4) based on the current player's position
      // This ensures that the current player is always at the bottom (position 3)
      // and other players are positioned relative to that
      const relativePosition = ((playerPosition - currentPlayerPosition + 4) % 4 + 1) as PlayerPosition;

      newArrangedCards.push({
        card,
        playerId,
        position: relativePosition,
        index // Store the play order
      });

      console.log(`Added card to arrangedCards: ${card.value} of ${card.suit} played by ${player.name} (fixed position: ${playerPosition}, relative position: ${relativePosition})`);
    });

    setArrangedCards(newArrangedCards);
  }, [playedCards, players]);

  // Debug log the played cards
  useEffect(() => {
    if (playedCards.length > 0) {
      console.log('PlayedCards state:', playedCards.map(card => {
        return {
          id: card.id,
          value: card.value,
          suit: card.suit,
          playedBy: (card as any).playedBy
        };
      }));
    }
  }, [playedCards]);

  // Debug log to see what's happening with the played cards
  useEffect(() => {
    if (playedCards.length > 0) {
      console.log('PlayedCardsArrangement - Current played cards:', {
        playedCards: playedCards.map(card => `${card.value} of ${card.suit} played by ${(card as any).playedBy || 'unknown'}`),
        arrangedCards: arrangedCards.map(info => `${info.card.value} of ${info.card.suit} played by ${info.playerId}`)
      });

      // Check for any cards missing the playedBy property
      const cardsWithoutPlayedBy = playedCards.filter(card => !(card as any).playedBy);
      if (cardsWithoutPlayedBy.length > 0) {
        console.warn('Found cards without playedBy property:', cardsWithoutPlayedBy);
      }

      // Check for any players without positions
      const playersWithoutPositions = players.filter(p => !p.position);
      if (playersWithoutPositions.length > 0) {
        console.warn('Found players without positions:',
          playersWithoutPositions.map(p => `${p.name} (${p.id}) - Team ${p.team}`)
        );
      }
    }
  }, [playedCards, arrangedCards, players]);

  // Add a cleanup effect to ensure played cards are properly cleared when the component unmounts
  useEffect(() => {
    // This will run when the component unmounts
    return () => {
      console.log('PlayedCardsArrangement component unmounting, clearing arranged cards');
      setArrangedCards([]);
    };
  }, []);

  // Define the return type for the card info with position data
  type CardWithPositionInfo = PlayedCardInfo & {
    offsetX: number;
    offsetY: number;
    zIndex: number;
    rotation: number;
  };

  // Get cards for the center display
  const getCenterCards = (): CardWithPositionInfo[] => {
    // If we don't have arranged cards but we do have played cards, create a simple arrangement
    if (arrangedCards.length === 0 && playedCards.length > 0) {
      console.log('No arranged cards but we have played cards, creating simple arrangement');

      // Create a simple arrangement based on the played cards
      const simpleArrangement: CardWithPositionInfo[] = playedCards.map((card, index) => {
        const playerId = (card as any).playedBy || 'unknown';
        return {
          card,
          playerId,
          position: ((index % 4) + 1) as PlayerPosition,
          index,
          offsetX: (index - playedCards.length/2) * 25,
          offsetY: -(index - playedCards.length/2) * 15,
          zIndex: index + 1,
          rotation: (index - playedCards.length/2) * 3
        };
      });

      return simpleArrangement;
    }

    if (arrangedCards.length === 0) return [];

    // Create a copy of the arranged cards and sort by index (order played)
    const sortedCards = [...arrangedCards].sort((a, b) => a.index - b.index);

    // Check for duplicate cards and remove them
    const uniqueCards: PlayedCardInfo[] = [];
    const seenCardIds = new Set<string>();
    const seenPlayerIds = new Set<string>();
    const seenPositions = new Set<PlayerPosition>();

    // First, sort by index to ensure we keep the cards in the order they were played
    sortedCards.forEach(card => {
      // Check for duplicate card IDs
      if (!seenCardIds.has(card.card.id)) {
        seenCardIds.add(card.card.id);

        // Also check if we already have a card from this player
        // This prevents the same player from having multiple cards in the played cards area
        if (!seenPlayerIds.has(card.playerId)) {
          seenPlayerIds.add(card.playerId);

          // Also check if we already have a card at this position
          // This prevents multiple cards at the same position
          if (!seenPositions.has(card.position)) {
            seenPositions.add(card.position);
            uniqueCards.push(card);
            console.log(`Added card ${card.card.value} of ${card.card.suit} from player ${card.playerId} at position ${card.position}`);
          } else {
            console.warn(`Position ${card.position} already has a card. Skipping duplicate position card.`);
          }
        } else {
          console.warn(`Player ${card.playerId} already has a card in the played cards. Skipping duplicate player card.`);
        }
      } else {
        console.warn(`Duplicate card detected: ${card.card.value} of ${card.card.suit} (${card.card.id})`);
      }
    });

    // Log the final unique cards
    console.log(`Final unique cards: ${uniqueCards.length} cards`);

    // For the center display, we'll use a different layout based on number of cards
    return uniqueCards.map((cardInfo, index) => {
      const totalCards = uniqueCards.length;

      // If only one card is played, center it
      if (totalCards === 1) {
        return {
          ...cardInfo,
          offsetX: 0,
          offsetY: 0,
          zIndex: 1,
          rotation: 0
        };
      }

      // For multiple cards, create a staggered/overlapping arrangement
      // Calculate offsets based on play order, not position
      let offsetX = 0;
      let offsetY = 0;
      let rotation = 0;

      // Base offset for all cards
      const baseOffsetX = 0;
      const baseOffsetY = 0;

      // Stagger amount per card - more pronounced staggering
      const staggerX = 25;
      const staggerY = 15;

      // Calculate offset based on index (play order)
      offsetX = baseOffsetX + (index - totalCards/2) * staggerX;
      offsetY = baseOffsetY - (index - totalCards/2) * staggerY;
      rotation = (index - totalCards/2) * 3; // Very slight rotation

      return {
        ...cardInfo,
        offsetX,
        offsetY,
        zIndex: index + 1, // First card (index 0) has lowest z-index, last card has highest
        rotation
      };
    });
  };

  if (playedCards.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-50">
      <div className="relative w-fit h-fit sm:w-[400px]  flex flex-col items-center justify-center" style={{ top: "-4rem" }}>
        <div className="absolute -top-8 left-0 right-0 text-center">
          <span className="text-[#E1C760] text-lg font-bold bg-black px-4 py-1 rounded-full border border-[#E1C760]">
            Played Cards ({playedCards.length})
          </span>
        </div>
        {/* Cards in the Center */}
        <div className="relative flex items-center justify-center h-[200px] w-full overflow-visible">
          <AnimatePresence>
            {getCenterCards().map((cardInfo, index) => {
              return (
                <motion.div
                  key={`${cardInfo.card.id}-${index}`}
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{
                    scale: 1,
                    opacity: 1,
                    x: cardInfo.offsetX,
                    y: cardInfo.offsetY,
                    rotate: cardInfo.rotation,
                  }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                  className="absolute w-[var(--card-width-xs)] sm:w-[var(--card-width-sm)] md:w-[var(--card-width-md)] transform-gpu"
                  style={{ zIndex: cardInfo.zIndex }}
                >
                  <Card className="aspect-[2/3] w-full flex items-center justify-center bg-white shadow-lg border-2 border-gray-200 rounded-md">
                    <img
                      src={getCardImagePath(
                        cardInfo.card.value,
                        cardInfo.card.suit
                      )}
                      alt={`${cardInfo.card.value} of ${cardInfo.card.suit}`}
                      className="w-full h-full object-contain"
                    />
                  </Card>

                  {/* Player name indicator below the card */}
                  <div className="absolute -bottom-5 left-0 right-0 text-center">
                    <span className="text-white text-xs bg-black/80 px-1.5 py-0.5 rounded-sm">
                      {players.find(p => p.id === cardInfo.playerId)?.name ||
                       (cardInfo.playerId !== 'unknown' ? `Player ${cardInfo.position}` : 'Unknown Player')}
                    </span>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>

        {/* Your Turn indicator */}
        {playedCards.length < 4 && currentTurn === gameService.getSocketId() && (
          <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-[#E1C760] text-xl font-bold bg-black px-4 py-2 rounded-md border-2 border-[#E1C760] shadow-[0_0_10px_rgba(225,199,96,0.5)]"
            >
              Your Turn
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
}
