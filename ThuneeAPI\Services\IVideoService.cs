using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface IVideoService
    {
        Task RegisterUserAsync(string connectionId, string name);
        Task AddToRoomAsync(string connectionId, string roomId);
        Task RemoveFromRoomAsync(string connectionId, string roomId);
        Task RemoveFromAllRoomsAsync(string connectionId);
        Task<List<VideoUser>> GetOtherParticipantsAsync(string roomId, string excludeConnectionId);
        Task<(bool IsValid, string? Error)> ValidateSignalAsync(string fromConnectionId, string toConnectionId, string roomId);
        Task<string?> GetUserNameAsync(string connectionId);
    }
}
