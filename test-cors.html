<!DOCTYPE html>
<html>
<head>
    <title>Test CORS Configuration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Thunee CORS Configuration Test</h1>
        
        <div class="info">
            <strong>Testing CORS between:</strong><br>
            Frontend: http://localhost:5173 (this page)<br>
            API: http://localhost:5000
        </div>

        <button onclick="testBasicAPI()">Test Basic API Connection</button>
        <button onclick="testSignalRNegotiation()">Test SignalR Negotiation</button>
        <button onclick="clearResults()">Clear Results</button>

        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testBasicAPI() {
            addResult('Testing basic API connection...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    addResult('✅ Basic API connection successful!', 'success');
                    addResult(`Response status: ${response.status}`, 'info');
                } else {
                    addResult(`❌ API returned status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Basic API connection failed: ${error.message}`, 'error');
                console.error('API test error:', error);
            }
        }

        async function testSignalRNegotiation() {
            addResult('Testing SignalR negotiation endpoint...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/gameHub/negotiate?negotiateVersion=1', {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ SignalR negotiation successful!', 'success');
                    addResult(`Response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ SignalR negotiation failed with status: ${response.status}`, 'error');
                    const text = await response.text();
                    addResult(`Response: ${text}`, 'error');
                }
            } catch (error) {
                addResult(`❌ SignalR negotiation failed: ${error.message}`, 'error');
                console.error('SignalR test error:', error);
            }
        }

        // Auto-run tests when page loads
        window.onload = function() {
            addResult('CORS Test Page Loaded', 'info');
            addResult('Click the buttons above to test the connection to your ASP.NET Core API', 'info');
        };
    </script>
</body>
</html>
