"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useGameStore } from "@/store/gameStore";
import { CheckCircle, XCircle } from "lucide-react";

type FourBallResultProps = {
  isOpen: boolean;
  onClose: () => void;
  result: {
    ballType: string;
    option: string;
    targetPlayer: string;
    targetTeam: 1 | 2;
    isValidJordhi: boolean;
    jordhiValue: number;
    jordhiCards?: Array<{ suit: string, value: string }>;
    winningTeam: 1 | 2;
    ballsAwarded: number;
  } | null;
};

export default function FourBallResultDisplay({
  isOpen,
  onClose,
  result
}: FourBallResultProps) {
  const { teamNames } = useGameStore();
  const [countdown, setCountdown] = useState(10);

  // Auto-close after countdown
  useEffect(() => {
    if (!isOpen || !result) return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [isOpen, onClose, result]);

  // Reset countdown when modal opens
  useEffect(() => {
    if (isOpen) {
      setCountdown(10);
    }
  }, [isOpen]);

  if (!isOpen || !result) return null;

  // Function to format suit name
  const formatSuit = (suit: string) => {
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  // Function to get card value name
  const getCardValueName = (value: string) => {
    switch (value) {
      case "9": return "Nine";
      case "10": return "Ten";
      case "J": return "Jack";
      case "Q": return "Queen";
      case "K": return "King";
      case "A": return "Ace";
      default: return value;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && result && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div className="absolute inset-0 bg-black/70" />
          <Card className="relative w-full  bg-black border-2 border-[#E1C760] p-6 z-10">
            <div className="text-center mb-4">
              <h2 className="text-2xl font-bold text-[#E1C760]">4-Ball Result</h2>
              <p className="text-white mt-2">
                {result.option} by {teamNames[result.winningTeam]}
              </p>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg p-4">
                <h3 className="text-[#E1C760] font-semibold mb-2">Jordhi Call Details</h3>
                <p className="text-white">
                  Player: <span className="font-semibold">{result.targetPlayer}</span> ({teamNames[result.targetTeam]})
                </p>
                <p className="text-white">
                  Value: <span className="font-semibold">{result.jordhiValue}</span>
                </p>
                <div className="flex items-center mt-2">
                  <p className="text-white mr-2">Status:</p>
                  {result.isValidJordhi ? (
                    <div className="flex items-center text-green-500">
                      <CheckCircle className="h-5 w-5 mr-1" />
                      <span>Valid Jordhi</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-red-500">
                      <XCircle className="h-5 w-5 mr-1" />
                      <span>Invalid Jordhi</span>
                    </div>
                  )}
                </div>
              </div>

              {result.jordhiCards && result.jordhiCards.length > 0 && (
                <div className="bg-gray-900 rounded-lg p-4">
                  <h3 className="text-[#E1C760] font-semibold mb-2">Jordhi Cards</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {result.jordhiCards.map((card, index) => (
                      <div key={index} className="bg-gray-800 p-2 rounded">
                        {getCardValueName(card.value)} of {formatSuit(card.suit)}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="bg-gray-900 rounded-lg p-4">
                <h3 className="text-[#E1C760] font-semibold mb-2">Result</h3>
                <p className="text-white">
                  {teamNames[result.winningTeam]} wins {result.ballsAwarded} balls
                </p>
                <p className="text-white mt-2">
                  {result.isValidJordhi 
                    ? "The Jordhi call was valid, so the opposing team wins 4 balls."
                    : "The Jordhi call was invalid, so the team that called 4-ball wins 4 balls."}
                </p>
              </div>
            </div>

            <div className="mt-6 flex justify-center">
              <Button 
                onClick={onClose}
                className="bg-[#E1C760] text-black hover:bg-[#c9b052]"
              >
                Continue ({countdown})
              </Button>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
