using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using ThuneeAPI.Data;
using ThuneeAPI.Models;
using BCrypt.Net;

namespace ThuneeAPI.Services
{
    public class AuthService : IAuthService
    {
        private readonly ThuneeDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;

        public AuthService(ThuneeDbContext context, IConfiguration configuration, ILogger<AuthService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username || u.Email == request.Email);

                if (existingUser != null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = existingUser.Username == request.Username 
                            ? "Username already exists" 
                            : "Email already exists"
                    };
                }

                // Hash password
                var passwordHash = BCrypt.Net.BCrypt.HashPassword(request.Password);

                // Generate OTP (using 1234 as requested)
                var otpCode = "1234";
                var otpExpiry = DateTime.UtcNow.AddMinutes(10);

                // Create new user
                var user = new User
                {
                    Username = request.Username,
                    Email = request.Email,
                    PasswordHash = passwordHash,
                    IsVerified = false,
                    OtpCode = otpCode,
                    OtpExpiry = otpExpiry,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"User registered successfully: {request.Username}");

                return new AuthResponse
                {
                    Success = true,
                    Message = "Registration successful. Please verify your account with the OTP sent to your email.",
                    User = MapToUserDto(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration");
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred during registration"
                };
            }
        }

        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username);

                if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Invalid username or password"
                    };
                }

                if (!user.IsVerified)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Account not verified. Please verify your account with OTP."
                    };
                }

                // Update last login
                user.LastLoginAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                var token = GenerateJwtToken(user);

                _logger.LogInformation($"User logged in successfully: {request.Username}");

                return new AuthResponse
                {
                    Success = true,
                    Message = "Login successful",
                    User = MapToUserDto(user),
                    Token = token
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user login");
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred during login"
                };
            }
        }

        public async Task<AuthResponse> VerifyOtpAsync(VerifyOtpRequest request)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username);

                if (user == null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "User not found"
                    };
                }

                if (user.IsVerified)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Account is already verified"
                    };
                }

                if (user.OtpCode != request.OtpCode || user.OtpExpiry < DateTime.UtcNow)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Invalid or expired OTP"
                    };
                }

                // Verify user
                user.IsVerified = true;
                user.OtpCode = null;
                user.OtpExpiry = null;
                user.LastLoginAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var token = GenerateJwtToken(user);

                _logger.LogInformation($"User verified successfully: {request.Username}");

                return new AuthResponse
                {
                    Success = true,
                    Message = "Account verified successfully",
                    User = MapToUserDto(user),
                    Token = token
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during OTP verification");
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred during verification"
                };
            }
        }

        public async Task<AuthResponse> ResendOtpAsync(ResendOtpRequest request)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username);

                if (user == null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "User not found"
                    };
                }

                if (user.IsVerified)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Account is already verified"
                    };
                }

                // Generate new OTP (using 1234 as requested)
                user.OtpCode = "1234";
                user.OtpExpiry = DateTime.UtcNow.AddMinutes(10);

                await _context.SaveChangesAsync();

                _logger.LogInformation($"OTP resent for user: {request.Username}");

                return new AuthResponse
                {
                    Success = true,
                    Message = "OTP resent successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during OTP resend");
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred while resending OTP"
                };
            }
        }

        public async Task<UserDto?> GetUserByIdAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            return user != null ? MapToUserDto(user) : null;
        }

        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username);
            return user != null ? MapToUserDto(user) : null;
        }

        public string GenerateJwtToken(User user)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"] ?? "YourSuperSecretKeyThatIsAtLeast32CharactersLong!";
            var issuer = jwtSettings["Issuer"] ?? "ThuneeAPI";
            var audience = jwtSettings["Audience"] ?? "ThuneeClient";

            var key = Encoding.ASCII.GetBytes(secretKey);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Name, user.Username),
                    new Claim(ClaimTypes.Email, user.Email)
                }),
                Expires = DateTime.UtcNow.AddDays(7),
                Issuer = issuer,
                Audience = audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public bool ValidateJwtToken(string token)
        {
            try
            {
                var jwtSettings = _configuration.GetSection("JwtSettings");
                var secretKey = jwtSettings["SecretKey"] ?? "YourSuperSecretKeyThatIsAtLeast32CharactersLong!";
                
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(secretKey);
                
                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return true;
            }
            catch
            {
                return false;
            }
        }

        private UserDto MapToUserDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt
            };
        }
    }
}
