using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public interface ITimeframeService
    {
        void InitTimeframeVoting(Lobby lobby);
        bool RecordTimeframeVote(Lobby lobby, string playerId, int timeframe);
        void UpdateVoteResults(Lobby lobby);
        bool AllPlayersVoted(Lobby lobby);
        int? DetermineSelectedTimeframe(Lobby lobby);
        TimeframeVoting? GetVotingState(Lobby lobby);
        void ResetTimeframeVoting(Lobby lobby);
        TurnTimerState InitTurnTimer(Lobby lobby, string playerId);
        Task UpdateTurnTimerAsync(Lobby lobby);
        void StopTurnTimer(Lobby lobby);
    }
}
