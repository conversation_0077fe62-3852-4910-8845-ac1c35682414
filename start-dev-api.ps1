# PowerShell script to start Thunee ASP.NET Core API in Development Mode
Write-Host "Starting Thunee ASP.NET Core API in Development Mode..." -ForegroundColor Green

# Navigate to the ThuneeAPI directory
$projectPath = "C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE\ThuneeAPI"
Set-Location $projectPath

# Set environment to Development
$env:ASPNETCORE_ENVIRONMENT = "Development"

Write-Host "Environment: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor Cyan
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

Write-Host "Building project..." -ForegroundColor Yellow
$buildResult = & dotnet build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting API on http://localhost:5000..." -ForegroundColor Green
Write-Host ""
Write-Host "Available URLs:" -ForegroundColor Cyan
Write-Host "  Main API: http://localhost:5000" -ForegroundColor White
Write-Host "  Test Client: http://localhost:5000/test-client.html" -ForegroundColor White
Write-Host "  Config: http://localhost:5000/config.js" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Run with explicit development URL
& dotnet run --urls "http://localhost:5000"
