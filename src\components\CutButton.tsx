"use client";
import { useState, useEffect } from "react";
import { useGameStore } from "@/store/gameStore";
import gameService from "@/services/gameService";
import { Button } from "./ui/button";
import { getPositionToLeft } from "@/utils/playerPositionUtils";

interface CutButtonProps {
  onCutComplete: () => void;
}

export default function CutButton({ onCutComplete }: CutButtonProps) {
  const { isDealer, players } = useGameStore();
  const [isCutting, setIsCutting] = useState(false);

  // Debug: Log players when component renders
  useEffect(() => {
    console.log("CutButton rendered with players:", players.map(p => `${p.name} (${p.id}) - Team ${p.team}`));
  }, [players]);

  // Only the dealer should see this button
  if (!isDealer) {
    return null;
  }

  const handleGiveCut = async () => {
    try {
      setIsCutting(true);
      console.log("Giving opponent to cut...");

      // Debug: Log all players and their teams
      console.log("All players:", players.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // Find the player to the left of the dealer (next player)
      const currentPlayerId = gameService.getSocketId();
      const currentPlayer = players.find(p => p.id === currentPlayerId);

      if (!currentPlayer) {
        console.error("Current player not found in players list");
        setIsCutting(false);
        return;
      }

      console.log(`Current player: ${currentPlayer.name} (${currentPlayer.id}) - Team ${currentPlayer.team}`);

      // If we don't have team information, use a fallback approach
      if (!currentPlayer.team) {
        console.log("No team information available, using fallback approach");
        // Just pick any other player that's not the current player
        const otherPlayers = players.filter(p => p.id !== currentPlayerId);
        if (otherPlayers.length === 0) {
          console.error("No other players found");
          setIsCutting(false);
          return;
        }

        // Pick the first other player
        const nextPlayer = otherPlayers[0];
        console.log(`Selected player for cut: ${nextPlayer.name} (${nextPlayer.id})`);

        // Send cut request to server
        await gameService.sendGameAction("request_cut", {
          playerId: nextPlayer.id
        });

        setIsCutting(false);
        return;
      }

      // Get the current player's team
      const dealerTeam = currentPlayer.team;

      // In Thunee, the player to the left of the dealer should be from the opposite team
      // Find all players from the opposite team
      const oppositeTeamPlayers = players.filter(p => p.team !== dealerTeam);

      if (oppositeTeamPlayers.length === 0) {
        console.error("No players from opposite team found");
        // Fallback: just pick any other player
        const otherPlayers = players.filter(p => p.id !== currentPlayerId);
        if (otherPlayers.length === 0) {
          console.error("No other players found");
          setIsCutting(false);
          return;
        }

        // Pick the first other player
        const nextPlayer = otherPlayers[0];
        console.log(`Fallback: Selected player for cut: ${nextPlayer.name} (${nextPlayer.id})`);

        // Send cut request to server
        await gameService.sendGameAction("request_cut", {
          playerId: nextPlayer.id
        });

        setIsCutting(false);
        return;
      }

      console.log(`Found ${oppositeTeamPlayers.length} players from opposite team:`,
        oppositeTeamPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // Get the player to the left of the dealer (clockwise)
      // In Thunee with fixed positions, the dealer is at position 3 (bottom left)
      // The player to the left of the dealer is at position 4 (bottom right)

      // Get the dealer's position (should be 3)
      const dealerPosition = currentPlayer.position;

      // Get the position to the left of the dealer
      const leftPosition = getPositionToLeft(dealerPosition);

      console.log(`Dealer position: ${dealerPosition}, Position to the left: ${leftPosition}`);

      // Find the player at the left position
      const playerToLeft = players.find(p => p.position === leftPosition);

      let nextPlayer;

      if (playerToLeft) {
        nextPlayer = playerToLeft;
        console.log(`Selected player to the left of dealer by position: ${nextPlayer.name} (${nextPlayer.id}) - Team ${nextPlayer.team} - Position ${nextPlayer.position}`);
      } else {
        console.error(`No player found at position ${leftPosition}`);
        // Fallback to the old method
        const currentPlayerIndex = players.findIndex(p => p.id === currentPlayerId);
        const nextPlayerIndex = (currentPlayerIndex + 1) % players.length;
        nextPlayer = players[nextPlayerIndex];
        console.log(`Fallback: Selected player to the left of dealer by index: ${nextPlayer.name} (${nextPlayer.id}) - Team ${nextPlayer.team}`);
      }

      console.log(`Dealer team: ${dealerTeam}, Next player team: ${nextPlayer.team}`);

      console.log(`Requesting cut from player ${nextPlayer.name} (${nextPlayer.id})`);

      // Send cut request to server
      await gameService.sendGameAction("request_cut", {
        playerId: nextPlayer.id
      });

      // Don't complete the cut process yet - wait for the player to cut
      // The server will send a cut_complete event when the player has made their choice
      setIsCutting(false);
    } catch (error) {
      console.error("Error requesting cut:", error);
      setIsCutting(false);
    }
  };

  return (
    <div className="fixed bottom-24 left-0 right-0 flex justify-center items-center z-50">
      <Button
        onClick={handleGiveCut}
        disabled={isCutting}
        className="bg-[#E1C760] text-black px-10 py-4 rounded-md text-2xl font-bold shadow-lg border-4 border-black hover:bg-[#E1C760]/90 transition-colors"
      >
        {isCutting ? "WAITING..." : "GIVE OPPONENT TO CUT"}
      </Button>
    </div>
  );
}
